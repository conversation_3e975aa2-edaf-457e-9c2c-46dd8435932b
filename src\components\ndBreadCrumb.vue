<template>
  <div class="nd-bread-crumb-box">
    <div class="image el-icon-location-outline" />
    <div class="name">
      {{ title }}
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      title: "",
    };
  },
  created() {
    var paramObject = this.getRequestParams();
    let menu = decodeURIComponent(paramObject.np)
    // console.log(menu);
    for (let i = 0; i < menu.length; i++) {
      if (menu[i] === ',') {
        this.title += ' ' + '>' + ' '
      } else {
        this.title += menu[i]
      }
    }
  },
  methods: {
    getRequestParams() {
      let url = location.href;
      let requestParams = {};
      if (url.indexOf("?") !== -1) {
        let str = url.substring(url.indexOf("?") + 1);
        let strs = str.split("&");
        for (let i = 0; i < strs.length; i++) {
          requestParams[strs[i].split("=")[0]] = strs[i].split("=")[1];
        }
      }
      return requestParams;
    },
    // 【jsp】获得服务器路径
    getServerPath() {
      if (typeof getContextPath === "function") {
        console.log("获得服务器路径:" + getContextPath());
        return getContextPath();
      } else {
        return "/sz_product_new";
      }
    },
    // 获得iframe的src的params
    getParams() {
      var search = location.search;
      var params1 = search.substring(search.indexOf('?') + 1, search.length).split("&");
      var params2 = {};
      for (var i = 0; i < params1.length; i++) {
        var v = params1[i] || "";
        if (v && v.indexOf("=") > -1) {
          var t = (params1[i] || "").split("=") || [];
          if (t.length == 2) {
            params2[t[0]] = t[1];
          }
        }
      }
      return params2;
    },
  }
}
</script>

<style lang='scss' scoped>
.nd-bread-crumb-box {
  width: 100%;
  height: 30px;
  background-color: #f6faff;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1px solid #dce6f3;

  .image {
    margin-left: 12px;
    margin-right: 6px;
    color: #0098ff;
  }

  .name {
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    color: #555555;
    font-family: 'Microsoft YaHei';
  }
}
</style>
