<template>
  <div class="main-cont">
    <div v-if="listData.length !== 0" class="list-content">
      <div class="list-item" @click.stop="goDetail(item)" v-for="(item, index) in listData" :key="index">
        <div class="img">
          <img src="@/img/zzggListIcon.png" alt="">
        </div>
        <div class="item-top">
          <div class="title">{{ item.projectName }}</div>
          <div class="content">
            <div class="content_timeann">公告日期：{{ item.publicTime || '--' }}</div>
            <div class="content_timeend">终止日期：{{ item.endTime || '--' }}</div>
            <div class="content_detail">
              <p>查看详情</p>
              <img src="@/img/toRight.png" alt="">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="list-content">
      <empty :boxHeight="300"></empty>
    </div>
    <!-- 分页器 -->
    <div class="pagination">
      <nd-pagination :page-size="pager.pageSize" layout="total, prev, pager, next, jumper" :total="pager.total"
        :total-page="totalPage" :current-page="pager.pageNo" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
      <div class="current-total">
        当前页共{{ listData.length }}条
      </div>
    </div>
  </div>
</template>

<script>
import ndPagination from '@/components/ndPagination.vue';
import empty from '@/views/empty/index.vue';

export default {
  name: 'list',
  components: {
    ndPagination,
    empty,
  },
  props: {
    showTab: {
      type: String,
      default: '1',
    },
    requestUrl: {
      type: String,
      default: '',
    },
    breadcrumbName: {
      type: String,
      default: '资讯详情',
    },
    postParams: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      listData: [],
      pager: {
        pageNo: 1, // 当前页数
        pageSize: 10, // 当前页条数
        total: 0, // 总条目数
        pageSizes: [10, 30, 50, 100],
      },
      totalPage: 1,
    };
  },
  watch: {
    //监听info对象
    requestUrl: {
      handler(newVal, oldVal) {
        //监听info对象变化
        console.log("newVal", newVal)
        this.pager.pageNo = 1
        this.getData(newVal)
      },
      deep: true, //深度监听
    },
    postParams: {
      handler(newVal, oldVal) {
        this.pager.pageNo = 1
        this.getData()
      },
      deep: true, //深度监听
    }
  },
  mounted() {
    this.pager.pageNo = 1
    this.getData();
  },
  methods: {
    getData() {
      this.$ajax({
        url: this.$props.requestUrl,
        method: 'post',
        data: {
          ...this.postParams,
          page: this.pager.pageNo,
          size: this.pager.pageSize
        }
      }).then((res) => {
        if (res.status === 200) {
          console.log('rewssssssss', res);

          try {
            this.listData = res.data.data.records;
            this.pager.total = res.data.data.total;
            this.totalPage = Math.ceil(this.pager.total / this.pager.pageSize);
          } catch (error) {
            console.error('数据格式有误');
          }
        }
      });
    },

    goDetail(item) {
      //  终止公告 详情跳转处理
      console.log(item, '详情');
      if (item.dataSource == 2) {
        return window.open(item.projectLink);
      }

      let query = [];
      query = [{ name: '公示公告', route: 'publicInformationView' }];
      if (window.location.origin == 'http://localhost:8080') {
        window.open(
          window.location.origin +
          `/#/endDetail?id=${item.projectId}&route=${JSON.stringify(query)}&homeType=2`,
        );
      } else {
        window.open(
          window.location.origin +
          window.location.pathname +
          `#/endDetail?id=${item.projectId}&route=${JSON.stringify(query)}&homeType=2`,
        );
      }
    },

    //  分页器函数
    handleCurrentChange(e) {
      this.pager.pageNo = e;
      this.getData()
    },
    handleSizeChange(e) {
      this.pager.pageSize = e;
      this.pager.pageNo = 1;
      this.getData()
    },

    // 获取年份
    getYear(time) {
      if (!time) return '';
      return new Date(time).getFullYear();
    },
    // 获取月份日期
    getDate(time) {
      if (!time) return '';
      let month = new Date(time).getMonth() + 1 + '';
      month.padStart(2, '0');
      let day = new Date(time).getDate() + '';
      day.padStart(2, '0');
      return month + '-' + day;
    },
  },
};
</script>
<style lang="scss" scoped>
.main-cont {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 400px;
}

.list-content {
  width: 1300px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e5e5;
  margin-top: 21px;
  padding: 10px 0;

  .list-item {
    cursor: pointer;
    padding: 20px;
    border-bottom: 1px solid #E5E5E5;
    display: flex;

    &:hover {
      box-shadow: 0px 0px 10px 0px rgba(32, 32, 32, 0.1);

      .item-top {
        // box-shadow: 0 4px 10px #ccc;
        color: #ed911f;
        transition: 0.3s;
      }

      .title {
        font-size: 20px;
        color: #ed911f;
      }

      .content {
        font-size: 16px;
        color: #ed911f;
      }
    }
  }

  .img {
    width: 18px;
    line-height: 46px;

    img {
      width: 100%;
    }
  }

  .item-top {
    flex: 1;
    padding-left: 14px;

    .title {
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      line-height: 36px;
      display: flex;
      align-items: center;
    }

    .content {
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 24px;
      padding-top: 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .content_timeann {
        width: 260px;
      }

      .content_timeend {
        flex: 1;
      }

      .content_detail {
        display: flex;
        align-items: center;
        color: #ED911F;

        img {
          height: 8px;
          margin-left: 10px;
        }
      }
    }
  }
}

.pagination {
  width: 100%;
  margin-top: 14px;
  display: flex;
  align-items: center;
  justify-content: center;

  .current-total {
    font-weight: 400;
    color: #606266;
    font-size: 13px;
  }

  ::v-deep .nd-pagination-box {
    width: auto;
    margin-right: 10px;
  }

  :v-deep .el-pagination button:disabled {
    background: none;
  }

  ::v-deep .el-pagination .btn-next,
  .el-pagination .btn-prev {
    background: none;
  }

  ::v-deep .el-pagination .btn-prev:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pagination .btn-next:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pager li {
    background: none;
  }

  ::v-deep .el-pager li.active {
    color: #fff;
    background: #f2ae0f;
  }

  ::v-deep .el-pager li.active:hover {
    color: #fff;
  }

  ::v-deep .el-pager li:hover {
    color: #f2ae0f;
  }
}
</style>