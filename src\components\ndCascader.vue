<template>
  <div class="nd-cascader-box" :style="{ width: width }">
    <el-cascader
      v-bind="$attrs"
      :props="caprops"
      :placeholder="placeholder"
      size="mini"
      v-on="$listeners"
    />
  </div>
</template>
  
  <script>
export default {
  props: {
    width: {
      type: String,
      default: "220px",
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    caprops: {},
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {},
};
</script>
  
<style lang="scss" scoped>
::v-deep .el-cascader {
  width: 100% !important;
  border-radius: 0px;
  border: 1px solid #dcdfe6;
  padding-top: 0px;
  padding-bottom: 0px;
  font-size: 12px;
  background-color: transparent;
  .el-input__inner {
    border: none;
  }
}
</style>