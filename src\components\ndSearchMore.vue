<template>
  <div class="nd-search-more-box">
    <el-popover v-if="vertical" ref="popover" @hide="hideHandler" placement="bottom-start" trigger="click" popper-class="nd-search-more-popper">
      <div class="nd-search-more-popper-content">
        <el-scrollbar :style="{ width: scrollWidth, height: '100%', '--maxHeight': scrollHeight }">
          <div class="search-item-box">
            <nd-search-more-item v-if="showSaveSearchData" :width="leftTitleWidth" title="常用条件">
              <nd-select v-model="projectItem" placeholder="请选择" popper-class="eloption" :width="itemInputWidth" :popper-append-to-body="true" @change="sendSearchData($event)">
                <el-option :value="null">
                  请选择
                </el-option>
                <el-option v-for="item in projectItems" :key="item.id" :label="item.queryName" :value="item.id">
                  <div class="search-project-item">
                    <span>{{ item.queryName }}</span>
                    <i class="el-icon-close" @click.stop="deleteProjectItem(item)" />
                  </div>
                </el-option>
              </nd-select>
            </nd-search-more-item>
            <slot />
          </div>
        </el-scrollbar>
        <div class="nd-search-more-popper-foot">
          <slot name="foot" />
        </div>
      </div>
      <nd-button slot="reference" :disabled="searchDisabled">
        <div class="nd-search-more-reference-box">
          <i class="el-icon-search nd-search-more-reference-icon" />
          <span class="text">查&nbsp;询</span>
          <i class="el-icon-arrow-down nd-search-more-reference-icon" />
        </div>
      </nd-button>
    </el-popover>
    <div v-if="!vertical" class="nd-search-more-horizontal-box">
      <slot />
      <div class="nd-search-more-horizontal-popper-foot">
        <slot name="foot" />
      </div>
    </div>
    <nd-search-more-save-dialog ref="saveProjectDialogRef" @saveSearchData="saveSearchData" />
  </div>
</template>

<script>
import ndButton from "./ndButton.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndSelect from "./ndSelect.vue";
import ndSearchMoreSaveDialog from "@/components/ndSearchMoreSaveDialog"
export default {
  components: {
    ndButton,
    ndSearchMoreItem,
    ndSelect,
    ndSearchMoreSaveDialog,
  },
  props: {
    onlyIdentify: {//唯一标识暂不用
      type: String,
      default: ""
    },
    searchDisabled: {//是否禁用查询按钮
      type: Boolean,
      default: false
    },
    modelType: {//是什么模块进入
      type: String,
      default: ""
    },
    getSearchData: {
      type: Object,
      default: () => { },
    },
    // 排除项
    exclusionData: {
      type: Array,
      default: () => [],
    },
    // 
    caseWidth: {
      type: String,
      default: '58px'
    },
    // item名称宽度
    leftTitleWidth: {
      type: String,
      default: '58px'
    },
    // input宽度
    itemInputWidth: {
      type: String,
      default: '100%'
    },
    // 是否显示保存查询条件区域
    showSaveSearchData: {
      type: Boolean,
      default: false,
    },
    // 滚动区域宽度
    scrollWidth: {
      type: String,
      default: '100%'
    },
    // 滚动区域高度
    scrollHeight: {
      type: String,
      default: '300px'
    },
    vertical: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      projectItem: "", // 方案id
      projectItems: [], // 方案
      projectName: "", // 方案名称
      isDefault: "0", // 是否默认方案
      reportQuerySaveExcludeReqList: [],
      stay: false,
    };
  },
  watch: {
    exclusionData: {
      handler(newValue) {
        this.reportQuerySaveExcludeReqList = newValue
      }
    }
  },
  mounted() {
    if (this.showSaveSearchData) {
      this.getProjectItem()
    }
  },
  methods: {
    open() {
      this.lock();
      this.$refs.popover.doShow();
    },
    close() {
      this.$refs.popover.doClose();
    },
    lock() {
      this.stay = true;
    },
    unlock() {
      setTimeout(() => {
        this.stay = false;
      }, 500);
    },
    hideHandler() {
      if (this.stay) {
        this.open();
        this.unlock();
      }
    },
    // 获取方案
    getProjectItem() {
      if (this.showSaveSearchData) {
        let url = "/tjCwKmMoney/getNameQueryConditions.do"
        if (this.modelType === "subjectStatistics") {
          url = "/tjSubjectInfoQuerySave/getNameQueryConditions.do"
        } else {
          url = "/tjCwKmMoney/getNameQueryConditions.do"
        }
        this.$ajax({
          url: url,
          method: "post",
        }).then((res) => {
          console.log(res, "getNameQueryConditions");
          if (res.data.code === 0) {
            if (res.data.data.length != 0) {
              this.projectItems = res.data.data
              res.data.data.forEach((element) => {
                if (element.isDefault === 1) {
                  console.log(element, "/tjCwKmMoney/getNameQueryConditions.do");
                  this.projectItem = element.id
                  this.isDefault = String(element.isDefault)
                  this.projectName = String(element.queryName)
                  this.sendSearchData(element.id)
                }
              })
            } else {
              this.projectItems = []
              this.$emit('setSearchData', false)
            }
          } else {
            this.projectItems = []
            this.$emit('setSearchData', false)
          }
        }).catch(() => {
          this.projectItems = []
          this.$emit('setSearchData', false)
        });
      }
    },
    // 删除方案
    deleteProjectItem(item) {
      this.$confirm('确定删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.showSaveSearchData) {
          let url = "/tjCwKmMoney/deleteQuery.do"
          if (this.modelType === "subjectStatistics") {
            url = "/tjSubjectInfoQuerySave/deleteQuery.do"
          } else {
            url = "/tjCwKmMoney/deleteQuery.do"
          }
          let params = {
            id: item.id
          }
          this.$ajax({
            url: url,
            method: "post",
            data: params
          }).then((res) => {
            if (res.data.code === 0) {
              if (item.id == this.projectItem) {
                this.projectItem = ""
              }
              this.$message.success("查询方案删除成功")
              this.getProjectItem()
            } else {
              this.$message.warning(res.data.msg)
            }
          });
        }
        this.open()
      }).catch(() => {
        this.open()
      });
    },
    // 获取查询条件
    sendSearchData(id) {
      if (this.showSaveSearchData) {
        if (id) {
          let params = {
            id: id
          }
          this.projectItems.forEach(element => {
            if (element.id == id) {
              this.projectItem = element.id
              this.isDefault = String(element.isDefault)
              this.projectName = String(element.queryName)
              let url = "/tjCwKmMoney/getOneQueryConditions.do"
              if (this.modelType === "subjectStatistics") {
                url = "/tjSubjectInfoQuerySave/getOneQueryConditions.do"
              } else {
                url = "/tjCwKmMoney/getOneQueryConditions.do"
              }
              this.$ajax({
                url: url,
                method: "post",
                data: params
              }).then((res) => {
                if (res.data.code === 0) {
                  this.$emit('setSearchData', res.data.data)
                } else {
                  this.$message.warning(res.data.msg)
                }
              });
            }
          })
        } else {
          this.isDefault = ""
          this.projectName = ""
          this.$emit('setSearchData', false)
        }
      }
    },
    // 打开保存查询条件对话框
    openSaveProjectDialog() {
      this.$refs.saveProjectDialogRef.open(this.projectName, this.isDefault)
    },
    // 保存查询条件
    saveSearchData(...val) {
      if (this.showSaveSearchData) {
        let search = JSON.parse(JSON.stringify(this.getSearchData));
        let url = "/tjCwKmMoney/saveQueryCondition.do"
        if (this.modelType === "subjectStatistics") {
          url = "/tjSubjectInfoQuerySave/queryConditionSave.do"
          this.$delete(search, 'villageDelete')
        } else {
          url = "/tjCwKmMoney/saveQueryCondition.do"
        }
        // console.log(encodeURIComponent(val[0]),"encodeURIComponent(val[0])encodeURIComponent(val[0])encodeURIComponent(val[0])");
        // this.$set(search, 'queryName',val[0]);
        if (this.projectItem != "") {
          Object.assign(search, {
            id: this.projectItem
          })
          // this.$set(search, 'id', this.projectItem);
        }
        // this.$set(search, 'isDefault', val[1]);
        // this.$set(search, 'reportQuerySaveExcludeReqList', this.reportQuerySaveExcludeReqList);
        Object.assign(search, {
          queryName: val[0],
          isDefault: val[1],
          reportQuerySaveExcludeReqList: this.reportQuerySaveExcludeReqList
        })
        this.$delete(search, 'reportId')
        this.$delete(search, 'yearAndMonth')
        this.$delete(search, 'startMonth')
        this.$delete(search, 'endMonth')
        this.$delete(search, 'startDate')
        this.$delete(search, 'endDate')
        this.$delete(search, 'ruleOutARegionAllPath')
        this.$delete(search, 'suitOrganizationType')
        this.$ajax({
          url: url,
          method: "post",
          data: search
        }).then((res) => {
          if (res.data.code === 0) {
            this.isClickProjectItem = ""
            this.$message.success(res.data.data)
            this.getProjectItem()
          } else {
            this.$message.warning(res.data.msg)
          }
        });
      }
    },
  }
};
</script>

<style lang="scss">
.eloption {
  ::v-deep .el-select-dropdown__list {
    max-height: 200px;
  }
}


.nd-search-more-popper-content {
  .el-scrollbar__wrap {
    max-height: var(--maxHeight);
  }

  .el-scrollbar__bar.is-vertical {
    width: 6px !important;
  }
}

.nd-search-more-popper {
  padding: 2px;
  min-width: 125px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.3);
  border-radius: 5px;
}
</style>
<style scoped lang="scss">
.search-item-box {
  margin-right: 18px;

}

.search-project-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.el-icon-close {
  color: #0098ff;
  font-weight: bold;
  cursor: pointer;
}

.nd-search-more-popper {
  .nd-search-more-popper-content {
    padding: 10px 0 10px 10px;
    font-size: 12px;
  }

  .nd-search-more-popper-foot {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
  }
}

.nd-search-more-box {
  width: auto;
  height: auto;
  display: inline-block;

  .nd-search-more-reference-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;

    .text {
      margin-right: 4px;
      margin-left: 4px;
    }
  }
}

.nd-button-box+.nd-search-more-box {
  margin-left: 10px;
}

.nd-search-more-horizontal-box {
  width: auto;
  height: auto;
  display: flex;

  .nd-search-more-horizontal-popper-foot {
    margin-left: 10px;
  }
}
</style>