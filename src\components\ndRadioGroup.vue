<template>
  <div class="nd-radio-group-box">
    <el-radio-group v-bind="$attrs" v-on="$listeners">
      <slot />
    </el-radio-group>
  </div>
</template>
<script>
export default {
  props: {
    width: {
      type: String,
      default: "220px",
    },
  },
  data() {
    return {
    };
  },
  mounted() {
  },
  methods: {
  },
};
</script>
<style lang="scss" scoped>
.nd-radio-group-box {
  height: auto;
  width: auto;
}
</style>
