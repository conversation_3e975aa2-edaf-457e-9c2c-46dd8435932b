<template>
  <div>
    <nd-dialog
      ref="multifunctionExportRef"
      class="dialog"
      :width="'454px'"
      :height="'204px'"
      append-to-body
      :before-close="close"
      title="导出表样选择"
      center
    >
      <nd-radio-group v-model="exoprtname" class="multifunction-export-box">
        <div class="multifunction-export-item">
          <nd-radio :label="1">
            横向导出
          </nd-radio>
          <div class="multifunction-export-item-text">
            左侧列展示地区，统计数据横向展示，同主界面横向视图
          </div>
        </div>
        <div class="multifunction-export-item">
          <nd-radio :label="2">
            纵向导出
          </nd-radio>
          <div class="multifunction-export-item-text">
            统计数据项纵向展示，地区横向排列展示
          </div>
        </div>
        <div class="multifunction-export-item">
          <nd-radio :label="3">
            按行政级别导出
          </nd-radio>
          <div class="multifunction-export-item-text">
            与“横向导出”样式一致，仅展示到地区，不含单位/组织
          </div>
        </div>
        <div v-if="zcfzFlag" class="multifunction-export-item">
          <nd-radio :label="4">
            资产负债汇总表
          </nd-radio>
          <div class="multifunction-export-item-text">
            按资产负债表样导出汇总数，不显示具体地区/单位
          </div>
        </div>
      </nd-radio-group>
      <template #footer>
        <nd-button type="primary" @click="trueClick()">
          确定
        </nd-button>
        <nd-button slot="reference" @click="close">
          取消
        </nd-button>
      </template>
    </nd-dialog>
  </div>
</template>

<script>
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndRadioGroup from "@/components/ndRadioGroup.vue"
import ndRadio from "@/components/ndRadio.vue";
export default {
    name: "NdbMultifunctionExport",
    components: {
        ndDialog,
        ndButton,
        ndRadioGroup,
        ndRadio,
    },
    props: {
        //默认选中的值
        selectedDefault: {
            type: Number,
            default: 0,
        },
        //是否显示资产负债汇总表
        zcfzFlag: {
            type:Boolean,
            default: false
        },
    },

    data() {
        return {
            exoprtname:'', //选中的导出名称
        };
    },

    mounted() {
       
    },

    methods: {
        //点击确定导出
        trueClick() {
            this.$emit('getExportName', this.exoprtname);
            this.close();
        },
        //打开弹框
        open() {
            this.exoprtname = this.selectedDefault;
            this.$refs.multifunctionExportRef.open();
        },
        //关闭弹框
        close() {
            this.exoprtname = '';
            this.$refs.multifunctionExportRef.close();
        },
    },
};
</script>

<style lang="scss" scoped>
.multifunction-export-box {
    padding: 19px 27px;
    display: flex;
    flex-direction: column;

    .multifunction-export-item {
        margin-bottom: 17px;
        line-height:17px;

        .multifunction-export-item-text {
            font-size: 12px;
            margin-top:5px;
            padding-left: 24px;
            color: #999;
        }

    }

    ::v-deep .el-radio__label {
        font-size: 12px;
        color: #555;
    }

    .multifunction-export-item:last-child {
        margin-bottom: 0;
    }
}
</style>