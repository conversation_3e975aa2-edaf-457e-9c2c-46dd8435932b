<template>
  <div>
    <nd-dialog
      ref="addLoading"
      width="650px"
      height="200px"
      title="导入数据处理中"
      append-to-body
      :before-close="closeLoading"
      center
    >
      <div class="content">
        <div><img src="@/img/loading.png" alt=""></div>
        <div class="content-one">
          导入数据处理中...
        </div>
        <div class="content-two">
          说明：数据导入过程中，可关闭界面，后台会继续处理，导入结果请通过导入界面【查看上次导入结果】查看
        </div>
      </div>
      <template #footer>
        <nd-button type="normal" @click="close">
          关闭
        </nd-button>
      </template>
    </nd-dialog>
  </div>
</template>

<script>
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndTable from "@/components/ndTable.vue";
import ndInput from "@/components/ndInput.vue";
export default {
  components: {
    ndDialog,
    ndButton,
    ndTable,
    ndInput,
  },
  data() {
    return {};
  },

  mounted() {},

  methods: {
    //打开弹框
    openLoading() {
      this.$refs.addLoading.open();
    },
    //关闭弹框
    closeLoading() {
      this.$refs.addLoading.close();
       this.$emit('fatherMethod');
    },
    // 关闭弹框回到导入界面
    close() {
      this.closeLoading();
    },
  },
};
</script>

<style lang="scss" scoped>
img {
  padding-left: 280px;
  padding-top: 50px;
  margin-bottom: 6px;
}
.content-one {
  height: 14px;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #555555;
  line-height: 30px;
  margin-bottom: 9px;
  padding-left: 243px;
}
.content-two {
  height: 12px;
  font-size: 12px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #999999;
  line-height: 30px;
  padding-left: 17px;
}
</style>