<template>
  <div>123</div>
</template>

<script>
export default {
  mounted() {
    this.getData();
  },
  methods: {
    // 获取数据
    getData() {
      this.$ajax({
        url: "/if/xwdt/",
        method: "get",
        serverName: "nd-ss",
      })
        .then((response) => {
          if (response.status === 200) {
            console.log(response.data);
            // let data = r.data;
            // let a = data.substring(0, data.lastIndexOf(","));
            // let b = data.substring(data.lastIndexOf(",") + 1);
            // let newData = a + b;
            // // console.log(newData,"newData");
            // let c = eval("(" + newData + ")");
            // // console.log(c);
            // // console.log(c.length,"length");
            // pageList.listData = [];
            // pageList.listItem = []; //清空页面显示数据
            // pageData.pageArr = []; //清空页数下拉数据
            // pageList.listData = c;
            // pageData.totalPage = Math.ceil(pageList.listData.length / 20);
            // let last = parseInt(pageList.listData.length / 20);
            // console.log(last, "log");
            // // 下拉页数
            // for (let m = 1; m <= pageData.totalPage; m++) {
            //   pageData.pageArr.push(m);
            // }
            // if (pageNum <= last) {
            //   for (let i = (pageNum - 1) * 20; i < pageNum * 20; i++) {
            //     pageList.listItem.push(pageList.listData[i]);
            //   }
            // } else {
            //   // 最后一页的列表数据
            //   for (let i = last * 20; i < pageList.listData.length; i++) {
            //     pageList.listItem.push(pageList.listData[i]);
            //   }
            // }
            // pageData.n++;
            // console.log(pageList.listItem, "pageList.listItem");
          }
        })
        .catch((err) => {
          // console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped></style>
