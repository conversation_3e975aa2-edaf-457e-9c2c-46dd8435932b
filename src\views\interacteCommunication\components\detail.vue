<template>
  <div class="dialog" v-if="show">
    <div class="dialog-box">
      <div class="del" @click="close">
        <i class="el-icon-close"></i>
      </div>
      <div class="title">查看详情</div>
      <div class="content-box">
        <div class="subheading">处理结果</div>
        <el-descriptions :column="2" border style="margin-bottom: 30px">
          <el-descriptions-item span="2">
            <template slot="label"> 处理结果 </template>
            <div style="min-width: 399px">{{ data.replyContent }}</div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 处理人 </template>
            <div style="min-width: 399px">{{ data.replyName }}</div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 处理时间 </template>
            <div style="min-width: 399px">{{ data.replyTime }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <div class="subheading">咨询内容</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item span="2">
            <template slot="label"> 项目名称 </template>
            <div style="min-width: 399px">{{ data.projectName }}</div>
          </el-descriptions-item>
          <template v-if="data.type === '3'">
            <el-descriptions-item>
              <template slot="label"> 问题类型 </template>
              <div style="min-width: 399px">{{ data.typeName }}</div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 咨询单位 </template>
              <div style="min-width: 399px">{{ data.chooseDeptName }}</div>
            </el-descriptions-item>
          </template>
          <template v-if="data.type !== '3'">
            <el-descriptions-item span="2">
              <template slot="label"> 问题类型 </template>
              <div style="min-width: 399px">{{ data.typeName }}</div>
            </el-descriptions-item>
          </template>
          <el-descriptions-item span="2">
            <template slot="label"> 咨询内容 </template>
            <div style="min-width: 399px">{{ data.questionerContent }}</div>
          </el-descriptions-item>
          <el-descriptions-item span="2">
            <template slot="label"> 附件 </template>
            <div style="min-width: 399px">
              <div
                v-for="(item, index) in data.fileList"
                :key="index"
                style="
                  font-family: Microsoft YaHei;
                  font-weight: 400;
                  font-size: 14px;
                  color: #ed911f;
                  cursor: pointer;
                "
                @click="viewFile(item)"
              >
                {{ item.fileName }}
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 联系人 </template>
            <div style="min-width: 399px">{{ data.questionerName }}</div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 联系电话 </template>
            <div style="min-width: 399px">{{ data.questionerTelephone }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="bottom-box">
        <div class="button" @click="close">关&nbsp;闭</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      show: false,
      data: {},
      fileLest: [],
    };
  },
  methods: {
    /**
     * 点击查看附件
     */
    viewFile(e) {
      window.open(e.fileUrl);
    },
    /**
     * 获取详情
     */
    getDeatil(id) {
      let data = {
        id,
      };
      this.$ajax({
        url: '/web//findHistoryDetail',
        method: 'GET',
        data,
      }).then((r) => {
        if (r.data.code !== 200) return this.$message.error(r.data.msg);
        this.data = r.data.data;
        this.fileLest = r.data.data.fileList;
      });
    },
    /**
     * open
     */
    open(data) {
      const { id } = data;
      this.getDeatil(id);
      this.show = true;
    },
    /**
     * close
     */
    close() {
      this.data = {};
      this.show = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999999;
  width: 100%;
  height: 100%;
  background-color: rgba($color: #000000, $alpha: 0.45);
  overflow: hidden;

  .dialog-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1200px;
    padding: 20px;
    background: #ffffff;
    border-radius: 8px;

    .content-box {
      max-height: 500px;
      margin-bottom: 40px;
      overflow-y: auto;
    }

    .del {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 17px;
      height: 17px;
      cursor: pointer;
      font-size: 17px;
    }

    .title {
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #ed911f;
      line-height: 1;
      margin-bottom: 30px;
    }

    .subheading {
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      line-height: 1;
      margin-bottom: 14px;
    }

    ::v-deep .el-descriptions-item__label.is-bordered-label {
      width: 160px;
      text-align: right;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
    }

    ::v-deep .el-descriptions-item__content {
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }

    .bottom-box {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .button {
        width: 90px;
        height: 34px;
        background: #ed911f;
        border-radius: 4px;
        border: 1px solid #ed911f;
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
    }
  }
}
</style>
