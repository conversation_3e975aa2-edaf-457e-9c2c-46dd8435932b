<!-- 招商服务 -->
<template>
  <div class="wrap-container">
    <div class="nav-area">
      <div class="item-area" v-for="(item, index) in navArr" :key="index"
        :class="[currentTab === item.order ? 'active-item' : 'deactive-item']" @click="navClick(item.order, index)">
        <span>{{ item.title }}</span>
      </div>
    </div>
    <div class="line-gap"></div>
    <listTypeE :columnId="jcmsConfig.indexJr" :breadcrumbName="'金融产品详情'" :bannerType="0">
    </listTypeE>
    <!-- <listTypeC v-show="currentTab == '1'" requestUrl="/gxdt/gydt/" :breadcrumbName="'供应大厅详情'" :showTab="'0'">
    </listTypeC>
    <listTypeC v-show="currentTab == '2'" requestUrl="/gxdt/xqdt/" :breadcrumbName="'需求大厅详情'" :showTab="'0'">
    </listTypeC> -->
  </div>
</template>
<script>
import listTypeE from '@/views/newsListView/components/listTypeE.vue';
export default {
  components: {
    listTypeE,
  },
  data() {
    return {
      currentTab: 1, 
      jcmsConfig: window.ipConfig.jcms,
      navArr: [
        {
          title: '金融产品',
          order: 1,
        },
        // {
        //   title: '需求大厅',
        //   order: 2,
        // }
      ],
    };
  },

  // mounted() {
  // if (this.$route.query.order) {
  //   this.currentTab = Number(this.$route.query.order);
  // }
  // console.log(this.$route.query.order);
  // },
  activated() {
    if (this.$route.query.activeNav) {
      this.currentTab = Number(this.$route.query.activeNav)
      //   this.currentTab = Number(this.$route.query.order);
    } else {
      this.currentTab = 1
    }
  },
  methods: {
    navClick(order, index) {
      this.currentTab = order;
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap-container {
  width: 100%;
  overflow: auto;
  height: 100%;
  background: #fff;
  position: relative;
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-area {
  // width: 100%;
  // padding: 0 310px;
  height: 50px;
  display: flex;
  align-items: center;
  flex-direction: row;
  width: 1300px;
  // background: #f9f9f9;
  // margin-bottom: 24px;

  .active-item {
    font-family: Microsoft YaHei;
    font-weight: bold;
    font-size: 20px;
    color: #ed911f;
    line-height: 24px;
    border-top: 2px solid #ed911f;
    height: 100%;
  }

  .deactive-item {
    font-family: Microsoft YaHei;
    font-weight: 400;
    font-size: 20px;
    color: #333333;
    line-height: 24px;
  }

  .item-area:nth-child(-n + 3) {
    margin-right: 50px;
  }

  .item-area {
    // width: 25%;
    display: flex;
    align-items: center;
    justify-content: center;
    // height: 77px;
  }
}

.line-gap {
  width: 100%;
  height: 1px;
  background: #e5e5e5;
}
</style>