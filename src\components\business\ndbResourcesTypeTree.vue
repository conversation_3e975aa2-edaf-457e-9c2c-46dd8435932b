<template>
  <nd-select
    id="wbNavUsSelect"
    v-model="mineStatus"
    placeholder="全部"
    clearable
    collapse-tags
    @clear="clearable"
  >
    <el-option :value="mineStatusValue" style="height: auto">
      <el-tree
        ref="tree"
        :data="treeData"
        :default-expanded-keys="[]"
        show-checkbox
        node-key="id"
        highlight-current
        :props="defaultProps"
        @check="handleCheckChange"
      />
    </el-option>
  </nd-select>
</template>
<script>
import ndSelect from "@/components/ndSelect.vue";
export default {
  components: {
    ndSelect,
  },
  data() {
    return {
      defaultProps: {
        children: "childrenList",
        label: "label",
        id: "id",
      },
      mineStatus: [],
      mineStatusValue: [],
      treeData: [],
      paginations: {
        groupIds: [],
      },
      selectkey: "",
      selectTreeNode: [],
    };
  },

  mounted() {
    //初始化el-select IE下光标闪现的问题
    this.stateChange();
    this.getTreeData();
    //初始化滚动条
    // let scrollWrap = document.querySelectorAll(
    //   ".el-scrollbar .el-select-dropdown__wrap"
    // )[0];
    // if (scrollWrap) {
    //   scrollWrap.style.cssText =
    //     "margin: 0px; max-height: none; overflow: hidden;";
    // }
    // let scrollBar = document.querySelectorAll(
    //   ".el-scrollbar .el-scrollbar__bar"
    // );
    // scrollBar.forEach((ele) => (ele.style.width = 0));
  },

  methods: {
    //当复选框勾选中时，给下拉框赋值并且给查询条件赋值
    handleCheckChange() {
      let res = this.$refs.tree.getCheckedNodes();
      console.log(res, "res");
      // 将选中的值赋值给selectkey
      this.selectkey = res.map((item) => {
        return item.id;
      });

      // 将选中的值传递给index
      this.$emit("func", res);
      let arrLabel = [];
      let arr = [];
      res.forEach((item) => {
        arrLabel.push(item.label);
        arr.push(item);
      });
      this.mineStatusValue = arr;
      if (arrLabel.length > 0) {
        this.mineStatus = arrLabel[0] + "...";
      } else {
        this.mineStatus = "全部";
      }

      for (let i in arr) {
        this.paginations.groupIds.push(arr[i].id);
      }
    },
    //当多选时候，删除小标签时，将删除的子节点状态改为未勾选，
    // remove(val) {
    //   this.paginations.groupIds = [];
    //   this.$refs.tree.setChecked(val, false);
    //   let arr = this.$refs.tree.getCheckedNodes();
    //   for (let i in arr) {
    //     this.paginations.groupIds.push(arr[i].id);
    //   }
    //   // this.postcar(this.paginations.groupIds);
    // },
    //清除所有选项
    clearable() {
      this.paginations.groupIds = [];
      this.cars = [];
      this.$refs.tree.setCheckedKeys([]);
    },

    stateChange() {
      // 处理在ie浏览器出现光标的问题
      const elem = document.getElementById("wbNavUsSelect");
      if (elem.hasAttribute("unselectable") === false) {
        elem.setAttribute("unselectable", "on");
        elem.setAttribute("onfocus", "this.blur()");
      }
    },
    //获取树数据
    getTreeData() {
      this.treeData = [];
      this.$ajax({
        method: "post",
        url: "" + "/ziyuancx.do?method=resourceTypeList",
      })
        .then((res) => {
          if (res.data.code == 0) {
            // this.assetsValue = "";
            this.treeData = res.data.data;
            // this.changeId2(this.treeData, "id", "id");
            this.changeId2(this.treeData, "dataValue", "label");
            // this.changeId2(this.treeData, "childrenList", "children");
            console.log(this.treeData, "1111");
          } else {
            this.$message({
              showClose: true,
              message: response.msg,
              type: "warning",
            });
          }
          //默认选中所有
          this.$nextTick(() => {
            //这个如果要默认全选就必须加，否则会报错“setCheckedNodes”未定义
            this.$refs.tree.setCheckedNodes(this.treeData);
            //  获取复选框中选中的的值，并赋值给select
            this.handleCheckChange();
            this.mineStatus = "全部";
          });
        })
        .catch((err) => {});
    },
    //【jsp】获得服务器路径
    getServerPath() {
      if (typeof getContextPath === "function") {
        console.log("获得服务器路径:" + getContextPath());
        return getContextPath();
      } else {
        return "/sz_product_new";
      }
    },
    // 改变树形结构的key
    changeId2(objAry, key, newkey) {
      objAry.forEach((item) => {
        Object.assign(item, {
          [newkey]: item[key],
        });
        // delete item[key];
        if (item.childrenList != null) {
          this.changeId2(item.childrenList, key, newkey);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/*滚轮样式*/
*::-webkit-scrollbar {
  width: 10px;
  height: 10px!important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}
/*滚轮样式*/

.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 230px;
  padding: 0;
  overflow: hidden;
  overflow-y: auto;
}
.el-select-dropdown__item.selected {
  font-weight: normal;
}
ul li >>> .el-tree .el-tree-node__content:hover {
  background-color: #e8f7ff;
}
ul li >>> .el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
  background-color: #ffffff;
}
.el-tree-node__label {
  font-weight: normal;
  margin-right: 10px;
  font-size: 12px;
}
.el-tree .is-current .el-tree-node__label {
  color: #409eff;
  font-weight: 700;
}
.el-tree .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}
.el-select-dropdown__item.hover {
  background: transparent;
}
.el-select-dropdown__item span {
  margin-left: 10px;
}
::v-deep .el-scrollbar__bar.is-horizontal {
  height: 0px;
}
::v-deep .el-scrollbar__bar.is-vertical {
  width: 0px;
}
::v-deep
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #c8ecff;
}
::v-deep .el-tag.el-tag--info {
  font-size: 12px;
  height: 16px;
  display: inline;
  line-height: 16px;
}
::v-deep .el-tag.el-tag--info {
  max-width: 100px;
}
</style>