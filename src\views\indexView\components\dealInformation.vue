<template>
  <div class="main">
    <div class="main-box">
      <div class="top-box">
        <div class="left">
          <div class="title-text">招商服务</div>
          <img src="@/assets/detail/title-icon.png" alt="" srcset="" />
        </div>
        <div class="more-text" @click="toMore">查看更多>></div>
      </div>
      <el-carousel trigger="click" height="257px" :interval="3000" arrow="hover" @change="carouselChange" indicator-position="none">
        <el-carousel-item v-for="(item2, index) in list" :key="index">
          <div class="bot-box">
            <template v-for="(item,index) in item2">
              <div class="list-box" :key="index" :style="{
                'background-image': 'url(' + item.imgUrl + ')',
                'background-repeat': 'no-repeat',
                'background-size': '100% 100%',
              }" @click="goDetails(item.path)">
                <div class="text-cont">
                  <div class="list-text">
                    {{ item.title }}
                    <!-- 北京市通县177.68亩六塘河河滩地出租 北京市通县177.68亩六塘河河滩地出租 -->
                  </div>
                </div>
              </div>
            </template>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script>
import ndRadio from '@/components/ndRadio.vue';

export default {
  components: {
    ndRadio,
  },
  name: 'home',
  data() {
    return {
      imgList: [
        // { url: require('@/assets/detail/exBg3.png') },
        // { url: require('@/assets/detail/exBg3.png') },
        // { url: require('@/assets/detail/exBg3.png') },
        // { url: require('@/assets/detail/exBg3.png') },
      ], //初始化数据列表
      list: [
        // { url: require('@/assets/detail/exBg3.png') },
        // { url: require('@/assets/detail/exBg3.png') },
        // { url: require('@/assets/detail/exBg3.png') },
        // { url: require('@/assets/detail/exBg3.png') },
      ], //初始化数据列表
      activeArea: 0, //选中的省份
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    handleActive(index) {
      this.activeArea = index;
    },
    goDetails(path) {
      // this.$router.push({
      //   path: 'details',
      // });
      // this.$message.success('敬请期待！');
      // 跳转详情
      this.$router.push({
        path: 'listDetailView',
        query: {
          path: path,
          // preInfo: JSON.stringify({ name: this.$route.meta.pageName, path: this.$route.name }),
          bannerType: 0, // bannerType[this.$route.name],
          breadcrumbName: '招商服务',
        },
      });
      this.$emit('getDealInformation', '1')
    },

    toMore() {
      //更多
      // this.$router.push({
      //   path: 'dealInformationIndex',
      //   query: {
      //     // path: item.path,
      //     breadcrumbName: '供需大厅',
      //     bannerType: 0,
      //   },
      // });
      this.$router.push('dealInformationIndex');
      this.$emit('getDealInformation', '1')
      // this.$message.success('敬请期待！');
      // sessionStorage.setItem('isMain', true);
    },
    // 跳转 详情
    goDetail(item) {
      this.$router.push({
        path: 'listDetailView',
        query: {
          path: item.path,
          preInfo: JSON.stringify({ name: this.$route.meta.pageName, path: this.$route.name }),
          bannerType: 4,
        },
      });
    },
    getData() {
      //---初始化数据
      this.$ajax({
        url: '/zsfw/ckxx/',
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        if (res.status === 200) {
          try {
            // this.list = eval(res.data).slice(0, 8);
            // console.log(this.list, '009090990'            

            // let arrAll = eval(res.data).slice(0, 8);
            let arrAll = eval(res.data);
            let small = [];
            arrAll.forEach(item => {
              if (small.length == 4) {
                this.list.push(small);
                small = [];
              }
              small.push(item);
            });
            if (small.length) this.list.push(small);
            console.log(this.list, '009090990');
          } catch (error) {
            console.error('数据格式有误');
          }
        }
      });
    },
    carouselChange(e) {
      console.log(e, '轮播图');
    },
  },
};
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  padding: 30px 0 0px;
  background: #fff;
  display: flex;
  justify-content: center;

  .main-box {
    // width: 100%;
    width: 1300px;

    .top-box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      user-select: none;

      .left {
        display: flex;
        flex-direction: column;
        height: 32px;

        .title-text {
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 24px;
          color: #333333;
          line-height: 24px;
        }
      }

      .more-text {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 24px;
        cursor: pointer;
      }
    }

    .bot-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;

      // margin-bottom: 5px;
      // row-gap: 20px;
      .list-box {
        height: 227px;
        width: 311px;
        // height: 20.68vh;
        // width: 23.84%;
        cursor: pointer;
        position: relative;
        margin-bottom: 30px;
        border-radius: 8px;
        display: flex;
        align-items: flex-end;

        .text-cont {
          // position: absolute;
          // bottom: 0px;
          // left: 2px;
          height: 44px;
          width: 100%;
          background: rgba(61, 61, 61, 0.8);
          border-radius: 0px 0px 8px 8px;
          padding-left: 10px;
          display: flex;
          align-items: center;
        }

        .list-text {
          width: 98%;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    ::v-deep .el-carousel__arrow {
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.9);
      top: calc(50% - 14px);

      .el-icon-arrow-left,
      .el-icon-arrow-right {
        color: #666666;
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
}
.more-text:hover{
  color: #ed911f!important;
}
</style>
