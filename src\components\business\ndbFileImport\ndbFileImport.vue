<template>
  <div>
    <nd-dialog
      ref="fileImport"
      width="690px"
      height="253px"
      :title="title"
      append-to-body
      :before-close="closeFileImport"
      center
    >
      <div class="content">
        <div class="content-item-one">
          导入文件：
          <input
            ref="avatarInput"
            type="file"
            multiple
            name="avatar"
            accept=".xlsx,.xls"
            style="display: none"
            @change="changeExcel($event)"
          />
          <nd-input v-model="content" width="492px" :disabled="true" />
          <nd-button style="width: 73px; margin-left: 16px" type="normal" @click="handleSubmit">
            浏览...
          </nd-button>
        </div>
        <div class="content-item-two" style="cursor: pointer">
          <img src="@/img/download.png" /><span @click="downloadTemplate">下载导入模板</span
          ><span v-if="isshow" @click="viewResults">查看上次导入结果</span>
        </div>
        <div class="content-item-three">
          <div class="text">提示说明：</div>
          <div>
            <p v-if="flag1">
              1、不同支付银行、不同业务类型要求的收款方信息可能不同,为确保满足导入要求，请您务必下载本功能导入模板。
            </p>
            <p v-if="flag2">1、请按导入模板要求整理数据。</p>
            <p v-if="flag3">2、每次导入数据是在原有数据基础上进行追加。</p>
            <p v-if="flag4">
              2、每次导入只能导入一张凭证。导入凭证为数据追加,不校验重复,系统自动生成导入凭证号。
            </p>
             <p v-if="flag6">
              2、已存在的数据系统会进行更新,不存在的数据才进行追加
            </p>
            <p v-if="flag5">
              3、若多人中多文件同时导入或导入数据量过大,系统后台会排队处理，您无需在该界面等待导入结果。
            </p>
          </div>
        </div>
      </div>
      <template #footer style="width: 45px">
        <nd-button type="primary" @click="confirmImport"> 确定导入 </nd-button>
        <nd-button v-if="bankTable" type="normal" @click="bankAccount"> 银行行号对照表 </nd-button>
        <nd-button v-if="identificationCode" type="normal" @click="viewIdentificationCode">
          查看组织识别码
        </nd-button>
        <nd-button type="normal" @click="close"> 关闭 </nd-button>
      </template>
    </nd-dialog>
    <!-- 查看导入结果弹窗 -->
    <ndb-file-import-result ref="fileImportResult" :is-sz="isSz" />
    <!-- 点击确定导入打开加载页面弹框 -->
    <ndb-file-import-loading ref="fileImportLoading" @fatherMethod="fatherMethod" />
    <!-- 银行行号对照表对应弹框 -->
    <bankTable ref="bankTable" />
    <!-- 查看组织识别码对照表对应弹框 -->
    <view-identification-code
      :isSz="isSz"
      :disciplineSupervision="disciplineSupervision"
      ref="viewIdentificationCode"
    ></view-identification-code>
  </div>
</template>

<script>
import ndDialog from '@/components/ndDialog.vue';
import ndButton from '@/components/ndButton.vue';
import ndTable from '@/components/ndTable.vue';
import ndInput from '@/components/ndInput.vue';
import ndbFileImportResult from './ndbFileImportResult.vue';
import ndbFileImportLoading from './ndbFileImportLoading.vue';
import bankTable from './bankTable.vue';
import viewIdentificationCode from './viewIdentificationCode.vue';
export default {
  components: {
    ndDialog,
    ndButton,
    ndTable,
    ndInput,
    ndbFileImportResult,
    ndbFileImportLoading,
    bankTable,
    viewIdentificationCode,
  },
  props: {
    // 是否需要银行对照表默认false不需要
    bankTable: {
      type: Boolean,
      default: false,
    },
    // 是否需要查看组织识别码默认false不需要
    identificationCode: {
      type: Boolean,
      default: false,
    },
    isSz: {
      type: Boolean,
      default: false,
    },
    //查看组织识别码
    disciplineSupervision: {
      type: Boolean,
      default: false,
    },
    //三资的模板类型选择 10.租赁合同 11.资产出售合同 12.资源发包 14.资金合同
    modelType: {
      type: String,
      default: '',
    },
    // 下载导入模板url
    downloadObject: {
      type: Object,
      dedefault: function () {
        return {};
      },
    },
    title: {
      type: String,
      default: '导入',
    },
    // 自定义导入事件
    customizeImport: {
      type: Boolean,
      default: false,
    },
    //下方文字说明
    textType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      content: '', //上传的文件名
      url: '', // 当前项目后台接口请求路径
      files: '', //导入文件
      timer: null, //定时器每隔几秒调用一次
      isshow: true, //判断是否有查看上次导入结果按钮
      flag1: false,
      flag2: true,
      flag3: true,
      flag4: false,
      flag5: true,
      flag6:false,
    };
  },

  mounted() {},

  methods: {
    // 打开弹框
    open() {
      this.$refs.fileImport.open();
      if (this.modelType === '13') {
        this.flag1 = false;
        this.flag2 = true;
        this.flag3 = false;
        this.flag4 = true;
        this.flag5 = true;
      }
      // 资金拨付信息  租赁导入合同 租金导入合同
      if (this.textType === '1') {
        this.flag1 = false;
        this.flag2 = true;
        this.flag3 = true;
        this.flag4 = false;
        this.flag5 = true;
      }
      // 平湖导入
      else if (this.textType === '2') {
        this.flag1 = false;
        this.flag2 = true;
        this.flag3 = true;
        this.flag4 = false;
        this.flag5 = false;
        this.isshow = false;
      }
      // 贾汪三资干部亲属
       else if (this.textType === '3') {
        this.flag1 = false;
        this.flag2 = true;
        this.flag3 = false;
        this.flag4 = false;
        this.flag5 = true;
        this.flag6 = true;
      }
      this.existsImportButton();
    },
    // 关闭按钮关闭弹框
    close() {
      this.flag1 = false;
      this.flag2 = true;
      this.flag3 = true;
      this.flag4 = false;
      this.flag5 = true;
      this.isshow = true;
      (this.content = ''), (this.files = '');
      this.$refs.fileImport.close();
    },
    //右上角x号关闭弹框
    closeFileImport() {
      this.close();
    },
    // 判断是否需要查看上次导入结果的按钮
    existsImportButton() {
      if (this.isSz) {
        if (this.modelType === '21') {
          this.$ajax({
            url: '/szUseWorkPerson/lookLast.do',
            // url: '/szUseWorkPerson/xlsImport.do',
            method: 'post',
          }).then((res) => {
            if (res.data.code == 6000) {
              this.isshow = false;
            } else {
              this.isshow = true;
            }
          });
        }else if (this.modelType === '13') {
          this.$ajax({
            url: '/pingzgl/getImportResult.do',
            method: 'post',
          }).then((res) => {
            if (res.data.code == 6000) {
              this.isshow = false;
            } else {
              this.isshow = true;
            }
          });
        } else if (this.modelType === '10' || this.modelType == '11' || this.modelType == '12') {
          let url = '/zlhtNew.do?method=lookLast';
          if (this.modelType == '12') {
            url = '/zyfbNew.do?method=xlsImport';
          }
          let data =
            this.modelType == '12' ? { dataType: this.modelType } : { type: this.modelType };
          this.$ajax({
            url: url,
            method: 'post',
            data: data,
          }).then((res) => {
            if (res.data.code === 0) {
              this.isshow = true;
            } else {
              this.isshow = false;
            }
          });
        } else if (this.modelType === '19') {
          this.$ajax({
            url: '/szOtherContract.do?method=queryLastImportFile',
            method: 'post',
            data: {
              dataType: '19',
            },
          }).then((res) => {
            if (res.data.code === 0) {
              this.isshow = true;
            } else {
              this.isshow = false;
            }
          });
        }
      } else if (this.textType === '2') {
        //不需要查看上次导入结果的按钮，不做任何处理
        console.log('平湖导入');
      } else {
        // 贾汪;
        this.$ajax({
          method: 'get',
          url: '/excel/downloadFailRecord',
          data: {
            type: this.downloadObject.type,
          },
          serverName: 'nd-disciplineSupervision',
        })
          .then((res) => {
            if (200 == res.data.code) {
              if (res.data.data === null) {
                this.isshow = false;
              } else {
                this.isshow = true;
              }
            }
          })
          .catch(function () {});
      }
    },
    // 选择导入文件
    handleSubmit() {
      this.$refs.avatarInput.click();
    },
    changeExcel(e) {
      console.log(e, 'eeeeeee');
      const file = e.target.files[0];
      this.content = file.name;
      this.files = file;
    },

    // 下载导入模板
    downloadTemplate() {
      if (this.isSz) {
        // 三资  const bankName = this.bankType; console.log(bankName);
        let szUrl = '';
        if (this.modelType === '13') {
          szUrl = '/zlht.do?method=xlsDownLoad_hasInfo&modelType=13';
        } else if (this.modelType === '10') {
          szUrl = '/zlht.do?method=xlsDownLoad_hasInfo&modelType=10';
        } else if (this.modelType === '11') {
          szUrl = '/zlht.do?method=xlsDownLoad_hasInfo&modelType=11';
        } else if (this.modelType === '12') {
          szUrl = '/zyfbNew.do?method=xlsDownLoad_hasInfo&modelType=12';
        } else if (this.modelType === '19') {
          szUrl = '/szOtherContract.do?method=downloadContractTemp';
        } else {
          szUrl = this.downloadObject.url + this.downloadObject.bankType;
        }
        this.$ajax({
          url: szUrl,
          method: 'post',
          responseType: 'blob',
        }).then((res) => {
          console.log(res);
          if (!res) return;
          const blob = new Blob([res.data], {
            type: 'application/vnd.ms-excel',
          }); // 构造一个blob对象来处理数据，并设置文件类型
          if (window.navigator.msSaveOrOpenBlob) {
            //兼容IE10
            navigator.msSaveBlob(blob, this.downloadObject.excelName);
          } else {
            const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
            const a = document.createElement('a'); //创建a标签
            a.style.display = 'none';
            a.href = href; // 指定下载链接
            a.setAttribute('download', this.downloadObject.excelName);
            //a.download = this.filename; //指定下载文件名
            a.click(); //触发下载
            URL.revokeObjectURL(a.href); //释放URL对象
          }
        });
      } else if (this.textType === '2') {
        this.$emit('downloadFormwork');
      } else {
        // 贾汪
        this.$ajax({
          url: this.downloadObject.url,
          data: {
            type: this.downloadObject.type,
          },
          serverName: 'nd-disciplineSupervision',
          method: 'get',
          responseType: 'blob',
        }).then((res) => {
          if (!res) return;
          const blob = new Blob([res.data], {
            type: 'application/vnd.ms-excel',
          });
          // 构造一个blob对象来处理数据，并设置文件类型
          if (window.navigator.msSaveOrOpenBlob) {
            //兼容IE
            navigator.msSaveBlob(blob, this.downloadObject.excelName);
          } else {
            const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
            const a = document.createElement('a'); //创建a标签
            a.style.display = 'none';
            a.href = href; // 指定下载链接
            a.setAttribute('download', this.downloadObject.excelName);
            //a.download = this.filename; //指定下载文件名
            a.click(); //触发下载
            URL.revokeObjectURL(a.href); //释放URL对象
          }
        });
      }
    },
    // 开启定时器
    openSetIntervalTimer() {
      //每隔多少秒检查一下根据状态查看上次导入结果判断走哪个弹框
      clearInterval(this.timer);
      this.openImport();
      this.timer = setInterval(() => {
        this.openImport();
      }, 500);
    },
    // 关闭定时器
    closeIntervalTimer() {
      clearInterval(this.timer); //清除定时器
      this.timer = null;
    },
    fatherMethod() {
      this.closeIntervalTimer();
    },
    // 根据状态查看上次导入结果判断走哪个弹框
    openImport() {
      this.$ajax({
        method: 'get',
        url: '/excel/downloadFailRecord',
        data: {
          type: this.downloadObject.type,
        },
        serverName: 'nd-disciplineSupervision',
      })
        .then((res) => {
          if (200 == res.data.code) {
            if (res.data.data.status == '1') {
              if (res.data.data.done == '1') {
                this.$refs.fileImportResult.isshow = false;
              } else {
                this.$refs.fileImportResult.isshow = true;
              }
              this.$refs.fileImportLoading.closeLoading();
              this.$refs.fileImportResult.openImportResult(this.downloadObject.type);
              this.closeIntervalTimer();
            } else {
              this.$refs.fileImportLoading.openLoading();
            }
          } else {
            this.$refs.fileImportLoading.closeLoading();
            this.$message({
              message: res.data.message,
              type: 'warning',
            });
          }
        })
        .catch(function () {});
    },
    // 点击打开查看上次导入结果弹框
    viewResults() {
      if (this.isSz) {
        if (this.modelType === '13') {
          this.$refs.fileImportResult.openImportResult(this.modelType);
        } else if (this.modelType === '10' || this.modelType === '11' || this.modelType === '12' ||  this.modelType === '21' ) {
          this.$refs.fileImportResult.openImportResult(this.modelType);
        } else if (this.modelType === '19') {
          this.$refs.fileImportResult.openImportResult(this.modelType);
        } else {
          let data = sessionStorage.getItem('sizeId');
          if (data !== null) {
            this.$refs.fileImportLoading.openLoading();
          } else {
            this.$refs.fileImportResult.openImportResult();
          }
        }
      } else {
        //贾汪
        this.openSetIntervalTimer();
      }
    },
    // 点击确定
    confirmImport() {
      if (this.content == '') {
        this.$message({
          message: '请选择导入文件，导入文件不能为空',
          type: 'warning',
        });
      } else {
        if (this.customizeImport) {
          this.$emit('importFile', this.files);
        } else {
          // 贾汪确定导入
          this.$refs.fileImportLoading.openLoading();
          this.$refs.avatarInput.value = '';
          this.content = '';
          let data = new FormData();
          data.append('file', this.files);
          data.append('type', this.downloadObject.type);
          this.$ajax({
            method: 'post',
            url: '/excel/upload',
            data,
            serverName: 'nd-disciplineSupervision',
          })
            .then((res) => {
              if (200 == res.data.code) {
                this.openSetIntervalTimer();
              } else {
                this.$refs.fileImportLoading.closeLoading();
                this.$message({
                  message: res.data.message,
                  type: 'warning',
                });
              }
            })
            .catch(function () {});
        }
      }
    },
    // 打开银行行号对照表弹框
    bankAccount() {
      this.$refs.bankTable.openBankTable();
    },
    // 打开查看组织识别码对照表弹框
    viewIdentificationCode() {
      this.$refs.viewIdentificationCode.open();
    },
    openLoading() {
      this.$refs.fileImportLoading.openLoading();
    },
    closeLoading() {
      this.$refs.fileImportLoading.closeLoading();
    },
    openImportResult(modelType) {
      this.$refs.fileImportResult.openImportResult(modelType);
    },
  },
};
</script>

<style lang="scss" scoped>
.content-item-one {
  display: flex;
  align-items: center;
  padding-left: 17px;
  padding-top: 17px;
  margin-bottom: 8px;
}
.content-item-two {
  padding-left: 76.5px;
  margin-bottom: 30px;
  span {
    margin-right: 19px;
    height: 12px;
    font-size: 12px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #0098ff;
  }
}
.content-item-three {
  padding-left: 17px;
  p {
    margin-bottom: 9.5px;
  }
  .text {
    font-size: 12px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #555555;
    line-height: 35px;
  }
}
</style>
