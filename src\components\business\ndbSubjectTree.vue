<template>
  <div class="ndb-select-organize-tree-box">
    <nd-select
      id="wbJspTreeSelect"
      ref="ndSelect"
      v-bind="$attrs"
      :filterable="true"
      :remote="true"
      :value="nodeName"
      :placeholder="placeholder || '请选择'"
      :width="width"
      :remote-method="remoteMethod"
      :clearable="true"
      @change="changeVal"
      @focus="focus"
      v-on="$listeners"
    >
      <!-- list结构 -->
      <div v-show="isShowText">
        <span
          style="color: #555555; font-size: 14px;display: block;text-align: center;padding: 15px 0px;"
        >暂无数据</span>
      </div>
      <el-option v-for="(item, index) in options" v-show="showOptionList" :key="index" :label="item.name" :value="item">
        <div style="position:relative;">
          <span
            style="color: #555555; font-size: 14px;display: block;height: 30px;line-height: 30px;"
            v-html="keyHights(item.fullKmName)"
          />
        </div>
      </el-option>
      <!-- tree结构 -->
      <el-option v-show="showOptionTree" v-loading="loading" :value="nodeName" :label="nodeName">
        <el-tree
          id="tree-option"
          ref="tree"
          highlight-current
          :data="listArry"
          :props="defaultProps"
          node-key="id"
          :default-expand-all="false"
          @node-click="handleNodeClick"
        />
      </el-option>
    </nd-select>
  </div>
</template>
<script>
import ndSelect from "../../components/ndSelect.vue";
export default {
  components: {
    ndSelect,
  },
  model: {
    prop: 'subjectTreeVal',
    event: "change",
  },
  props: {
    subjectTreeVal: {
      type: [Number, String, Object],
      default: null,
    },
    width: {
      type: String,
      default: "220px",
    },
    type:{
      type: String,//传tree为树形结构，传list为list结构
      default: "tree",
    },
    sourceType:{
      type: String,//地址来源  传ledger总账（1） ， 传detailed明细账（0）
      default: "",
    },
  },
  data() {
    return {
      options: [],
      showOptionList: false,
      showOptionTree: true,
      placeholder: "",
      nodeName: '',
      defaultProps: {
        id: "id",
        label: "kmName",
        children: "children"
      },
      listArry: [],
      loading: false,
      inputVal: '',
      isShowText: false
    };
  },
  watch: {
    'subjectTreeVal.kmName': {
      immediate: true,
      deep: true,
      handler(value) {
        if (value != '' && value != undefined) {
          // this.nodeName = value;
        }
      }
    },
    type: {
      immediate: true,
      deep: true,
      handler(value) {
        if (value != '' && value != undefined) {
          if (value == 'list') {
            this.showOptionList = true;
            this.showOptionTree = false;
          } else {
            this.showOptionTree = true;
            this.showOptionList = false;
          }
        }
      }
    },
  },
  mounted() {
    if(this.type=='list'){
      this.selectMethod();
    }else{
      this.getList();
    }
  },
  methods: {
    focus() {//获得焦点时触发
      this.options = [];
      this.inputVal='';
      this.isShowText=false;
      this.selectMethod();
    },
    keyHights(title) {// 改变搜索关键字颜色为蓝色
      let s = this.inputVal;
      var str = title;
      let inputvalue = s.replace(",");
      let keyWordArr = inputvalue.split(",");
      if (str && str != "") {
        keyWordArr.forEach((e) => {
          let regStr = "" + `(${e})`;
          let reg = new RegExp(regStr, "g");
          str = str.replace(reg, '<span style="color:#0098FF;position:relative;top:-2px;">' + e + "</span>");
        });
      }
      return str;
    },
    remoteMethod(query) {
      this.inputVal = query;
      if (query !== '') {
        this.options = [];
        this.selectMethod();
      } else {
        this.isShowText = false;
        this.selectMethod();
      }
    },
    changeVal(e) {//取用户下拉选取值
      this.isShowText = false;
      this.nodeName = e.kmName;
      this.$emit('change', e)
    },
    selectMethod() {
      return new Promise(() => {
        let urlFlag;
        let isToTree;
        if(this.type=='tree'){
          isToTree='';
        }else{
          isToTree='1';
        }
        if(this.sourceType=='ledger'){
          urlFlag='1';
        }else{
          urlFlag='';
        }
        let params = {
          kmName: this.inputVal,
          kmCode: '',
          kmJb: '',
          endKm: '',
          isAll: '',
          urlFlag: urlFlag,
          isToTree: isToTree,
        };
        this.$ajax({
          url: "" + "/tatolExcel/kmTree.do",
          method: "post",
          data: params,
        })
          .then((res) => {
            if (res.data.code == 0) {
              if (res.data.data.tree.length == 0) {
                if (this.options.length == 0 && this.showOptionTree == false) {
                  this.isShowText = true;
                } else {
                  this.isShowText = false;
                }
                return false;
              } else {
                this.isShowText = false;
                this.options = [...this.options, ...res.data.data.tree];
              }
            } else {
              if (res.data.code != 9999) {
                this.$message.error(res.data.msg);
              }
            }
            this.loading = false;
          })
      });
    },
    filterNode(value, data) {
      if (!value) return true
      return data.kmName.indexOf(value) !== -1
    },
    assetsTypeFilter(val) {
      this.$refs.tree.filter(val)
    },
    // 重新加载数据
    budgeReload() {
      this.$nextTick(() => {
        this.selectMethod();
        this.getList();
      });
    },
    //获取预算科目树信息
    getList() {
      this.listArry = [];
      this.loading = true;
        let urlFlag;
        let isToTree;
        if(this.type=='tree'){
          isToTree='';
        }else{
          isToTree='1';
        }
        if(this.sourceType=='ledger'){
          urlFlag='1';
        }else{
          urlFlag='';
        }
      let params = {
        kmName: '',
        kmCode: '',
        kmJb: '',
        endKm: '',
        isAll: '',
        urlFlag: urlFlag,
        isToTree:isToTree,
      };
      this.$ajax({
        url: "" + "/tatolExcel/kmTree.do",
        method: "post",
        data: params,
      })
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.tree.length > 0) {
              this.listArry = res.data.data.tree;
            }
          } else {
            if (res.data.code != 9999) {
              this.$message.error(res.data.msg);
            }
          }
          this.loading = false;
        })
    },
    // 节点点击
    handleNodeClick(node) {
      this.nodeName = node.kmName;
      this.$emit('change', node)
      this.$refs.ndSelect.$refs.select.blur();

    },
  },
};
</script>
<style scoped>
*::-webkit-scrollbar {
  width: 10px;
  height: 10px !important;
  width: 6px;
  height: 12px !important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}

.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 230px;
  /* padding: 0; */
  padding: 0 15px;
  overflow: hidden;
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

ul li>>>.el-tree .el-tree-node__content:hover {
  background-color: #e8f7ff;
}

ul li>>>.el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
  background-color: #ffffff;
}

.el-tree-node__label {
  font-weight: normal;
  margin-right: 10px;
  font-size: 12px;
}

.el-tree .is-current .el-tree-node__label {
  color: #409eff;
  font-weight: 700;
}

.el-tree .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}

.el-select-dropdown__item.hover {
  background: transparent;
}

.el-select-dropdown__item span {
  margin-left: 10px;
}

::v-deep .el-scrollbar__bar.is-horizontal {
  height: 0px;
}

::v-deep .el-scrollbar__bar.is-vertical {
  width: 0px;
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #c8ecff;
}

.el-select-dropdown__item span {
  margin-left: 0px;
}
</style>
