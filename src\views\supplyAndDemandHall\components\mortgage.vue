<template>
  <div class="list-content">
    <div class="div-box">
      <div class="box" v-for="(item, index) in pageData" :key="index">
        <div class="name" @click.stop="handleMore(item)">{{ item.announcementTitle }}</div>
        <div class="time">{{ item.releaseTime }}</div>
      </div>
    </div>
    <!-- 分页器 -->
    <div class="pagination">
      <nd-pagination
        :page-size="pager.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="pager.total"
        :total-page="totalPage"
        :current-page="pager.pageNo"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
const bannerType = {
  // newsListView: 3,
  projectRecommendations: 4,
  regulationsListView: 3,
  projectInformationView: 1,
  helpCenter: 2,
};
import ndPagination from '@/components/ndPagination.vue';
export default {
  name: 'list',
  components: {
    ndPagination,
  },
  props: {
    // showDownLoad: {
    //   type: Boolean,
    //   default: false,
    // },
    requestUrl: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      showDownLoad: false,
      listData: [],
      pager: {
        pageNo: 1, // 当前页数
        pageSize: 9, // 当前页条数
        total: 1, // 总条目数
        pageSizes: [10, 30, 50, 100],
      },
      totalPage: 1,
      pageData: [],
    };
  },

  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.$ajax({
        // url: 'http://**************:8080/manage/notice/mortgageAnnoList',
        url: '/notice/mortgageAnnoList',
        method: 'post',
        // needServerPath:false,
        data:{
            pageNo:this.pager.pageNo,
            pageSize:this.pager.pageSize,
        }
      }).then((res) => {
        if (res.data.code === 200) {
          try {
            this.pageData =res.data.data.records;
            this.pager.total = res.data.data.total;
            this.totalPage = Math.ceil(res.data.data.total / this.pager.pageSize);
          } catch (error) {}
        }
      });
    },

    // 跳转详情
    handleMore(item) {
      let query = [];
      query = [{ name: '交易公告', route: 'detailsMortgage', query: { type: 0 } }];

      let query2 = {
        id: item.id,
        route: JSON.stringify(query),
        type: "",
        homeType: 1
      };
      if (window.location.origin == "http://localhost:8080") {
        window.open(window.location.origin + `/#/detailsMortgage?id=${item.id}&route=${JSON.stringify(query)}&type=${query2.type}&homeType=1`)
      } else {
        window.open(window.location.origin + window.location.pathname + `#/detailsMortgage?id=${item.id}&route=${JSON.stringify(query)}&type=${query2.type}&homeType=1`)
      }
    },

    //  分页器函数
    handleCurrentChange(e) {
      this.pager.pageNo = e;
      this.getData()
    },
    handleSizeChange(e) {
      this.pager.pageSize = e;
      this.pager.pageNo = 1;
      this.getData()
    },
  },
};
</script>
<style>
.list-item:last-child {
  border-bottom: 0 !important;
}
</style>
<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.list-content {
  width: 1200px;
  margin: 0 auto;
  padding: 30px 0;

  .div-box {
    width: 100%;
    min-height: 500px;
    border: 1px solid #ececec;
    border-radius: 10px;
    padding: 30px 20px;
    overflow-y: auto;
    .box {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      .name {
        cursor: pointer;
      }
    }
    .box:last-child{
      margin-bottom: 0px;
    }
  }
}
</style>