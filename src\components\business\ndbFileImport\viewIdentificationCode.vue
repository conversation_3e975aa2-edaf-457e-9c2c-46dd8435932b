<template>
  <div>
    <nd-dialog
      ref="identificationCode"
      width="1100px"
      height="513px"
      title="组织信息"
      append-to-body
      :before-close="closeIdentificationCode"
      center
    >
      <div class="content">
        <!-- 查询条件 -->
        <div class="search-opea">
          <div class="item" v-if="isSz">
            <span>所属组织:</span>
            <ndb-selectvillage-tree
              :width="'210px'"
              @change="getAreaValueSz($event)"
              :isSz="true"
            ></ndb-selectvillage-tree>
          </div>
          <div class="item" v-if="disciplineSupervision">
            <span>所属组织:</span>
            <ndb-selectvillage-tree
              :width="'210px'"
              :serverName="'nd-disciplineSupervision'"
              @change="getAreaValue($event)"
            ></ndb-selectvillage-tree>
          </div>
          <div class="item">
            <span>组织名称:</span>
            <nd-input v-model="params.organizationName" width="210px" />
          </div>
          <div class="item">
            <span>组织编码(系统识别码):</span>
            <nd-input v-model="params.organizationalCode" width="210px" />
          </div>
          <div class="item">
            <nd-button type="primary" @click="search"> 查询 </nd-button>
          </div>
        </div>
        <!-- 列表数据 -->
        <ndTable v-if="isSz" v-loading="loading" :data="tableData" style="height: 431px">
          <el-table-column prop="UNITNAME" label="所属地区" align="center" />
          <el-table-column prop="DEPTNAME" label="组织名称" align="center" />
          <el-table-column prop="SYSTEMCODE" align="center" label="组织编码(系统识别码)" />
        </ndTable>
         <ndTable  v-if="!isSz" v-loading="loading" :data="tableData" style="height: 431px">
          <el-table-column prop="area" label="所属地区" align="center" />
          <el-table-column prop="deptName" label="组织名称" align="center" />
          <el-table-column prop="systemCode" align="center" label="组织编码(系统识别码)" />
        </ndTable>
      </div>
      <template #footer>
        <nd-button type="normal" @click="close"> 关闭 </nd-button>
      </template>
    </nd-dialog>
  </div>
</template>

<script>
import ndDialog from '@/components/ndDialog.vue';
import ndButton from '@/components/ndButton.vue';
import ndTable from '@/components/ndTable.vue';
import ndInput from '@/components/ndInput.vue';
import ndSelect from '@/components/ndSelect.vue';
import ndbSelectvillageTree from '@/components/business/ndbSelectVillageTree.vue';

export default {
  components: {
    ndDialog,
    ndButton,
    ndTable,
    ndInput,
    ndSelect,
    ndbSelectvillageTree,
  },
  props: {
    isSz: {
      type: Boolean,
      default: false,
    },
    disciplineSupervision: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      // 查询列表参数
      params: {
        organizationName: '', //组织名称
        organizationalCode: '', //组织编码
      },
      url: '', //请求接口地址
      tableData: [], //列表数据,
      unitId: '', //地区id
      allpath: '', //地区全路径
      level: '', //地区级别
      type: '', //地区类型
      name: '', //地区名称
    form:{
      areaId:'',//贾汪三资地区id
      deptId:'',//贾汪三资单位id
    }
    };
  },

  mounted() {
    // this.getTableData();
  },
  methods: {
    getAreaValueSz(value) {
      console.log(value, '=======value树');
      this.unitId = value.id;
      this.allpath = value.allpath;
      this.level = value.level;
      this.type = value.type;
      this.name = value.name;
    },
    getAreaValue(value){
    console.log(value, '=======value树');
      //value.type为bm，表明选择的是单位，dq表明选择的是地区
      if (value.type==='bm') {
        this.form.deptId = value.id;
        this.form.areaId = '';
      } else {
        this.form.areaId = value.id;
        this.form.deptId = '';
      }
    },
    // 一打开页面获取列表数据
    getTableData() {
      // 三资
      if (this.isSz) {
        this.loading = true;
        console.log(1111);
        this.tableData = [];
        let data1 = {
          unitId: this.unitId,
          deptName: this.params.organizationName,
          systemCode: this.params.organizationalCode,
          commonTreeDto: {
            unitId: this.unitId,
            allpath: this.allpath,
            level: this.level,
            type: this.type,
            name: this.name,
          },
        };
        this.$ajax({
          url: '/szOtherContract.do?method=querySystemCodeList',
          method: 'post',
          data: data1,
        }).then((res) => {
          if (res.data.code == 0) {
            this.tableData = res.data.data;
            this.loading = false;
          } else {
            this.$message({
              message: res.data.msg,
              type: 'error',
            });
            this.loading = false;
          }
        });
      } else {
        // 贾汪三资
        this.$ajax({
          url: '/dept/findDeptList',
          method: 'get',
          serverName: 'nd-disciplineSupervision',
          data:{
            areaId:this.form.areaId,
            deptId:this.form.deptId,
            deptName:this.params.organizationName,
            systemCode:this.params.organizationalCode,
          }
        }).then((res) => {
          if (res.data.code == 200) {
              this.tableData = res.data.data;
              this.loading = false;
          } else {
            this.$message({
              message: res.data.message,
              type: 'error',
            });
            this.loading = false;
          }
        });
      }
    },
    //打开弹框
    open() {
      this.$refs.identificationCode.open();
      this.getTableData();
    },
    //关闭弹框
    close() {
      // 清空数据表单
      this.params.organizationName = '';
      this.params.organizationalCode = '';
      this.unitId = '';
      this.allpath = '';
      this.level = '';
      this.type = '';
      this.name = '';
      this.$refs.identificationCode.close();
    },
    // 点击上方×按钮
    closeIdentificationCode() {
      this.close();
    },
    // 根据条件查询数据
    search() {
      this.getTableData();
    },
  },
};
</script>

<style lang="scss" scoped>
.search-opea {
  display: flex;
  align-items: center;
  padding-left: 9px;
  padding-top: 17px;
  margin-bottom: 10px;
  .item {
    span {
      margin-right: 6px;
      height: 12px;
      font-size: 12px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #555555;
    }
    display: flex;
    align-items: center;
    margin-right: 20px;
  }
}
.nd-table-box {
  width: 1066px;
  height: 383px;
  padding-left: 9px;
  margin-bottom: 25px;
}

.primary {
  width: 73px;
}
</style>