<!-- 关于我们 -->
<template>
  <div class="wrap-container">
    <div class="main-content" v-html="content"></div>
  </div>
</template>
<script>
export default {
  components: {
  },
  data() {
    return {
      content: '',
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.$ajax({
        url: "/gywm/lxwm/",
        method: 'get',
        serverName: 'nd-ss',
      }).then((r) => {
        console.log(r,"99999999999999999999");
        let newArr = eval(r.data);
        this.$ajax({
          url: this.getPathFromUrl(newArr[0].path),
          method: 'get',
          serverName: 'nd-ss',
        }).then((res) => {
          let data = res.data.replaceAll(
            '<img alt="" src="',
            `<img style="max-width:1078px;display: block;" src="${window.ipConfig.imgUrl}`,
          );
          this.content = data;
        });
      });
    },

    // 截取url中除去协议域名端口号后的内容
    getPathFromUrl(url) {
      const regex = /\/\/[^/]+(.+)/;
      const match = url.match(regex);
      return match ? match[1] : null;
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap-container {
  min-width: 1000px;
  width: 100%;
  overflow: auto;
  height: 100%;
  background: #fff;
  position: relative;
  padding-bottom: 20px;

  .bg {
    width: 100%;
    height: 180px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .main-content {
    width: 100%;
    min-height: 300px;
    background: #fff;
    margin-top: 20px;
    overflow: hidden;
    padding-top: 10px;
  }
}
</style>
