<!-- 关于我们 -->
<template>
  <div class="wrap-container">
    <div class="content">
      <div class="box">
        <div class="title">关于我们</div>
        <div class="time">发布时间：{{ writeTime.slice(0, 4) }}-{{ writeTime.slice(5, 7) }}-{{
                      writeTime.slice(8, 10)
                    }}</div>
        <div class="center">{{title }}</div>
      </div>
      <div class="main-content" v-html="content"></div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      content: '',
      id: '',
      title: '',
      writeTime: '',
      page: {
        pageSize: 10,
        pageNo: 1,
      },
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.$ajax({
        url: '/jcms/site/manuscript/listPage',
        method: 'post',
        serverName: 'nd-ss',
        data: {
          columnId: window.ipConfig.jcms.indexAbout,
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          title: '',
        },
      }).then((r) => {
        if (r.data.code == 200) {
          this.id = r.data.data.records[0].id;
          this.getData(this.id);
        }
      });
    },
    getData(id) {
      console.log(id, 'id');
      this.$ajax({
        url: `/jcms/site/manuscript/getDetailById?id=${id}`,
        method: 'get',
        serverName: 'nd-ss',
      }).then((r) => {
        if (r.data.code == 200) {
          this.content = r.data.data.manuscriptContent;
          this.title = r.data.data.title;
          this.writeTime = r.data.data.writeTime;
        }
      });
    },

    // getData() {
    //   this.$ajax({
    //     url: "/gywm/lxwm/",
    //     method: 'get',
    //     serverName: 'nd-ss',
    //   }).then((r) => {
    //     console.log(r,"99999999999999999999");
    //     let newArr = eval(r.data);
    //     this.$ajax({
    //       url: this.getPathFromUrl(newArr[0].path),
    //       method: 'get',
    //       serverName: 'nd-ss',
    //     }).then((res) => {
    //       let data = res.data.replaceAll(
    //         '<img alt="" src="',
    //         `<img style="max-width:1078px;display: block;" src="${window.ipConfig.imgUrl}`,
    //       );
    //       this.content = data;
    //     });
    //   });
    // },

    // 截取url中除去协议域名端口号后的内容
    getPathFromUrl(url) {
      const regex = /\/\/[^/]+(.+)/;
      const match = url.match(regex);
      return match ? match[1] : null;
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap-container {
  min-width: 1000px;
  width: 1200px;
  margin: 20px auto;
  // width: 100%;
  overflow: auto;
  height: 100%;
  background: #fff;
  position: relative;
  padding-bottom: 20px;
  border: 1px solid #e8e8e8;
  padding: 30px;

  .bg {
    width: 100%;
    height: 180px;
    img {
      width: 100%;
      height: 100%;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    .box {
      display: flex;
      flex-direction: column;
      text-align: center;
      .title {
        font-size: 24px;
        font-weight: bold;
        color: #151515;
        padding-bottom: 20px;
      }
      .time{
        color: #000;
        font-size: 16px;
        padding-bottom: 10px;
        border-bottom: 2px solid #DDDDDD;
      }
      .center{
        padding-top: 10px;
        color: #444;
        font-size: 29px;
        font-weight: bold;
      }
    }
    .main-content {
      width: 100%;
      min-height: 300px;
      background: #fff;
      margin-top: 20px;
      overflow: hidden;
      padding-top: 10px;
    }
  }
}
</style>
