<template>
  <div class="nd-agreement-dialog">
    <el-dialog
      :visible.sync="dialog.visible"
      :width="width"
      :height="height"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="close"
      align-center
      append-to-body :title="title"
      class="nd-agreement-dialog"
    >
      <!-- <div class="my-header">{{ title }} &nbsp;</div> -->
      <div class="main-box" style="height: 300px;width: 100%;overflow-y: scroll;">
        <div class="item-main ql-editor" v-html="htmlContent"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
    props: {
      title: {
            type: String,
            default: "",
        },
        width: {
                type: String,
    default: "50%",
        },
        height: {
                type: String,
    default: "300px",
        },
    },
    data() {
        return {
          dialog: {
            visible:false
          },
          title:'',
          htmlContent:'',
        };
    },
    methods:{
      open(data, content){
          this.title = data;
          this.htmlContent = content;
          this.dialog.visible = true;
      },
      close(){
          this.dialog.visible = false;
      }
    }
    }
// import ndButton from "@/commponents/ndButton.vue";
// import { reactive, ref } from "vue";

// const emit = defineEmits(["isAgree"]);

// const props = defineProps({
//   // 标题
//   title: {
//     type: String,
//     default: " ",
//   },
//   // 弹窗宽度
//   width: {
//     type: String,
//     default: "50%",
//   },
//   // 弹窗高度
//   height: {
//     type: String,
//     default: "auto",
//   },
// });
// let dialog = reactive({
//   visible: false,
// });
// let title = ref("");
// let htmlContent = ref("");
// // 打开弹窗
// function open(data, content) {
//   title.value = data;
//   htmlContent.value = content;
//   dialog.visible = true;
// }

// // 关闭弹窗
// function close() {
//   dialog.visible = false;
// }

// defineExpose({
//   open,
//   close,
// });
</script>

<style lang="scss">
.nd-agreement-dialog {
  .el-dialog__headerbtn .el-dialog__close {
  color: #fff; /* 自定义颜色 */
  position: relative;
  top: 12px;
}
.el-dialog__headerbtn:hover .el-dialog__close {
  color: #fff ;
}
  .el-dialog__header {
    padding: 16px;
    margin: 0;
    padding-bottom: 10px;
    background: #11bb00;
    span{
      color: #fff;
    }

    .my-header {
      font-family: Microsoft YaHei;
      font-size: 18px;
      font-weight: bold;
      color: #3d3d3d;
    }
  }

  .el-dialog__body {
    padding: 16px;
    margin: 0;
    padding-top: 0;

    .main-box {
      img{
            width: 100%;
            height: auto;
        }
      .main-item {
        border: 1px solid #eeeeee;
        .item-title {
          width: 100%;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f7fa;
          border-bottom: 1px solid #eeeeee;

          font-family: Microsoft YaHei;
          font-size: 16px;
          font-weight: bold;
          color: #333333;
        }

        .item-main {
          width: 100%;
          height: 300px;
          overflow-y: auto;
          padding: 15px;

          .webview {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  .el-dialog__headerbtn {
    top: 2px;
  }

  .el-dialog__footer {
    padding: 0 15px 15px 15px;
    display: flex;
    justify-content: center;
  }
}
</style>

<style lang="scss" scoped></style>
