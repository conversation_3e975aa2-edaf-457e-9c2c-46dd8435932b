<template>
  <div class="sponsor">
    <div id="box">
      <div class="sponsor-item-box">
        <div class="sponsor-item"><img src="@/assets/index/item4.png" alt="" @click="skipAddress('https://www.bjraee.com/index/ ')"/></div>
        <div class="sponsor-item"><img src="@/assets/index/item5.png" alt="" @click="skipAddress('https://www.wznccq.com/#/')" /></div>
        <div class="sponsor-item"><img src="@/assets/index/item6.png" alt="" @click="skipAddress('http://www.jsnc.gov.cn/')" /></div>
        <div class="sponsor-item"><img src="@/assets/index/item7.png" alt="" @click="skipAddress('https://www.cdaee.com/www/')" /></div>
        <div class="sponsor-item"><img src="@/assets/index/item8.png" alt="" @click="skipAddress('https://www.hzaee.com/ ')" /></div>
        <div class="sponsor-item"><img src="@/assets/index/item9.png" alt="" @click="skipAddress('https://www.whnccq.com/')" /></div>
      </div>
      <div class="sponsor-item-box">
        <div class="sponsor-item"><img src="@/assets/index/item4.png" alt="" @click="skipAddress('https://www.bjraee.com/index/ ')" /></div>
        <div class="sponsor-item"><img src="@/assets/index/item5.png" alt="" @click="skipAddress('https://www.wznccq.com/#/')" /></div>
        <div class="sponsor-item"><img src="@/assets/index/item6.png" alt="" @click="skipAddress('http://www.jsnc.gov.cn/')" /></div>
        <div class="sponsor-item"><img src="@/assets/index/item7.png" alt="" @click="skipAddress('https://www.cdaee.com/www/')" /></div>
        <div class="sponsor-item"><img src="@/assets/index/item8.png" alt="" @click="skipAddress('https://www.hzaee.com/ ')" /></div>
        <div class="sponsor-item"><img src="@/assets/index/item9.png" alt="" @click="skipAddress('https://www.whnccq.com/')" /></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  mounted() {
    this.infinitescRol(true);
  },
  // 销毁定时器
  beforeDestroy() {
    this.infinitescRol(false);
  },
  deactivated(){
    this.infinitescRol(false);
  },
  data() {
    return {
      roll: null,
    };
  },
  methods: {
    // 无缝滚动
    infinitescRol(flage) {
      this.$nextTick(() => {
        let that = this;
        if (flage) {
          let width = document.getElementsByClassName('sponsor-item-box')[0].offsetWidth;
          let a = 0;
          function rollStart() {
            clearInterval(that.roll);
            that.roll = setInterval(() => {
              document.getElementsByClassName('sponsor-item-box')[0].style.left = a + 'px';
              document.getElementsByClassName('sponsor-item-box')[1].style.left = a + width + 'px';
              a--;
              if (a <= -width) {
                a = 0;
              }
            }, 10);
          }
          rollStart();
          document.getElementById('box').onmouseenter = function () {
            clearInterval(that.roll);
          };

          document.getElementById('box').onmouseout = function () {
            rollStart();
          };

          document.querySelectorAll('.sponsor-item img').forEach((item) => {
            item.onmouseenter = function () {
              clearInterval(that.roll);
            };

            item.onmouseout = function () {
              rollStart();
            };
          });
        } else {
          clearInterval(that.roll);
        }
      });
    },
    skipAddress(url){
      window.open(url)
    },
  },
};
</script>

<style lang="scss" scoped>
.sponsor {
  padding: 40px 0 15px 0;
  height: calc(70px + 40px + 15px);
  background: #3b3b3b;
  width: 100%;
  overflow: hidden;

  > div {
    position: relative;
    max-width: 1920px;
    margin: auto;
    height: 70px;
    overflow: hidden;

    > div {
      position: absolute;
      // left: 50%;
      // transform: translateX(-50%);
      // left: 0%;
      // transform: translateX(-15%);
      display: flex;
      justify-content: center;
      cursor: pointer;
    }
  }

  #box {
    position: relative;

    &::before {
      // color: transparent;
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 80px;
      background: linear-gradient(90deg, #3b3b3b, transparent);
      z-index: 999;
    }

    &::after {
      // color: transparent;
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      height: 100%;
      width: 80px;
      background: linear-gradient(-90deg, #3b3b3b, transparent);
      z-index: 999;
    }
  }

  .sponsor-item {
    width: 290px;
    max-width: 290px;
    min-width: 290px;
    height: 70px;
    margin-right: 20px;

    // &:nth-last-of-type(1) {
    //   margin-right: 0;
    // }

    img {
      width: 100%;
      height: 100%;
      display: block;
    }
  }
}
</style>