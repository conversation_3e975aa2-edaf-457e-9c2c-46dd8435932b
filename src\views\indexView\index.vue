<template>
  <div>
    <div >
      <!-- 开发 -->
      <!-- <dataPanel /> -->
      <!-- 新闻中心 -->
      <messageCenter  />
      <!-- 交易公告 -->
      <projectInformationView  />
      <!-- 成交公告 -->
      <transactionAnnouncementView  />
      <!-- 联合招商 -->
      <deal-information-vue @getDealInformation="getDealInformation" ></deal-information-vue>
      <!-- 供需大厅 -->
      <supplyHall   @getDealInformation="getDealInformation"/>
      <!-- 新闻中心 -->
      <!-- <news></news> -->
    </div>
  </div>
</template>

<script>
import dataPanel from './components/dataPanel.vue';
import news from './components/news.vue';
import projectInformationView from './components/projectInformationView.vue';
import transactionAnnouncementView from './components/transactionAnnouncementView.vue';
import projectRecommendation from './components/projectRecommendation.vue';
import policies from './components/policies.vue';
import sponsor from './components/sponsor.vue';
import dealInformationVue from './components/dealInformation.vue';
import supplyHall from './components/supplyHall.vue';
import messageCenter from './components/messageCenter.vue';

export default {
  components: {
    dataPanel,
    news,
    projectInformationView,
    transactionAnnouncementView,
    projectRecommendation,
    policies,
    sponsor,
    dealInformationVue,
    messageCenter,
    supplyHall,
  },
  data() {
    return {};
  },
  methods: {
    getDealInformation(val) {
      this.$emit('getDealInformation', val)
    }
  }
};
</script>
<style lang="scss" scoped></style>