<template>
    <!-- 此文件，只做图片的压缩功能（IE浏览器暂不处理） -->
</template>
<script>
export default {
    components: {},
    data() {
        return {};
    },
    mounted() { },
    methods: {
        //处理图片异步压缩逻辑
        beforcompressImg(file) {
            let _this = this;
            if (this.IEVersion() == '-1') {
                // 不是IE浏览器处理压缩图片
                return new Promise((resolve, reject) => {
                    //获取文件名和大小
                    let fileSize = parseFloat(parseInt(file.size) / 1024 / 1024).toFixed(2);

                    //获取文件格式
                    let fileTypeLen = file.type.split("/");
                    let fileType = fileTypeLen[fileTypeLen.length - 1].toLowerCase(); //转化成小写

                    //如果是PDF文件，不做压缩，直接返回
                    if (fileType === "pdf") {
                        resolve(file);
                    } else {
                        if (fileSize < 1) { // 小于1M不压缩,直接返回
                            resolve(file);
                        } else {
                            //将图片压缩
                            _this.compressImg(file).then((fileNew) => {
                                //压缩完成
                                fileNew.uid = file.uid
                                resolve(fileNew);
                            }, (reason) => {
                                //压缩失败
                                reject(reason);
                            })
                        }
                    }
                });
            } else {
                //IE浏览器暂不做压缩处理，直接返回
                this.$emit("getCompressFile", file);
            }
        },
        //压缩图片
        compressImg(file) {
            let _this = this;

            let files;

            let fileSize = parseFloat(parseInt(file.size) / 1024 / 1024).toFixed(2);

            let read = new FileReader();

            read.readAsDataURL(file);

            return new Promise(function (resolve, reject) {

                read.onload = function (e) {

                    let img = new Image();

                    try {
                        img.src = e.target.result;
                    } catch (e) {
                        reject(e);
                    }

                    img.onload = function () {

                        //默认按比例压缩
                        let tempW = this.width
                        let tempH = this.height
                        function scaleFn(num) {
                            if (num > 4000) {
                                tempW = tempW / 10
                                tempH = tempH / 10
                                scaleFn(num / 10)
                            }
                        }
                        scaleFn(tempW)
                        scaleFn(tempH)
                        console.log(tempW, tempH);

                        let w = tempW,

                            h = tempH;

                        //生成canvas

                        let canvas = document.createElement('canvas');

                        let ctx = canvas.getContext('2d');

                        let base64;

                        // 创建属性节点

                        canvas.setAttribute("width", w);

                        canvas.setAttribute("height", h);

                        try {
                            ctx.drawImage(this, 0, 0, w, h);
                        } catch (e) {
                            reject(e);
                        }

                        if (fileSize < 1) {

                            //如果图片小于一兆 那么不执行压缩操作

                            base64 = canvas.toDataURL("image/jpeg", 0.8);

                        } else if (fileSize > 1 && fileSize < 2) {

                            //如果图片大于1M并且小于2M 那么压缩0.5

                            base64 = canvas.toDataURL("image/jpeg", 0.5);

                        } else {

                            //如果图片超过2m 那么压缩0.3

                            base64 = canvas.toDataURL("image/jpeg", 0.3);

                        }

                        // 回调函数返回file的值（将base64编码转成file）
                        let isIE = _this.IEVersion();
                        if (isIE == '-1') { // 不是IE浏览器
                            files = _this.dataURLtoFile(base64, file.name); //如果后台接收类型为base64的话这一步可以省略
                        } else {
                            // 释放内存
                            CollectGarbage();
                            img.src = null;
                            $(img).remove();

                            files = _this.dataURLtoFileIE(base64, file.name); //如果后台接收类型为base64的话这一步可以省略
                        }
                        resolve(files);
                    };
                };
            })
        },
        //base64转码（压缩完成后的图片为base64编码，这个方法可以将base64编码转回file文件）
        dataURLtoFile(dataurl, filename) {
            let arr = dataurl.split(','),

                mime = arr[0].match(/:(.*?);/)[1],

                bstr = atob(arr[1]),

                n = bstr.length,

                u8arr = new Uint8Array(n);

            while (n--) {

                u8arr[n] = bstr.charCodeAt(n);

            }

            return new File([u8arr], filename, { type: mime });
        },
        //base64转码IE版（压缩完成后的图片为base64编码，这个方法可以将base64编码转回file文件）
        dataURLtoFileIE(dataurl, filename) {
            let arr = dataurl.split(','),

                mime = arr[0].match(/:(.*?);/)[1],

                bstr = atob(arr[1]),

                n = bstr.length,

                u8arr = new Uint8Array(n);

            while (n--) {

                u8arr[n] = bstr.charCodeAt(n);

            }

            let theBlob = new Blob([u8arr], { type: mime });

            theBlob.lastModifiedDate = new Date();

            theBlob.name = filename;

            return theBlob;
        },
        //判断浏览器类型
        IEVersion() {
            let userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
            let isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
            let isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
            let isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
            let chorome = userAgent.indexOf('Chrome') > -1;
            if (isIE) {
                let reIE = new RegExp("MSIE (\\d+\\.\\d+);");
                reIE.test(userAgent);
                let fIEVersion = parseFloat(RegExp["$1"]);
                if (fIEVersion == 7) {
                    return 7;
                } else if (fIEVersion == 8) {
                    return 8;
                } else if (fIEVersion == 9) {
                    return 9;
                } else if (fIEVersion == 10) {
                    return 10;
                } else {
                    return 6;//IE版本<=7
                }
            } else if (isEdge) {
                return 'edge';//edge
            } else if (isIE11) {
                return 11; //IE11
            } else {
                //不是ie浏览器
                return -1;
                // if(chorome){
                //     //谷歌浏览器
                //     return 'chrome'
                // }else{
                //     //除谷歌外，其他浏览器
                //     return -1;
                // }
            }
        },
    },
}
</script>

<style lang="scss" scoped></style>