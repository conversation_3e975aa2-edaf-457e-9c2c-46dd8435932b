<template>
  <nd-dialog title="科目选择" ref="dialog" width="650px" append-to-body :tab="true" :before-close="handleClose" center>
    <div v-loading="loading">
      <div class="header-top-box" >
      <div v-if="subjectTemplate" class="subject-template-box">
        <div class="subject-template-box-text">科目模板</div>
        <nd-select v-model="subjectTemplateData" @change="chooseSubjectTemplate" width="260px">
          <el-option v-for="(item, index) in organizationTypeData" :key="index" :label="item.deptName"
            :value="item.deptValue" />
        </nd-select>
      </div>
      <div class="subject-level-box">
        <div class="subject-level-box-text">科目级次</div>
        <nd-select v-model="subjectLevelData" @change="chooseSubjectLevel" width="170px">
          <el-option v-for="(item, index) in subjectLevelDataList" :key="index" :label="item.value" :value="item.key" />
        </nd-select>
      </div>
    </div>
    <!-- 取自科目树 -->
    <nd-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="(item, index) in subjectTabs" :key="index" :label="item.dataValue" :name="item.dataKey">
      </el-tab-pane>
    </nd-tabs>
    <div>
      <div class="searchInput">
        <nd-input placeholder="输入科目代码或科目名称进行查询" v-model="filterText" @input="filterTextInput()" clearable width="280px"
          @keyup.enter.native="enterFun">
        </nd-input>
        <nd-button type="primary" @click="enterFun" class="search-button">搜索</nd-button>
      </div>
      <div v-for="item in checkAllList">
        <el-checkbox class="check-all-box" v-if="activeName === item.key" :indeterminate="item.isIndeterminate"
          v-model="item.checkAll" @change="handleCheckAllChange(item.key)">全选</el-checkbox>
      </div>
      <nd-tree :data="KmTree" :props="defaultProps" :filter-node-method="filterNode"
        :key="resertTree" ref="assetTree" class="TreeHeigh" highlight-current @node-expand="handleCheckChange2"
        @node-click="handleCheckChange" @node-dblclick="doubleClickTree" @keydown.native.enter="enter()"
        @check="checkChange" :render-content="renderContent" node-key="id" :default-expanded-keys="isLeafArry"
        :default-checked-keys="isCheckedArry" :show-checkbox="this.showCheckbox" :check-strictly="true">
      </nd-tree>
    </div>
    </div>
    <template v-slot:footer>
      <div class="footer-in-box">
        <div class="footer-in-checkBox">
          <el-checkbox :key="isChooseDialogKey" v-model="isChooseDialogCheck" width="auto"
            @change="dialogCheckBoxAll()">全选
          </el-checkbox>
          <div style="margin-left:20px">
            共选中
            <span style="color:red">{{ ALLCheckNumber }}</span>
            条
          </div>
        </div>
        <div>
          <nd-button @click="enter" type="primary">确定</nd-button>
          <nd-button @click="close">关闭</nd-button>
        </div>
      </div>
    </template>
  </nd-dialog>
</template>
<script>
import ndDialog from '@/components/ndDialog.vue';
import ndButton from '@/components/ndButton.vue';
import ndTabs from '@/components/ndTabs.vue';
import ndInput from '@/components/ndInput.vue';
import ndSelect from '@/components/ndSelect.vue';
import ndTree from '@/components/ndTree.vue';
export default {
  components: {
    ndDialog,
    ndButton,
    ndTabs,
    ndInput,
    ndTree,
    ndSelect,
  },
  props: {
    selectData: Object,
    //subject-template 是否展示科目模板选项框
    subjectTemplate: {
      type: Boolean,
      default: false
    },
    //show-checkbox是否展示多选框
    showCheckbox: {
      type: Boolean,
      default: false
    },
    //check-strictly在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 false
    checkStrictly: {
      type: Boolean,
      default: false
    },
    //account-set是否需要账套
    accountSet: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      checkAllList: [
        {
          key: '1',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '2',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '3',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '4',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '5',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }
      ],
      resertTree: 0,
      jspUrl: '',
      activeName: '1',
      filterText: '',
      KmTree: [], //更多数据
      KmSelect: [], //select下拉数据
      subjectTabs: [],//科目类型tab
      kmType: '1', //科目类型
      subjectTemplateData: "4",//科目模板数据
      kemuNode: [],//父页面已选中集合
      // kmIdArray: [],
      // kmCodeArray: [],
      // kmNameArray: [],
      TreeChechValue: {
        key: '',
        label: '',
      },
      organizationTypeData: [],
      subjectLevelData: "1",
      subjectLevelDataList: [],
      checkBoxChechLabel: {},
      treeRef: '1',
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'leaf',
      },
      ztId: '',
      loading: true,
      fzhsChildArr: [], //TODO:辅助核算子集
      isLeafArry: [],
      isCheckedArry: [],
      kmIndex: '', // 有的科目是需要列表的，记录是修改的列表中对应下标的数据
      kmCode: '', //编辑页面的kmcode
      id: '', //所选的科目id
      isChooseDialogCheck: false,
      isChooseDialogKey: 0,
      ALLCheckNumber: 0,
      allKMNum: 0,
      isVisible: false,
      isCheckAll: true,
      isCheckAllRE: false,
    };
  },
  mounted() {
    // this.getKmlist();
    this.$nextTick(() => {
      //  console.log(this.$parent.$parent.$parent.ztId,'ztId1');
    });
  },
  beforeMount() {
    //  console.log(this.$parent.$parent.$parent.ztId,'ztId2');
  },
  watch: {
    filterText(val) {
      let treeRef = this.treeRef
      console.log(this.treeRef);
      switch (treeRef) {
        case '1':
          // console.log(this.$refs.assetTree.$refs.ndTree);
          this.$refs.assetTree.$refs.ndTree.filter(val)
          break;
        case '2':
          this.$refs.liabilityTree.$refs.ndTree.filter(val)
          break;
        case '3':
          this.$refs.RightsTree.$refs.ndTree.filter(val)
          break;
        case '4':
          this.$refs.costTree.$refs.ndTree.filter(val)
          break;
        case '5':
          this.$refs.IncomeTree.$refs.ndTree.filter(val)
          break;
        default:
          break;
      }
    }
  },
  methods: {
    filterNode(value, data, node) {
      let treeRef = this.treeRef
      if (treeRef) {
        switch (treeRef) {
          case '1':
            this.filterNotShowData(node, data, this.$refs.assetTree.$refs.ndTree.root.childNodes)
            break;
          case '2':
            this.filterNotShowData(node, data, this.$refs.liabilityTree.$refs.ndTree.root.childNodes)
            break;
          case '3':
            this.filterNotShowData(node, data, this.$refs.RightsTree.$refs.ndTree.root.childNodes)
            break;
          case '4':
            this.filterNotShowData(node, data, this.$refs.costTree.$refs.ndTree.root.childNodes)
            break;
          case '5':
            this.filterNotShowData(node, data, this.$refs.IncomeTree.$refs.ndTree.root.childNodes)
            break;
          default:
            break;
        }
      }
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    filterNotShowData(node, data, childNodes) {
      for (let i in childNodes) {
        if (childNodes[i].id == node.id) {
          // console.log(node.id, data.kmName, data.fzhsType);
          if (data.fzhsType) {
            this.$nextTick(() => {
              childNodes[i].expanded = false
            });
          }
        } else {
          if (childNodes[i].childNodes && childNodes[i].childNodes.length > 0) {
            for (let j in childNodes[i].childNodes) {
              this.filterNotShowData(node, data, childNodes[i].childNodes)
            }
          }
        }
      }
    },
    //获取组织类型 
    getDeptType() {
      this.$ajax({
        url: "/api-dept/getDeptType.do",
        method: "POST",
        data: { requestFrom: '2' },
      }).then((res) => {
        if (res.data.code === 0) {
          console.log(res, '/api-dept/getDeptType.do');
          this.organizationTypeData = res.data.data
          this.accessToCourseGrades('4')
        }
      })
    },
    // 获取科目级次 
    accessToCourseGrades(type) {
      this.$ajax({
        url: "/tjSubjectInfo/accessToCourseGrades.do",
        method: "POST",
        data: { orgType: type },
      }).then((res) => {
        if (res.data.code === 0) {
          this.subjectLevelDataList = res.data.data
        }
      })
    },
    // 搜索
    enterFun() {
      this.KmTree = [];
      this.getKmlist(this.kmType, this.filterText, this.subjectLevelData);
    },

    //获取科目选择的科目类型数据类型 
    getDataDictionary() {
      this.$ajax({
        method: 'post',
        url: '/tjSubjectInfo/getDataDictionary.do',
        data: { baseType: "KMLX" },
      }).then((res) => {
        if (res.data.code === 0) {
          console.log(res, "获取科目选择的科目类型数据类型");
          this.subjectTabs = res.data.data
          this.subjectTabs.forEach(item => {
            Object.assign(item, {
              ischeckKmId: [],
              ischeckKmCode: [],
              ischeckKmName: [],
            });
          });
          // console.log(this.kemuNode, "this.kemuNodethis.kemuNodethis.kemuNode");
          if (this.kemuNode) {
            this.subjectTabs.forEach(item => {
              this.kemuNode.forEach(element => {
                if (item.id == element.kmType) {
                  item.ischeckKmId.push(element.id)
                  item.ischeckKmCode.push(element.kmCode)
                  item.ischeckKmName.push(element.kmName)
                }
              })
            });
          }
          if (this.accountSet) {
            this.getKmlist(1, "", "1")
            this.kmType = 1
          } else {
            this.defaultProps = {
              children: 'children',
              label: 'label',
              isLeaf: 'leaf',
            },
              this.kmType = this.subjectTabs[0].id
            this.isCheckedArry = this.subjectTabs[0].ischeckKmId
            //判断是否全部选中
            this.judgeAllSelected()
            this.getKmlist(this.subjectTabs[0].id, "", "1")
          }
        }
      })
    },
    //判断是否全部选中
    judgeAllSelected() {
      let isChooseNum = 0
      this.subjectTabs.forEach(item => {
        isChooseNum += item.ischeckKmId.length
      });
      // console.log(isChooseNum,"isChooseNumisChooseNumisChooseNum");
      if (isChooseNum === this.allKMNum) {
        this.isChooseDialogCheck = true
      } else {
        this.isChooseDialogCheck = false
      }
    },
    isLeafFun(data) {
      data.map((item) => {
        if (item.isLeaf === true && item.fzhsType) {
          //如果有子集并且是辅助核算
          item.children = [
            {
              children: '',
              label: '',
              isLeaf: '',
            },
          ];
        }
      });
    },
    // 辅助核算
    isFzhsFun(data) {
      data.map((item) => {
        if (item.children) {
          if (item.children.isLeaf === true && item.fzhsType) {
            item.children = [
              {
                children: '',
                label: '',
                isLeaf: true,
                fzhsType: '',
              },
            ];
          }
          this.isFzhsFun(item.children);
        }
      });
    },
    // 有子节点并且不是辅助核算
    openTree(data, level) {
      if (!this.checkStrictly) {
        data.map((item) => {
          this.checkAllList[Number(this.activeName) - 1].allNumber += 1
          // console.log(item, "item1111");
          if (item.children !== null && (item.fzhsType == null || item.fzhsType == '')) {
            if (item.kmLevel < level) {
              this.isLeafArry.push(item.id);
            }
            this.openTree(item.children, level);
          }
        });
      } else {
        data.map((item) => {
          this.checkAllList[Number(this.activeName) - 1].allNumber += 1
          if (item.children.length != 0) {
            if (item.kmLevel < level) {
              this.isLeafArry.push(item.id);
            }
            this.openTree(item.children, level);
          }
        })
      }
    },
    // 选择科目模板
    chooseSubjectTemplate(val) {
      this.clearAll()
      this.filterText=""
      this.loading = true;
      this.subjectTemplateData = val
      this.getKmlist(this.kmType, "", this.subjectLevelData)
    },
    //选择科目级次
    chooseSubjectLevel(val) {
      this.clearAll()
      this.filterText=""
      this.isLeafArry = []
      this.loading = true;
      this.subjectLevelData = val
      this.getKmlist(this.kmType, "", this.subjectLevelData)
      // if (this.subjectLevelData != '5') {
      //   this.getKmlist(this.kmType, "", this.subjectLevelData).then((res) => {
      //     console.log(this.checkAllList, "this.checkAllListthis.checkAllListthis.checkAllList");
      //   })
      // }
    },
    // 打开
    async open(id, kmCode, index, kemuNode) {
      await this.getDeptType()
      await this.getAllNodeNum()
      this.$refs.dialog.open(); //打开弹框
      if (index != null && index != '') {
        this.kmIndex = index;
      }
      this.activeName = '1';
      this.ztId = id;
      this.kmCode = kmCode;
      this.kemuNode = kemuNode
      // console.log(this.kemuNode, "this.kemuNodethis.kemuNode1");
      await this.getDataDictionary()
      // 执行匹配功能
      const findTreeDataArry = this.findItemById(this.ztId, this.KmTree);
    },

    //获取所有科目的数量
    getAllNodeNum() {
      this.$ajax({
        method: 'post',
        url: '/tjSubjectInfo/forInformationOnAllSubjects.do',
        data: {
          orgType: this.subjectTemplateData,
          courseGrades: this.subjectLevelData,
        }
      }).then((res) => {
        if (res.data.code === 0) {
          this.allKMNum = res.data.data.length
        }
      })
    },

    // 获取树形数据
    getKmlist(kmType, filterText, level) {

      this.checkAllList[Number(this.activeName) - 1].allNumber = 0
      this.KmTree = []
      return new Promise((resolve, reject) => {
        // this.$message.warning(this.subjectLevelData)
        if (this.accountSet) {
          this.$ajax({
            method: 'post',
            url: '/szPayTypeConfig/getKmMoreList.do',
            data: { kmType: kmType, id: this.ztId, kmContent: filterText },
          })
            .then((response) => {
              // console.log(response.data.data, "获取到的值");
              const resArry = response.data.data;
              if (resArry) {
                this.changeId1(resArry, 'kmCode', 'kmName', 'label');
                this.changeId2(resArry, 'id', 'value');
                console.log(resArry, '修改后的值');
                this.KmTree = resArry;
              } else {
                this.KmTree = resArry;
              }
              this.KmTree.map((item) => {
                this.isLeafFun(this.KmTree);
                // 如果有子节点并且不是辅助核算默认展开
                // 1.20 如果有子节点按照科目级次 默认展示一级
                this.openTree(this.KmTree, level);
              });
              setTimeout(() => {
                this.loading = false;
              }, 500);
              resolve(true);
            })
            .catch((err) => {
              resolve(true);
              setTimeout(() => {
                this.loading = false;
              }, 500);
            });
        } else {
          this.$ajax({
            method: 'post',
            url: '/tjSubjectInfo/subjectInformation.do',
            data: { orgType: this.subjectTemplateData, kmType: kmType, kmCodeOrKmName: filterText, courseGrades: this.subjectLevelData },
          })
            .then((response) => {
              console.log(response.data.data, "获取到的值");
              const resArry = response.data.data;
              if (resArry) {
                this.changeId1(resArry, 'kmCode', 'kmName', 'label');
                console.log(resArry, '修改后的值');
                this.KmTree = resArry;
              } else {
                this.KmTree = resArry;
              }
              this.openTree(this.KmTree, level);
              //判断是否被全选
              this.countCheckNum()
              this.isVisible = false
              this.isCheckAll = true
              this.isCheckAllRE = false
              this.$nextTick(() => {
                this.isCheckAllNode(null).then((res) => {
                  if (this.isCheckAll) {
                    this.checkAllList[Number(this.activeName) - 1].checkAll = true
                    this.checkAllList[Number(this.activeName) - 1].isIndeterminate = false
                  } else {
                    if (this.subjectTabs[Number(this.activeName) - 1].ischeckKmId.length > 0) {
                      this.checkAllList[Number(this.activeName) - 1].checkAll = false
                      this.checkAllList[Number(this.activeName) - 1].isIndeterminate = true
                    } else {
                      this.checkAllList[Number(this.activeName) - 1].checkAll = false
                      this.checkAllList[Number(this.activeName) - 1].isIndeterminate = false
                    }
                  }
                })
                this.judgeAllSelected()
              });

              setTimeout(() => {
                this.loading = false;
              }, 1000);
              resolve(true);
            })
            .catch((err) => {
              resolve(true);
              setTimeout(() => {
                this.loading = false;
              }, 1000);
            });
        }

      });
    },
    findItemById(id, list) {
      //第一个参数是要查询的值，第二个参数是树
      let res = list.find((item) => item.id == id);
      if (res) {
        return res;
      } else {
        for (let i = 0; i < list.length; i++) {
          if (list[i].children instanceof Array && list[i].children.length > 0) {
            res = this.findItemById(id, list[i].children);
            if (res) {
              return res;
            }
          }
        }
        return null;
      }
    },
    // 改变树形结构的key
    changeId1(objAry, key, key2, newkey) {
      console.log(objAry,'111111');
      objAry.forEach((item) => {
        console.log(item,"2222");
        Object.assign(item, {
          children: item.child ? item.child : [],
          [newkey]: item[key] + ' ' + item[key2],
          leaf: (!item.child || item.child.length != 0) ? false : true
        });
        //  delete item[key];
        //  delete item[key2];
        // if (item.children != null) {
        //   this.changeId1(item.children, key, key2, newkey);
        // }
        if (item.children.length != 0) {
          this.changeId1(item.children, key, key2, newkey);
        }
      });
      console.log(objAry);
    },
    // 改变树形结构的key
    changeId2(objAry, key, newkey) {
      objAry.forEach((item) => {
        Object.assign(item, {
          [newkey]: item[key],
        });
        //  delete item[key];

        if (item.children != null) {
          this.changeId2(item.children, key, newkey);
        }
        // if (item.child.length != 0) {
        //   this.changeId2(item.child, key, key2, newkey);
        // }
      });
    },
    // 将树形结构转换成list
    treeToArr(data, pid = null, res = []) {
      data.forEach((item) => {
        res.push({ pid: pid, value: item.value, label: item.label });
        if (item.children && item.children.length !== 0) {
          this.treeToArr(item.children, item.id, res);
        }
      });
      return res;
    },
    // 调用树形转换函数，将树转成list
    getSelect() {
      // console.log(this.treeToArr(this.KmTree));
      this.KmSelect = this.treeToArr(this.KmTree);
    },

    //tabs切换
    handleClick(tab, event) {
      console.log(tab, "切换");
      this.kmType = (Number(tab.index) + 1).toString()
      this.loading = true;
      this.isLeafArry = [];
      this.filterText = "";
      this.subjectTabs.forEach(element => {
        if (element.dataKey === tab.name) {
          if (this.accountSet) {
            this.getKmlist(Number(tab.name), "", this.subjectLevelData); //重新获取树得数据
            this.kmType = Number(tab.name);
          } else {
            this.getKmlist(element.id, "", this.subjectLevelData); //重新获取树得数据
            this.kmType = element.id
            this.isCheckedArry = element.ischeckKmId
          }
          return;
        }
      });
    },
    getServerPath() {
      if (typeof getContextPath === 'function') {
        // console.log("获得服务器路径:" + getContextPath());
        return getContextPath();
      } else {
        return '/sz_product_new';
      }
    },
    // 单击树选择
    handleCheckChange(data, e) {
      if (!this.showCheckbox) {
        let parentData = e.parent.data;
        console.log(data, 'data');
        this.id = data.id;
        // 结束
        this.TreeChechValue = {
          key: data.value,
          label: `${data.kmCode} ${data.fullKmName}`, //判断选中是否是一级节点 ，如果是直接返回 是子节点的话将父节点label拼接上
          kmCode: data.kmCode,
          kmType: data.kmType,
          child: data.isLeaf,
        };
        //console.log(this.TreeChechValue, "9999");
        // this.filterText=this.TreeChechValue.label //选择树节点后显示在input输入框内
      }

    },
    // 展开辅助核算节点
    handleCheckChange2(data, e) {
      let parentData = e.parent.data;
      console.log(data, 'data');
      // 如果有是辅助核算请求子集
      if (data.isLeaf === true && data.fzhsType) {
        this.$ajax({
          method: 'post',
          url: '' + '/apiKm/getKmMoreList.do',
          data: {
            kmType: this.kmType,
            ztId: this.ztId,
            kmContent: this.filterText,
            kmId: data.id,
          },
        })
          .then((response) => {
            // console.log(response.data.data, "获取到的值");
            const resArry = response.data.data;
            this.changeId1(resArry, 'kmCode', 'kmName', 'label');
            this.changeId2(resArry, 'id', 'value');
            console.log(resArry, '修改后的值22');
            data.children = resArry;
            console.log(this.KmTree, 'KmTree');
            this.loading = false;
            resolve(true);
          })
          .catch((err) => {
            resolve(true);
            this.loading = false;
          });
      }

      // 结束
      this.TreeChechValue = {
        key: data.value,
        label: `${data.kmCode} ${data.fullKmName}`, //判断选中是否是一级节点 ，如果是直接返回 是子节点的话将父节点label拼接上
        kmCode: data.kmCode,
        kmType: data.kmType,
      };
      console.log(this.TreeChechValue, '9999');
      // this.filterText=this.TreeChechValue.label //选择树节点后显示在input输入框内
    },
    // 查找节点
    // filterNode(value, data) {
    //   if (!value) return true;
    //   return data.label.indexOf(value) !== -1;
    // },
    //内部全选操作
    handleCheckAllChange(key) {
      let ischeckAll = this.checkAllList[Number(key) - 1].checkAll
      // console.log(this.checkAllList, "ischeckAll1");
      let filterNode = []
      this.filterCheckItemTree(this.$refs.assetTree.$refs.ndTree.store.root.childNodes, filterNode, ischeckAll).then((list) => {
        // console.log(list, "handleCheckAllChange");
        //拿到所有已选中或者已取消的节点
        this.getTabAllIdNode(list, ischeckAll).then((res) => {
          // console.log(res);
          this.subjectTabs.forEach((element) => {
            if (this.kmType == element.id) {
              if (ischeckAll) {
                res.checkedKeys.forEach(element1 => {
                  element.ischeckKmId.push(element1)
                });
                element.ischeckKmId = [...new Set(element.ischeckKmId)]
                res.checkedKmCodeKey.forEach(element2 => {
                  element.ischeckKmCode.push(element2)
                });
                element.ischeckKmCode = [...new Set(element.ischeckKmCode)]
                let ischeckKmNameNew = []
                res.checkedKmNameKey.forEach(element3 => {
                  ischeckKmNameNew.push(element3)
                });
                element.ischeckKmName = [...new Set(element.ischeckKmName)]
                this.isCheckedArry = element.ischeckKmId
              } else {
                let NewKmId = element.ischeckKmId.filter(item => {
                  return res.checkedKeys.every(item2 => {
                    return item != item2;
                  })
                })
                element.ischeckKmId = NewKmId

                let NewKmCode = element.ischeckKmCode.filter(item3 => {
                  return res.checkedKmCodeKey.every(item4 => {
                    return item3 != item4;
                  })
                })
                element.ischeckKmCode = NewKmCode

                let NewKmName = element.ischeckKmName.filter(item5 => {
                  return res.checkedKmNameKey.every(item6 => {
                    return item6 != item6;
                  })
                })
                element.ischeckKmName = NewKmName
                this.isCheckedArry = element.ischeckKmId
              }
            }
          })
          this.countCheckNum()
          this.$nextTick(() => {
            let isCheckAll = true
            let isCheckAllRE = false
            let isVisible = false
            this.isCheckAllNode(null).then((res) => {
              if (this.isCheckAll) {
                this.checkAllList[Number(this.activeName) - 1].isIndeterminate = false
              } else {
                this.checkAllList[Number(this.activeName) - 1].isIndeterminate = false
              }

            })
            this.judgeAllSelected()
          });
          // console.log(this.subjectTabs, "getTabAllIdNodesubjectTabs");
        })
      })
    },

    //获取tab所有的节点，通过 visible 判断改节点是否已被过滤
    filterCheckItemTree(node, filterNode, ischeckAll) {
      return new Promise(resolve => {
        console.log(ischeckAll, "ischeckAllischeckAllischeckAll");
        node.forEach(e => {
          if (e.visible) {
            filterNode.push(e)
            if (ischeckAll) {
              this.$set(e, 'checked', true)
            } else {
              this.$set(e, 'checked', false)
            }
            if (e.childNodes.length > 0) {
              this.filterCheckItemTree(e.childNodes, filterNode, ischeckAll)
            }
          }
        });
        resolve(filterNode)
      })
    },
    //获取tab当前所有节点
    getTabAllIdNode(node) {
      return new Promise(resolve => {
        let checkedData = {
          checkedKeys: [],
          checkedKmCodeKey: [],
          checkedKmNameKey: [],
        }
        node.forEach((e) => {
          checkedData.checkedKeys.push(e.data.id)
          checkedData.checkedKmCodeKey.push(e.data.kmCode)
          checkedData.checkedKmNameKey.push(e.data.kmName)
        });
        resolve(checkedData)
      })
    },
    //获取选中节点的值
    checkChange(data1, data2) {
      this.subjectTabs.forEach((element) => {
        if (this.kmType == element.id) {
          console.log(this.kmType, element.id);
          element.ischeckKmCode = []
          element.ischeckKmName = []
          element.ischeckKmId = data2.checkedKeys
          this.isCheckedArry = element.ischeckKmId
          data2.checkedNodes.forEach(element1 => {
            element.ischeckKmCode.push(element1.kmCode)
            element.ischeckKmName.push(element1.kmName)
          });
        }
      });
      this.countCheckNum()
      this.isVisible = false
      this.isCheckAll = true
      this.isCheckAllRE = false
      this.$nextTick(() => {
        this.isCheckAllNode(null).then((res) => {
          // console.log(res);
          if (!this.isVisible) {
            this.isCheckAll = false
          }
          // console.log(this.isCheckAll, "res.ischeckAll1");
          if (this.isCheckAll) {
            this.checkAllList[Number(this.activeName) - 1].checkAll = true
            this.checkAllList[Number(this.activeName) - 1].isIndeterminate = false
          } else {
            if (this.isCheckAllRE) {
              this.checkAllList[Number(this.activeName) - 1].checkAll = false
              this.checkAllList[Number(this.activeName) - 1].isIndeterminate = true
            } else {
              this.checkAllList[Number(this.activeName) - 1].checkAll = false
              this.checkAllList[Number(this.activeName) - 1].isIndeterminate = false
            }
          }
        })
        this.judgeAllSelected()
      });
    },
    //当前tab是否全部选中
    isCheckAllNode(node) {
      return new Promise(resolve => {
        let nodes = []
        if (node) {
          nodes = node
        } else {
          nodes = this.$refs.assetTree.$refs.ndTree.store.root.childNodes
        }
        // console.log(nodes,"当前tab是否全部选中");
        nodes.forEach(e => {
          if (e.visible) {
            // console.log(e, "visible");
            this.isVisible = true
            if (!e.checked) {
              // console.log(1111);
              this.isCheckAll = false
            } else {
              // console.log(e, "checked");
              this.isCheckAllRE = true
            }
          }
          if (e.childNodes.length > 0) {
            this.isCheckAllNode(e.childNodes)
          }
        });

        // console.log(isCheckAll);
        resolve(true)
      })
    },
    // 计算选中的节点数量
    countCheckNum() {
      this.ALLCheckNumber = 0
      this.subjectTabs.forEach((e) => {
        this.ALLCheckNumber += e.ischeckKmId.length
      })
    },
    //点击全选
    dialogCheckBoxAll() {
      console.log(this.isChooseDialogCheck, "this.isChooseDialogKeythis.isChooseDialogKey");
      if (this.isChooseDialogCheck) {
        this.$ajax({
          method: 'post',
          url: '/tjSubjectInfo/forInformationOnAllSubjects.do',
          data: {
            orgType: this.subjectTemplateData,
            courseGrades: this.subjectLevelData,
          }
        }).then((res) => {
          if (res.data.code === 0) {
            // 先清空之前所选的所有数据
            this.subjectTabs.forEach((element) => {
              element.ischeckKmCode = []
              element.ischeckKmName = []
              element.ischeckKmId = []
            });
            this.afterDialogCheckBoxAll(res.data.data).then(res => {
              // console.log(this.subjectTabs); 
              // this.isCheckAllNode()
              this.$nextTick(() => {
                this.checkAllList[Number(this.activeName) - 1].checkAll = true
                this.checkAllList[Number(this.activeName) - 1].isIndeterminate = false
              });
              this.countCheckNum()
            })
          }
        })
      } else {
       this.clearAll()
        // this.isCheckAllNode()
      
      }
    },
    //清空所有
    clearAll(){
      this.subjectTabs.forEach((element) => {
          element.ischeckKmCode = []
          element.ischeckKmName = []
          element.ischeckKmId = []
        });
        this.isCheckedArry = []
        this.checkAllList = [
          {
            key: '1',
            checkAll: false,
            isIndeterminate: false,
            allNumber: 0,
          }, {
            key: '2',
            checkAll: false,
            isIndeterminate: false,
            allNumber: 0,
          }, {
            key: '3',
            checkAll: false,
            isIndeterminate: false,
            allNumber: 0,
          }, {
            key: '4',
            checkAll: false,
            isIndeterminate: false,
            allNumber: 0,
          }, {
            key: '5',
            checkAll: false,
            isIndeterminate: false,
            allNumber: 0,
          }
        ],
          this.resertTree++
          this.countCheckNum()
    },
    //点击全选后数据操作方法
    afterDialogCheckBoxAll(nodes) {
      return new Promise(reslove => {
        for (let i = 0; i < nodes.length; i++) {
          for (let j = 0; j < this.subjectTabs.length; j++) {
            if (this.subjectTabs[j].id === nodes[i].kmType) {
              this.subjectTabs[j].ischeckKmCode.push(nodes[i].kmCode)
              this.subjectTabs[j].ischeckKmName.push(nodes[i].kmName)
              this.subjectTabs[j].ischeckKmId.push(nodes[i].id)
            }
          }
        }
        this.isCheckedArry = this.subjectTabs[Number(this.activeName) - 1].ischeckKmId
        reslove(true)
      })
    },
    filterTextInput() {
      //判断是否被全选
      this.countCheckNum()
      this.isVisible = false
      this.isCheckAll = true
      this.isCheckAllRE = false
      this.$nextTick(() => {
        let isCheckAll = true
        let isCheckAllRE = false
        let isVisible = false
        this.isCheckAllNode(null, isCheckAll, isCheckAllRE, isVisible).then((res) => {
          // console.log(this.isCheckAll, "res.isCheckAll");
          if (!this.isVisible) {
            this.isCheckAll = false
          }
          if (this.isCheckAll) {
            console.log(1111);
            this.checkAllList[Number(this.activeName) - 1].checkAll = true
            this.checkAllList[Number(this.activeName) - 1].isIndeterminate = false
          } else {
            console.log(2222);
            if (this.isCheckAllRE) {
              this.checkAllList[Number(this.activeName) - 1].checkAll = false
              this.checkAllList[Number(this.activeName) - 1].isIndeterminate = true
            } else {
              this.checkAllList[Number(this.activeName) - 1].checkAll = false
              this.checkAllList[Number(this.activeName) - 1].isIndeterminate = false
            }
          }
        })
        this.judgeAllSelected()
      })
    },
    // 点击确定 将值传递过去
    enter() {
      //全选前先获取所有科目的节点
      this.getAllSubjectBefore().then(res => {
        console.log(this.subjectTabs, "1111111111111111111111111111111111111111");
        if (this.ALLCheckNumber > 60) {
          this.$confirm('选中科目过多将影响查询效率，所选科目*统计内容  组合最多不得超过120项，确定继续？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            this.$emit('beforeClose', true)
            this.$emit("change", this.subjectTabs)
            this.$emit("subjectTemplate", this.subjectTemplateData)
            this.$refs.dialog.close();
          }).catch(() => {

          });
        } else {
          this.$emit('beforeClose', true)
          this.$emit("change", this.subjectTabs)
          this.$emit("subjectTemplate", this.subjectTemplateData)
          this.$refs.dialog.close();
        }
      })
    },
    getAllSubjectBefore() {
      return new Promise(reslove => {
        this.$ajax({
          method: 'post',
          url: '/tjSubjectInfo/forInformationOnAllSubjects.do',
          data: {
            orgType: this.subjectTemplateData,
            courseGrades: this.subjectLevelData,
          }
        }).then((res) => {
          if (res.data.code === 0) {
            for (let ii = 0; ii < this.subjectTabs.length; ii++) {
              this.subjectTabs[ii].ischeckKmName = []
            }
            for (let j = 0; j < res.data.data.length; j++) {
              for (let i = 0; i < this.subjectTabs.length; i++) {
                for (let z = 0; z < this.subjectTabs[i].ischeckKmId.length; z++) {
                  // console.log(this.subjectTabs[i].ischeckKmId[z],res.data.data[j].id);
                  if (this.subjectTabs[i].ischeckKmId[z] === res.data.data[j].id) {
                    this.subjectTabs[i].ischeckKmName.push(res.data.data[j].kmName)
                    console.log(this.subjectTabs[i].ischeckKmName, res.data.data[j].kmName);
                  }
                }
              }
            }
            reslove(true)
          }
        })
      })
    },
    // 双击科目树
    doubleClickTree(data) {
      if (!this.showCheckbox) {
        this.TreeChechValue = {
          key: data.value,
          label: `${data.kmCode} ${data.fullKmName}`, //判断选中是否是一级节点 ，如果是直接返回 是子节点的话将父节点label拼接上
          kmCode: data.kmCode,
          kmType: data.kmType,
          child: data.isLeaf,
        };
        this.id = data.id;
        //点击确认
        this.enter();
        // const numberArry = this.$parent.$parent.subjectList.filter((item) => {
        //   return item.key == this.TreeChechValue.key;
        // });
        // if (numberArry.length > 0) {
        //   this.enter();
        // }
      }
    },
    close() {
      this.kmType = "1"
      this.checkAllList = [
        {
          key: '1',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '2',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '3',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '4',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '5',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }
      ],
        this.$emit("subjectTemplate", this.subjectTemplateData)
      this.$emit('beforeClose', true)
      this.subjectTemplateData = "4"
      this.$refs.dialog.close();
      this.filterText = ''; //关闭弹框后将输入框值置空，再次进来后重新赋值才能调用watch里面的搜索
    },
    handleClose() {
      this.kmType = "1"
      this.checkAllList = [
        {
          key: '1',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '2',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '3',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '4',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }, {
          key: '5',
          checkAll: false,
          isIndeterminate: false,
          allNumber: 0,
        }
      ],
        this.$emit("subjectTemplate", this.subjectTemplateData)
      this.$emit('beforeClose', true)
      this.subjectTemplateData = "4"
      this.$refs.dialog.close();
      this.filterText = ''; //关闭弹框后将输入框值置空，再次进来后重新赋值才能调用watch里面的搜索
    },
    // 树的节点禁用
    append(data, node) {
      // console.log(node, "hello");
      //获取节点的id
      let nodeId = node.id;
      //获取所有节点上的class el-tree-node__content
      var arr = Array.from(document.getElementsByClassName('el-tree-node__content'));
      //点击初始的时候将所有节点上的backgroundColor清除，不能设置为白色，否则移入效果失效
      arr.forEach(function (item, index) {
        // item.style.backgroundColor = "white";
        item.style.removeProperty('background'); //清除backgroud
        // item.removeAttribute("style");
      });
      //如果节点有子节点将背景颜色设置白色，移入和点击都是白色
      if (node.childNodes && node.childNodes.length > 0) {
        document.getElementById(nodeId).parentNode.style.backgroundColor = 'white';
      } else {
        document.getElementById(nodeId).parentNode.style.backgroundColor = '#c8ecff';
      }
    },
    renderContent(h, { node, data, store }) {
      if (!this.checkStrictly) {
        if (node.childNodes && node.childNodes.length > 0) {
          return (
            <div class="custom-tree-node1" id={node.id} style="width:100%">
              <div on-click={() => this.append(data, node)} style="width:100%">
                {node.label}
              </div>
            </div>
          );
        } else {
          return (
            <div id={node.id} style="width:100%" class="custom-tree-node2">
              <div on-click={() => this.append(data, node)} style="width:100%">
                {node.label}
              </div>
            </div>
          );
        }
      } else {
        return (
          <div id={node.id} style="width:100%" class="custom-tree-node2">
            <div on-click={() => this.append(data, node)} style="width:100%">
              {node.label}
            </div>
          </div>
        );
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.header-top-box {
  display: flex;
  flex-direction: row;
}

.subject-template-box {
  display: flex;
  align-items: center;
  padding: 9px 0 5px 0;

  .subject-template-box-text {
    color: #606266;
    margin-left: 12px;
    margin-right: 10px;
  }
}

.subject-level-box {
  display: flex;
  align-items: center;
  padding: 9px 0 5px 0;
  margin-left: 10px;

  .subject-level-box-text {
    color: #606266;
    margin-left: 12px;
    margin-right: 10px;
  }
}

.check-all-box {
  padding-top: 10px;
  padding-left: 24px;

  ::v-deep .el-checkbox__label {
    font-size: 12px;
  }
}

.TreeHeigh {
  height: 300px;
  overflow: auto;

  ::v-deep .el-tree-node__label {
    font-size: 12px;
  }

  ::v-deep .custom-tree-node1 {
    // flex: 1;
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    // font-size: 14px;
    // padding-right: 8px;
    color: #999999;
    width: 100%;
    line-height: 26px;
  }

  ::v-deep .custom-tree-node2 {
    // flex: 1;
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    // font-size: 14px;
    // padding-right: 8px;
    line-height: 26px;
    width: 100%;
  }

  ::v-deep .el-tree-node:focus>.el-tree-node__content {
    background-color: white;
  }
}

// ::v-deep .el-input__suffix {
//   line-height: 22px;
//   margin-top: -8px;
//   height: 22px;
// }
// ::v-deep  .el-tree-node.is-current {
//   .el-tree-node__content {
//     background:red !important;
//   }
// }
// ::v-deep.nd-tree-box {
//   ::v-deep .el-tree--highlight-current {
//     ::v-deep .el-tree-node.is-current {
//       ::v-deep .el-tree-node__content {
//         background-color: red;
//       }
//     }
//   }
// }

// .nd-tree-box[data-v-a41ecaf8]
//   .el-tree--highlight-current
//   .el-tree-node.is-current
//   > .el-tree-node__content {
//   background-color: #e8f7ff;
// }

// ::v-deep.el-tree-node__content {
//   &:hover {
//     background-color: #1a9bfc !important;
//   }
// }
.searchInput {
  display: flex;
  margin-left: 14px;

  .search-button {
    margin-left: 32px;
  }
}

.footer-in-box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .footer-in-checkBox {
    position: absolute;
    left: 10px;
    top: 4px;
    display: flex;
    align-items: center;

    ::v-deep .el-checkbox__label {
      font-size: 12px;
    }
  }
}
</style>
  