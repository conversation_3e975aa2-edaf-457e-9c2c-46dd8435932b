<template>
  <div>
    <div class="searchBox">
      <div class="search">
        <div>项目查询： </div>
        <div>
          <nd-input :width="'415px'" maxlength="200" v-model.trim="form.name" placeholder="请输入项目名称/项目编号查询"></nd-input>
        </div>
      </div>
      <div class="btn-cont">
        <el-button style="
          height: 34px;
          width: 90px;
          background: #ed911f;
          border: 1px solid #ed911f;
          color: #fff;
        " @click="search">查询</el-button>
        <el-button style="height: 34px; width: 90px; color: #ed911f; border: 1px solid #ed911f" plain
          @click="rest">重置</el-button>
      
      </div>
    </div>
    <div class="result-change">
      <template  v-if="listData.length>0">
      <div v-for="(item, i) in listData" :key="i" @click="todetail(item)">
        <div class="card">
          <div>{{ item.title }}</div>
          <div>{{ item.publicTime }}</div>
        </div>
        <div class="underline"></div>
      </div>
      </template>
      <template v-else>
        <empty :boxHeight="300"></empty>
      </template>
    </div>

    <!-- 分页器 -->
    <div class="pagination">
      <nd-pagination :page-size="pager.pageSize" layout="total, prev, pager, next, jumper" :total="pager.total" :total-page="totalPage" :current-page="pager.pageNo" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
  </div>
</template>

<script>
import ndInput from '@/components/ndInput.vue';
import ndButton from '@/components/ndButton.vue';
import ndPagination from '@/components/ndPagination.vue';
import empty from '@/views/empty/index.vue';
export default {
  name: 'ResultChange',
  props: {

    currentTab: {
      type: String || Number,
      default: '1',
    },

  },
  components: {
    ndInput, ndButton, ndPagination,empty
  },

  data() {
    return {
      form: {
        name: '',
      },

      pager: {
        pageNo: 1, // 当前页数
        pageSize: 10, // 当前页条数
        total: 0, // 总条目数
        pageSizes: [10, 30, 50, 100],
      },
      totalPage: 1,
      listData: [],

    };
  },
  watch: {
    currentTab(newVal, oldVal) {
      this.getData();
    }
  },
  methods: {
    search(){
      this.pager.pageNo=1
      this.pager.pageSize=10
      this.getData()
    },
    getData() {
      this.listData=[]
      this.$ajax({
        url: '/exposeAnnouncement/getResultAnnoPage',
        method: 'post',
        data: {
          keyWords: this.form.name,
          pageNo: this.pager.pageNo,
          pageSize: this.pager.pageSize,
          tagStatus: 1, //1全部，2已报名
          timeSort: 1, //1倒序，2正序
        }
      }).then((res) => {
        if (res.data.code == 200) {
          this.listData = res.data.data.records;
          this.pager.total = res.data.data.total
        }
      })
    },
    rest() {
      this.form.name = ''
      this.getData()
    },
    todetail(item) {
      let query = [];
      query = [{ name: '公示公告', route: 'publicInformationView'}];
      item.id=item.id;
      item.projectId=item.projectId;
      if (window.location.origin == 'http://localhost:8080') {
        window.location.replace(
          window.location.origin +
          `/#/details?id=${item.id}&projectId=${item.projectId}&tenderId=${item.tenderId}&route=${JSON.stringify(query)}&type=11&homeType=2&noticeFlag=1`,
        );
      } else {
        window.location.replace(
          window.location.origin +
          window.location.pathname +
          `#/details?id=${item.id}&projectId=${item.projectId}&tenderId=${item.tenderId}&route=${JSON.stringify(query)}&type=11&homeType=2&noticeFlag=1`,
        );
      }
    },
    // 分页组件每页显示条数改变时
    handleSizeChange(val) {
      this.pager.pageSize = val;
      this.pager.pageNo = 1;
      this.getData()
    },
    // 分页组件跳转其他页码
    handleCurrentChange(val) {
      this.pager.pageNo = val;
      this.getData()
    },
  },
  mounted() {
    this.getData()
  },
};
</script>

<style scoped lang="scss">
.result-change {
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  margin-top: 20px;

  .underline {
    width: calc(100% - 20px);
    height: 1px;
    background: #ccc;
    margin: 0 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.card {
  width: 100%;
  height: 35px;
  /* border: 1px solid red; */
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  /* margin-bottom: 10px; */
  font-size: 13px;
  padding: 0 10px;
  border-radius: 5px;
  margin: 6px 0;
}

.card:hover {
  background: #ed911f;
}

.searchBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
}

.search {
  display: flex;
  font-weight: bold;
  color: #333;
}

.bountone {
  display: flex;
}
.btn-cont {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        float: right;
        flex: 1;

        ::v-deep .el-button {
          padding: 0;
        }
      }
</style>
