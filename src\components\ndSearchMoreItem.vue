<template>
  <div class="nd-search-more-item-box">
    <div v-if="showTitle" class="left-box" :style="{ width: width }" :class="letterSpacing ? 'letter-spacing' : ''">
      <span>{{ title }}</span>
      <nd-tooltip v-if="tipShow" effect="dark" :content="tipText" :placement="tipPlacement">
        <img class="tooltip" src="@/assets/prompt.png">
      </nd-tooltip>
    </div>
    <div class="right-box" :style="{ width: 'calc(100% - ' + width + ')' }">
      <slot />
    </div>
  </div>
</template>

<script>
import ndTooltip from "./ndTooltip.vue"
export default {
  components: {
    ndTooltip,
  },
  props: {
    title: {
      type: String,
      default: "未命名",
    },
    showTitle: {
      type: Boolean,
      default: true,
    },
    width: {
      type: String,
      default: "58px",
    },
    tipShow: {
      type: Boolean,
      default: false
    },
    tipText: {
      type: String,
      default: ""
    },
    tipPlacement: {
      type: String,
      default: "top-start"
    }
  },
  data() {
    return {
      letterSpacing: false,
    };
  },
  watch: {
    title: {
      immediate: true,
      handler(value) {
        if (value.length > 8) {
          this.letterSpacing = true;
        }
      },
    },
  },
  methods: {
  }
};
</script>

<style scoped lang="scss">
.nd-search-more-item-box {
  width: auto;
  height: auto;
  display: flex;
  flex-direction: row;
  // justify-content: center;
  align-items: center;
  margin-bottom: 10px;

  .left-box {
    // width: 58px;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding-right: 10px;

    .tooltip {
      width: 12px;
      cursor: pointer;
      margin-left: 5px;
      //打包到jsp里面中，和easyui冲突，加几行样式
      display: block;
      position: relative;
      padding: 0px;
    }
  }

  .letter-spacing {
    letter-spacing: -1.03px;
  }

  .right-box {
    // width: calc(100% - 58px);
    height: 100%;
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;
  }
}
</style>