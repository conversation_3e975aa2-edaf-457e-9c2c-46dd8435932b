﻿// window.alert = function (msg) {
//     // var div = document.createElement("div");
//     // div.innerHTML = "<style type=\"text/css\"> "
//     //     + ".nbaMask { position: fixed; z-index: 1000; top: 0; right: 0; left: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); } "
//     //     + ".nbaMaskTransparent { position: fixed; z-index: 1000; top: 0; right: 0; left: 0; bottom: 0; } "
//     //     + ".nbaDialog { position: fixed; z-index: 9999; width: 80%; max-width: 360px; top: 50%; left: 50%; -webkit-transform: translate(-50%, -50%); transform: translate(-50%, -50%); background-color: #fff; text-align: center; border-radius: 8px; overflow: hidden; opacity: 1; color: white; } "
//     //     + ".nbaDialog .nbaDialogBd { padding: 0 .27rem; font-size: 15px; line-height: 1.3; word-wrap: break-word; word-break: break-all; color: #000000; } "
//     //     + ".nbaDialog .nbaDialogFt { position: relative; line-height: 36px; font-size: 17px; display: -webkit-box; display: -webkit-flex; display: flex; } "
//     //     + ".nbaDialog .nbaDialogFt:after { content: \" \"; position: absolute; left: 0; top: 0; right: 0; height: 1px; border-top: 1px solid #e6e6e6; color: #e6e6e6; -webkit-transform-origin: 0 0; transform-origin: 0 0; -webkit-transform: scaleY(0.5); transform: scaleY(0.5); } "
//     //     + ".nbaDialog .nbaDialogBtn { display: block; -webkit-box-flex: 1; -webkit-flex: 1; flex: 1; color: #1390ff; text-decoration: none; -webkit-tap-highlight-color: transparent; position: relative; margin-bottom: 0; } "
//     //     + ".nbaDialog .nbaDialogBtn:after { content: \" \"; position: absolute; left: 0; top: 0; width: 1px; bottom: 0; border-left: 1px solid #e6e6e6; color: #e6e6e6; -webkit-transform-origin: 0 0; transform-origin: 0 0; -webkit-transform: scaleX(0.5); transform: scaleX(0.5); } "
//     //     + ".nbaDialog a { text-decoration: none; -webkit-tap-highlight-color: transparent; } "
//     //     + "</style> "
//     //     + "<div id=\"fe792695-b263-11ec-a8ee-f48e38bf4326\" style=\"display: none\"> "
//     //     + "<div class=\"nbaMask\"></div> "
//     //     + "<div class=\"nbaDialog\" id='ece8e1b8-b263-11ec-a8ee-f48e38bf4326'> "
//     //     + "<div class=\"nbaDialogBd\" id=\"07e21c65-b264-11ec-a8ee-f48e38bf4326\" style='font-size: 18px;line-height: 36px;'></div> "
//     //     + "<div class=\"nbaDialogFt\"> "
//     //     + "<a href=\"javascript:;\" class=\"nbaDialogBtn nbaDialogBtnPrimary\" id=\"11617891-b264-11ec-a8ee-f48e38bf4326\" style='font-size: 18px;'>确定</a> "
//     //     + "</div></div></div>";
//     // document.body.appendChild(div);
//     // var dialogs2 = document.getElementById("fe792695-b263-11ec-a8ee-f48e38bf4326");
//     // dialogs2.style.display = 'block';
//     // var dialog_msg2 = document.getElementById("07e21c65-b264-11ec-a8ee-f48e38bf4326");
//     // dialog_msg2.innerHTML = msg;
//     // var dialog_ok2 = document.getElementById("11617891-b264-11ec-a8ee-f48e38bf4326");
//     // dialog_ok2.onclick = function () {
//     //     dialogs2.style.display = 'none';
//     // };
// };

// import {service_path} from './common.js';
//==本JS是加载Lodop插件或Web打印服务CLodop/Lodop7的综合示例，可直接使用，建议理解后融入自己程序==
var CreatedOKLodopObject, CLodopIsLocal, CLodopJsState, LoadJsState, jsState;
//用双端口加载主JS文件Lodop.js(或CLodopfuncs.js兼容老版本)以防其中某端口被占:
var MainJS = "CLodopfuncs.js",
    URL_WS1 = "ws://localhost:8000/" + MainJS,                //ws用8000/18000
    URL_WS2 = "ws://localhost:18000/" + MainJS,
    URL_HTTP1 = "http://localhost:8000/" + MainJS,              //http用8000/18000
    URL_HTTP2 = "http://localhost:18000/" + MainJS,
    URL_HTTP3 = "https://localhost.lodop.net:8443/" + MainJS;   //https用8000/8443


//==判断是否需要CLodop(那些不支持插件的浏览器):==
function needCLodop() {
    try {
        var ua = navigator.userAgent;
        if (ua.match(/Windows\sPhone/i)) return true;
        if (ua.match(/iPhone|iPod|iPad/i)) return true;
        if (ua.match(/Android/i)) return true;
        if (ua.match(/Edge\D?\d+/i)) return true;
        var verTrident = ua.match(/Trident\D?\d+/i);
        var verIE = ua.match(/MSIE\D?\d+/i);
        var verOPR = ua.match(/OPR\D?\d+/i);
        var verFF = ua.match(/Firefox\D?\d+/i);
        var x64 = ua.match(/x64/i);
        if ((!verTrident) && (!verIE) && (x64)) return true;
        else if (verFF) {
            verFF = verFF[0].match(/\d+/);
            if ((verFF[0] >= 41) || (x64)) return true;
        } else if (verOPR) {
            verOPR = verOPR[0].match(/\d+/);
            if (verOPR[0] >= 32) return true;
        } else if ((!verTrident) && (!verIE)) {
            var verChrome = ua.match(/Chrome\D?\d+/i);
            if (verChrome) {
                verChrome = verChrome[0].match(/\d+/);
                if (verChrome[0] >= 41) return true;
            }
        }
        return false;
    } catch (err) {
        return true;
    }
}
//加载CLodop时用双端口(http是8000/18000,而https是8443/8444)以防其中某端口被占,
//主JS文件“CLodopfuncs.js”是固定文件名，其内容是动态的，与当前打印环境有关:
// function loadCLodop() {
//     if (CLodopJsState == "loading" || CLodopJsState == "complete") return;
//     CLodopJsState = "loading";
//     var head = document.head || document.getElementsByTagName("head")[0] || document.documentElement;
//     var JS1 = document.createElement("script");
//     var JS2 = document.createElement("script");
//     if (window.location.protocol == 'https:') {
//         JS1.src = "https://localhost.lodop.net:8443/CLodopfuncs.js";
//         JS2.src = "https://localhost.lodop.net:8444/CLodopfuncs.js";
//     } else {
//         JS1.src = "http://localhost:8000/CLodopfuncs.js";
//         JS2.src = "http://localhost:18000/CLodopfuncs.js";
//     }
//     CLodopJsState = "complete";
//     let jsStrae = false;
//     JS1.onload = JS2.onload = function () {
//         console.log("=====JS1.onload");
//         jsStrae = true;
//         CLodopJsState = "complete";
//     }
//     JS1.onerror = function (evt) {
//         console.log("=====JS1.onerror");
//         CLodopJsState = "complete";
//     }
//     JS2.onerror = function (evt) {
//         console.log("=====JS2.onerror");
//         CLodopJsState = "complete";
//     }
//     console.log(jsStrae,"===jsStrae");
//     head.insertBefore(JS1, head.firstChild);
//     head.insertBefore(JS2, head.firstChild);
//     CLodopIsLocal = !!((JS1.src + JS2.src).match(/\/\/localho|\/\/127.0.0./i));
// }

// //开始加载
// if (needCLodop()) {
//     loadCLodop();
// }

//==检查加载成功与否，如没成功则用http(s)再试==
//==低版本CLODOP6.561/Lodop7.043及前)用本方法==
function checkOrTryHttp() {
    if (window.getCLodop) {
        LoadJsState = "complete";
        return true;
    }
    if (LoadJsState == "loadingB" || LoadJsState == "complete") return;
    //WebSocket链接失败，表示未服务未开启。直接退出，不需要加载对应的js文件。提高速度
    if(!jsState){
        return;
    }
    LoadJsState = "loadingB";
    var head = document.head || document.getElementsByTagName("head")[0] || document.documentElement;
    var JS1 = document.createElement("script")
        , JS2 = document.createElement("script")
        , JS3 = document.createElement("script");
    JS1.src = URL_HTTP1;
    JS2.src = URL_HTTP2;
    JS3.src = URL_HTTP3;
    JS1.onload = JS2.onload = JS3.onload = JS2.onerror = JS3.onerror = function () { LoadJsState = "complete"; }
    JS1.onerror = function (e) {
        if (window.location.protocol !== 'https:')
            head.insertBefore(JS2, head.firstChild); else
            head.insertBefore(JS3, head.firstChild);
    }
    head.insertBefore(JS1, head.firstChild);
}

//==加载Lodop对象的主过程:==
(function loadCLodop() {
    if (!needCLodop()) return;
    CLodopIsLocal = !!((URL_WS1 + URL_WS2).match(/\/\/localho|\/\/127.0.0./i));
    LoadJsState = "loadingA";
    if (!window.WebSocket && window.MozWebSocket) window.WebSocket = window.MozWebSocket;
    //ws方式速度快(小于200ms)且可避免CORS错误,但要求Lodop版本足够新:
    try {
        var WSK2 = new WebSocket(URL_WS2);
        WSK2.onopen = function (e) {
            jsState = true;
            setTimeout(checkOrTryHttp(), 200);
        }
        WSK2.onmessage = function (e) {
            jsState = true;
            if (!window.getCLodop) eval(e.data);
        }
        WSK2.onerror = function (e) {
            jsState = false;
            var WSK1 = new WebSocket(URL_WS1);
            WSK1.onopen = function (e) { setTimeout(checkOrTryHttp(), 200); }
            WSK1.onmessage = function (e) { if (!window.getCLodop) eval(e.data); }
            WSK1.onerror = function (e) {checkOrTryHttp();}
        }
    } catch (e) {
        jsState = true;
        checkOrTryHttp();
    }
})();
//==获取LODOP对象主过程,判断是否安装、需否升级:==
function getLodop(oOBJECT, oEMBED) {
    // var service_path = window.ipConfig.threePowersUrl
    var service_path = ""
    var strFontTag = "<font color='#FF00FF'>打印控件";
    var strLodopInstall = strFontTag + "打印服务未安装!请点击<a href='javascript: window.location.href = encodeURI(\"" + service_path + "/static/lodop/CLodop_Setup_for_Win32NT.zip\")' target='_self'>下载安装</a>";
    // var strLodopUpdate    = strFontTag+"需要升级!点击这里<a href='install_lodop32.exe' target='_self'>执行升级</a>";
    var strLodop64Install = strFontTag + "打印服务未安装!请点击<a href='javascript: window.location.href = encodeURI(\"" + service_path + "/static/lodop/CLodop_Setup_for_Win64NT_4.158EN.zip\")' target='_self'>下载安装</a>";
    // var strLodop64Update  = strFontTag+"需要升级!点击这里<a href='install_lodop64.exe' target='_self'>执行升级</a>";
    var strCLodopInstallA = "<font color='#FF00FF'>打印服务未安装，请点击<a href='javascript: window.location.href = encodeURI(\"" + service_path + "/static/lodop/CLodop_Setup_for_Win32NT.zip\")' target='_self'>下载安装</a>";
    var strCLodopInstallB = "（若已安装过，可点击<a href='CLodop.protocol:setup' target='_self'>再次启动</a>）";
    // var strCLodopUpdate   = "<br><font color='#FF00FF'>打印服务需升级!点击这里<a href='CLodop_Setup_for_Win32NT.exe' target='_self'>执行升级</a>";
    var strLodop7FontTag = "<font color='#FF00FF'>Web打印服务Lodop7";
    var strLodop7HrefX86 = "点击<a href='javascript: window.location.href = encodeURI(\"" + service_path + "/static/lodop/Lodop7.043_Linux_X86_64_CN.tar.gz\")' target='_self'>下载安装</a>(下载后解压，点击lodop文件开始执行)";
    var strLodop7HrefARM = "点击<a href='javascript: window.location.href = encodeURI(\"" + service_path + "/static/lodop/Lodop7.043_Linux_ARM64_CN.tar.gz\")' target='_self'>下载安装</a>(下载后解压，点击lodop文件开始执行)";
    var strLodop7Install_X86 = strLodop7FontTag + "未安装启动，" + strLodop7HrefX86;
    var strLodop7Install_ARM = strLodop7FontTag + "未安装启动，" + strLodop7HrefARM;
    // var strLodop7Update_X86  = strLodop7FontTag+"需升级，"+strLodop7HrefX86;
    // var strLodop7Update_ARM  = strLodop7FontTag+"需升级，"+strLodop7HrefARM;
    var strInstallOK = "，成功后请刷新本页面或重启浏览器。<br>请不要禁用打印服务！</font>";
    var LODOP;
    try {
        var isWinIE = (/MSIE/i.test(navigator.userAgent)) || (/Trident/i.test(navigator.userAgent));
        var isWinIE64 = isWinIE && (/x64/i.test(navigator.userAgent));
        var isLinuxX86 = (/Linux/i.test(navigator.platform)) && (/x86/i.test(navigator.platform));
        var isLinuxARM = (/Linux/i.test(navigator.platform)) && (/aarch/i.test(navigator.platform));
        if (needCLodop() || isLinuxX86 || isLinuxARM) {
            try {
                LODOP = getCLodop();
            } catch (err) {
            }
            if (!LODOP && CLodopJsState !== "complete") {
                if (CLodopJsState == "loading") alert("打印控件未加载完成，请稍后再试！"); else alert("未加载打印控件，请先加载打印控件！");
                return;
            }
            var strAlertMessage;
            if (!LODOP) {
                if (isLinuxX86) strAlertMessage = strLodop7Install_X86; else if (isLinuxARM) strAlertMessage = strLodop7Install_ARM; else
                    strAlertMessage = strCLodopInstallA + (CLodopIsLocal ? strCLodopInstallB : "");
                // console.log('=======================-------------------=========================-----------------===========');
                // console.log(strAlertMessage , strInstallOK);
                // alert(strAlertMessage + strInstallOK);
                alert('打印服务未安装，请联系工作人员获取打印插件!若已安装过，请刷新本页面或重启浏览器。请不要禁用打印服务！');
                // document.body.innerHTML = strAlertMessage + strInstallOK + document.body.innerHTML;
                return;
            } else {
                // if (isLinuxX86 && LODOP.CVERSION < "7.0.4.3") strAlertMessage = strLodop7Update_X86; else
                // if (isLinuxARM && LODOP.CVERSION < "7.0.4.3") strAlertMessage = strLodop7Update_ARM; else
                // if (CLODOP.CVERSION < "4.1.5.8")              strAlertMessage = strCLodopUpdate;
                // if (strAlertMessage) document.body.innerHTML = strAlertMessage+ strInstallOK + document.body.innerHTML;
            }
        } else { //==如果页面有Lodop插件就直接使用,否则新建:==
            if (oOBJECT || oEMBED) {
                if (isWinIE)
                    LODOP = oOBJECT;
                else
                    LODOP = oEMBED;
            } else if (!CreatedOKLodopObject) {
                LODOP = document.createElement("object");
                LODOP.setAttribute("width", 0);
                LODOP.setAttribute("height", 0);
                LODOP.setAttribute("style", "position:absolute;left:0px;top:-100px;width:0px;height:0px;");
                if (isWinIE)
                    LODOP.setAttribute("classid", "clsid:2105C259-1E0C-4534-8141-A753534CB4CA");
                else
                    LODOP.setAttribute("type", "application/x-print-lodop");
                document.documentElement.appendChild(LODOP);
                CreatedOKLodopObject = LODOP;
            } else
                LODOP = CreatedOKLodopObject;
            //==Lodop插件未安装时提示下载地址:==
            if ((!LODOP) || (!LODOP.VERSION)) {
                // alert((isWinIE64 ? strLodop64Install : strLodopInstall) + strInstallOK);
                alert("请联系工作人员获取打印插件");
                // document.body.innerHTML = (isWinIE64 ? strLodop64Install : strLodopInstall) + strInstallOK + document.body.innerHTML;
                return LODOP;
            }
            // if (LODOP.VERSION < "6.2.2.6") {
            //     document.body.innerHTML = (isWinIE64 ? strLodop64Update : strLodopUpdate) + strInstallOK + document.body.innerHTML;
            // }
        }
        //===如下空白位置适合调用统一功能(如注册语句、语言选择等):=======================
        LODOP.SET_LICENSES("南京南大尚诚软件科技有限公司", "8C02E200329296561301775775A7070B", "", "");
        //===============================================================================
        return LODOP;
    } catch (err) {
        alert("请联系工作人员获取打印插件");
    }
}

function getLodop2(oOBJECT, oEMBED) {
    // var service_path = window.ipConfig.threePowersUrl
    var service_path = ""
    var strFontTag = "<font color='#FF00FF'>打印控件";
    var strLodopInstall = strFontTag + "打印服务未安装!请点击<a href='javascript: window.location.href = encodeURI(\"" + service_path + "/static/lodop/CLodop_Setup_for_Win32NT.zip\")' target='_self'>下载安装</a>";
    // var strLodopUpdate    = strFontTag+"需要升级!点击这里<a href='install_lodop32.exe' target='_self'>执行升级</a>";
    var strLodop64Install = strFontTag + "打印服务未安装!请点击<a href='javascript: window.location.href = encodeURI(\"" + service_path + "/static/lodop/CLodop_Setup_for_Win64NT_4.158EN.zip\")' target='_self'>下载安装</a>";
    // var strLodop64Update  = strFontTag+"需要升级!点击这里<a href='install_lodop64.exe' target='_self'>执行升级</a>";
    var strCLodopInstallA = "<font color='#FF00FF'>打印服务未安装，请点击<a href='javascript: window.location.href = encodeURI(\"" + service_path + "/static/lodop/CLodop_Setup_for_Win32NT.zip\")' target='_self'>下载安装</a>";
    var strCLodopInstallB = "（若已安装过，可点击<a href='CLodop.protocol:setup' target='_self'>再次启动</a>）";
    // var strCLodopUpdate   = "<br><font color='#FF00FF'>打印服务需升级!点击这里<a href='CLodop_Setup_for_Win32NT.exe' target='_self'>执行升级</a>";
    var strLodop7FontTag = "<font color='#FF00FF'>Web打印服务Lodop7";
    var strLodop7HrefX86 = "点击<a href='javascript: window.location.href = encodeURI(\"" + service_path + "/static/lodop/Lodop7.043_Linux_X86_64_CN.tar.gz\")' target='_self'>下载安装</a>(下载后解压，点击lodop文件开始执行)";
    var strLodop7HrefARM = "点击<a href='javascript: window.location.href = encodeURI(\"" + service_path + "/static/lodop/Lodop7.043_Linux_ARM64_CN.tar.gz\")' target='_self'>下载安装</a>(下载后解压，点击lodop文件开始执行)";
    var strLodop7Install_X86 = strLodop7FontTag + "未安装启动，" + strLodop7HrefX86;
    var strLodop7Install_ARM = strLodop7FontTag + "未安装启动，" + strLodop7HrefARM;
    // var strLodop7Update_X86  = strLodop7FontTag+"需升级，"+strLodop7HrefX86;
    // var strLodop7Update_ARM  = strLodop7FontTag+"需升级，"+strLodop7HrefARM;
    var strInstallOK = "，成功后请刷新本页面或重启浏览器。<br>请不要禁用打印服务！</font>";
    var LODOP;
    try {
        var isWinIE = (/MSIE/i.test(navigator.userAgent)) || (/Trident/i.test(navigator.userAgent));
        var isWinIE64 = isWinIE && (/x64/i.test(navigator.userAgent));
        var isLinuxX86 = (/Linux/i.test(navigator.platform)) && (/x86/i.test(navigator.platform));
        var isLinuxARM = (/Linux/i.test(navigator.platform)) && (/aarch/i.test(navigator.platform));
        if (needCLodop() || isLinuxX86 || isLinuxARM) {
            try {
                LODOP = getCLodop();
            } catch (err) {
            }
            if (!LODOP && CLodopJsState !== "complete") {
                if (CLodopJsState == "loading") alert("打印控件未加载完成，请稍后再试！"); else alert("未加载打印控件，请先加载打印控件！");
                return;
            }
            var strAlertMessage;
            if (!LODOP) {
                if (isLinuxX86) strAlertMessage = strLodop7Install_X86; else if (isLinuxARM) strAlertMessage = strLodop7Install_ARM; else
                    strAlertMessage = strCLodopInstallA + (CLodopIsLocal ? strCLodopInstallB : "");
                alert(strAlertMessage + strInstallOK);
                // document.body.innerHTML = strAlertMessage + strInstallOK + document.body.innerHTML;
                return;
            } else {
                // if (isLinuxX86 && LODOP.CVERSION < "7.0.4.3") strAlertMessage = strLodop7Update_X86; else
                // if (isLinuxARM && LODOP.CVERSION < "7.0.4.3") strAlertMessage = strLodop7Update_ARM; else
                // if (CLODOP.CVERSION < "4.1.5.8")              strAlertMessage = strCLodopUpdate;
                // if (strAlertMessage) document.body.innerHTML = strAlertMessage+ strInstallOK + document.body.innerHTML;
            }
        } else { //==如果页面有Lodop插件就直接使用,否则新建:==
            if (oOBJECT || oEMBED) {
                if (isWinIE)
                    LODOP = oOBJECT;
                else
                    LODOP = oEMBED;
            } else if (!CreatedOKLodopObject) {
                LODOP = document.createElement("object");
                LODOP.setAttribute("width", 0);
                LODOP.setAttribute("height", 0);
                LODOP.setAttribute("style", "position:absolute;left:0px;top:-100px;width:0px;height:0px;");
                if (isWinIE)
                    LODOP.setAttribute("classid", "clsid:2105C259-1E0C-4534-8141-A753534CB4CA");
                else
                    LODOP.setAttribute("type", "application/x-print-lodop");
                document.documentElement.appendChild(LODOP);
                CreatedOKLodopObject = LODOP;
            } else
                LODOP = CreatedOKLodopObject;
            //==Lodop插件未安装时提示下载地址:==
            if ((!LODOP) || (!LODOP.VERSION)) {
                alert((isWinIE64 ? strLodop64Install : strLodopInstall) + strInstallOK);
                // document.body.innerHTML = (isWinIE64 ? strLodop64Install : strLodopInstall) + strInstallOK + document.body.innerHTML;
                return LODOP;
            }
            // if (LODOP.VERSION < "6.2.2.6") {
            //     document.body.innerHTML = (isWinIE64 ? strLodop64Update : strLodopUpdate) + strInstallOK + document.body.innerHTML;
            // }
        }
        //===如下空白位置适合调用统一功能(如注册语句、语言选择等):=======================
        LODOP.SET_LICENSES("南京南大尚诚软件科技有限公司", "8C02E200329296561301775775A7070B", "", "");
        //===============================================================================
        return LODOP;
    } catch (err) {
        alert("getLodop出错:" + err);
    }
}

export { getLodop }; //导出getLodop
