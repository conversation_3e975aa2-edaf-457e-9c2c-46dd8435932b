<template>
  <div class="details-box" :style="'padding-top:' + detailPaddingTop">
    <iframe id="myIframe" class="iframe-box" :src="url"></iframe>

    <mapDialog ref="mapDialogRef" />
    <div class="content-box2" v-if="buttonList.length != 0">
      <div
        class="title"
        v-for="(item, index) in buttonList"
        :key="index"
        @click="tipClick(item.name, item.text)"
      >
        {{ item.name }}
      </div>
    </div>
    <tipDialog ref="tipDialogRef" />
  </div>
</template>

<script>
import mapDialog from './components/mapDailog.vue';
import tipDialog from './components/dialog.vue';

export default {
  components: {
    mapDialog,
    tipDialog,
  },
  props: {
    detailPaddingTop: {
      type: String,
      default: '0px',
    },
  },
  data() {
    return {
      url: 'http://localhost:8898/#/hgDetailView',
      buttonList: [],
    };
  },
  activated() {
    this.getButtonList();
    // this.url =
    //     window.ipConfig.detailUrl +
    //     "?id=" +
    //     this.$route.query.id +
    //     "&projectId=" +
    //     this.$route.query.projectId +
    //     "&noticeFlag=" +
    //     this.$route.query.noticeFlag +
    //     "&type=" +
    //     this.$route.query.type +
    //     "&typeArea=1200px" +
    //     "&theme=" +
    //     window.ipConfig.theme +
    //     "&random=" + Math.floor(Math.random() * 1000000000000000);

    if (!this.$route.query.noticeFlag) {
      // this.url = 'http://localhost:8598/#/' +
      //   'hgDetailView?id=' +
      //   this.$route.query.id +
      //   '&projectId=' +
      //   this.$route.query.projectId +
      //   '&changeType=' +
      //   this.$route.query.changeType +
      //   '&type=' +
      //   this.$route.query.type +
      //   '&typeArea=1200px' +
      //   '&theme=' +
      //   window.ipConfig.theme +
      //   '&random=' +
      //   Math.floor(Math.random() * 1000000000000000)+'&noticeType='+this.$route.query.noticeType;
      this.url = this.url =
        window.ipConfig.detailUrl +
        'hgDetailView?id=' +
        this.$route.query.id +
        '&projectId=' +
        this.$route.query.projectId +
        '&changeType=' +
        this.$route.query.changeType +
        '&type=' +
        this.$route.query.type +
        '&typeArea=1200px' +
        '&theme=' +
        window.ipConfig.theme +
        '&random=' +
        Math.floor(Math.random() * 1000000000000000)+'&noticeType='+this.$route.query.noticeType;
    } else {
        if(this.$route.query.type==11){
        this.url = this.url =
        window.ipConfig.detailUrl +
        'noticeDetail2?id=' +
        this.$route.query.id +
        '&projectId=' +
        this.$route.query.projectId +
        '&tenderId=' +
        this.$route.query.tenderId +
        '&type=' +
        this.$route.query.type +
        '&typeArea=1200px' +
        '&theme=' +
        window.ipConfig.theme +
        '&random=' +
        Math.floor(Math.random() * 1000000000000000);
//  this.url =
//         'http://localhost:8598/#/noticeDetail2?id=' +
//         this.$route.query.id +
//         '&projectId=' +
//         this.$route.query.projectId +
//         '&tenderId=' +
//         this.$route.query.tenderId +
//         '&type=' +
//         this.$route.query.type +
//         '&typeArea=1200px' +
//         '&theme=' +
//         window.ipConfig.theme +
//         '&random=' +
//         Math.floor(Math.random() * 1000000000000000);
      }else{
this.url = this.url =
        window.ipConfig.detailUrl +
        'noticeDetail?id=' +
        this.$route.query.id +
        '&projectId=' +
        this.$route.query.projectId +
        '&tenderId=' +
        this.$route.query.tenderId +
        '&type=' +
        this.$route.query.type +
        '&typeArea=1200px' +
        '&theme=' +
        window.ipConfig.theme +
        '&random=' +
        Math.floor(Math.random() * 1000000000000000);
      }
    //   this.url = this.url =
    //     window.ipConfig.detailUrl +
    //     'noticeDetail?id=' +
    //     this.$route.query.id +
    //     '&projectId=' +
    //     this.$route.query.projectId +
    //     '&tenderId=' +
    //     this.$route.query.tenderId +
    //     '&type=' +
    //     this.$route.query.type +
    //     '&typeArea=1200px' +
    //     '&theme=' +
    //     window.ipConfig.theme +
    //     '&random=' +
    //     Math.floor(Math.random() * 1000000000000000);
    }

    window.addEventListener('message', (event) => {
      // 检查消息的来源是否可信
      if (event.data.key === 'ndsc-mh-detail') {
        // 处理接收到的消息
      }

      const iframe = document.getElementById('myIframe');
      switch (event.data.key) {
        case 'ndsc-mh-detail':
          iframe.style.height = event.data.data.height + 'px';
          //   iframe.style.height = '90px';
          break;
        case 'ndsc-mh-detail-map':
          console.log(event, '!!!!!!!!!!');
          this.$refs.mapDialogRef.open({
            lat: event.data.data.lat,
            lng: event.data.data.lng,
          });
          break;
        default:
          break;
      }
    });
  },
  methods: {
    getButtonList() {
      this.$ajax({
        url: '/sysInfo/getBottomButtonConfig',
        method: 'get',
      }).then((r) => {
        if (r.data.code === 200) {
          this.buttonList = r.data.data;
        }
      });
    },
    tipClick(name, text) {
      this.$refs.tipDialogRef.open(name, text);
    },
  },
};
</script>

<style lang="scss" scoped>
.details-box {
  width: 100%;
  background: #f7f7f7;
  border-top: 1px solid #cbcbcb;
  .content-box2 {
    width: 1200px;
    margin: 0 auto;
    background: #fff;
    border-radius: 5px;
    padding: 0px 0px 15px 0px;
    margin-top: 12px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    font-size: 12px;
    .title {
      display: inline-block;
      //   border: 1px solid #2A5A1E;
      border-radius: 3px;
      padding: 8px 10px;
      display: flex;
      align-items: center;
      margin-left: 10px;
      margin-top: 10px;
      color: #2a5a1e;
      cursor: pointer;
      background: linear-gradient(0deg, rgba(41, 91, 28, 0.3) -36%, #ffffff 100%);
      border: 1px solid rgba(41, 91, 28, 0.3);

      i {
        display: inline-block;
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background: #2a5a1e;
        margin-right: 10px;
      }
    }
  }

  .iframe-box {
    width: 100%;
    display: block;
  }
}
</style>
