<template>
  <div class="news">
    <div class="news-center">
      <div class="title-box">
        <div class="left">
          <div class="title-text">新闻中心</div>
          <img src="@/assets/detail/title-icon.png" alt="" srcset="" />
        </div>
      </div>
      <div class="main">
        <div class="main-left">
          <el-carousel height="378px" arrow="never" :interval="3000">
            <el-carousel-item v-for="(item, index) in imgData" :key="index" style="cursor: pointer">
              <img :src="item.imgUrl" @click="handleMore(item, '工作动态')" alt="" />
              <div class="title">{{ item.title }}</div>
            </el-carousel-item>
          </el-carousel>
        </div>
        <div class="main-right">
          <div class="right-tab">
            <div class="tab-box">
              <div class="tab-item-box" v-for="(item, index) in tabList" :key="index">
                <div
                  class="tab-item"
                  :class="[activeTab === item.order ? 'tab-active' : 'tab-deactive']"
                  @mouseenter="tabActive(item, index)"
                >
                  {{ item.title }}
                </div>
                <div v-if="activeTab === item.order" class="active-line"></div>
              </div>
              <div @click="toMore(1)" class="more-text">查看更多>></div>
            </div>

            <div class="line"></div>
          </div>
          <div class="right-cont">
            <div
              class="right-item"
              v-for="(item, index) in newsList"
              :key="index"
              @click="handleMore(item, activeTitle)"
            >
              <div class="circle"></div>
              <div class="right">{{ item.title }}</div>
              <div class="left">
                <div class="bottom">
                  <span v-if="item.time"
                    >{{ item.time.slice(0, 4) }}-{{ item.time.slice(5, 7) }}-{{
                      item.time.slice(8, 10)
                    }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 专题专栏 -->
    <div class="special-cont">
      <div class="title-box">
        <div class="left">
          <div class="title-text">专题专栏</div>
          <img style="height: 6px" src="@/assets/detail/title-icon.png" alt="" srcset="" />
        </div>
        <div @click="toMore(2)" class="more-text">查看更多>></div>
      </div>

      <!-- <img class="img1" :src="imageList[0].imgUrl"  /> -->
      <!-- 轮播图 -->
      <div v-if="swipperList.length !== 0" class="swipper-cont" ref="swipperContRef" @mouseover="swipperContMousemove" @mouseout="swipperContMouseout">
        <transition name="el-fade-in">
          <div class="left-arrow" v-if="showArrow">
            <img
              style="cursor: pointer"
              @click="leftNext"
              src="@/assets/detail/left-arrow.png"
              alt=""
              srcset=""
            />
          </div>
        </transition>
        <carousel-3d
          ref="slideCarousel"
          disable3d
          height="182"
          width="295"
          space="315"
          :clickable="false"
          :border="0"
        >
          <slide v-for="(item, i) in swipperList" :index="i" :key="i">
            <template slot-scope="obj">
              <div class="carousel-item" @click="goTab(item)">
                <img
                  :class="[
                    obj.leftIndex === 1 && obj.rightIndex === -1
                      ? 'first-box'
                      : obj.leftIndex === -1 && obj.rightIndex === 1
                      ? 'last-box'
                      : '',
                  ]"
                  class="img1"
                  :src="item.imgUrl"
                  style="height: 140px; border-radius: 8px"
                />
                <div class="swipper-title" v-if="(obj.leftIndex === 1 && obj.rightIndex === -1) || (obj.leftIndex === -1 && obj.rightIndex === 1)">
                  {{ item.title.length > 4 ? item.title.slice(0, 4) + '...' : item.title }}
                </div>
                <div class="swipper-title" v-else>
                  {{ item.title.length > 15 ? item.title.slice(0, 15) + '...' : item.title }}
                </div>
              </div>
            </template>
          </slide>
        </carousel-3d>
        <!-- <div class="swipper-item" v-for="(item, index) in swipperList" :key="index">
          <div
            @click="goTab(item)"
            class="swipper-item-box"
            v-if="swipperList.length > 4"
            :class="[
              index === 0 ? 'first-box' : index === swipperList.length - 1 ? 'last-box' : '',
            ]"
          >
            <div :class="[index === 0 ? 'img-cont1' : index === 4 ? 'img-cont2' : 'img-cont']">
              <img class="img1" :src="item.imgUrl" alt="" srcset="" />
            </div>
            <div
              class="swipper-title"
              :class="[index === 0 || index === 4 ? 'cont-width1' : 'cont-width2']"
            >
              {{ item.title.length > 15 ? item.title.slice(0, 15) + '...' : item.title }}
            </div>
          </div>
          <div @click="goTab(item)" v-else class="swipper-item-box">
            <div class="img-cont"><img class="img1" :src="item.imgUrl" alt="" srcset="" /></div>
            <div class="swipper-title">
              {{ item.title.length > 15 ? item.title.slice(0, 15) + '...' : item.title }}
            </div>
          </div>
        </div> -->
        <transition name="el-fade-in">
          <div class="right-arrow" v-if="showArrow">
            <img
              style="cursor: pointer"
              @click="rightNext"
              src="@/assets/detail/right-arrow.png"
              alt=""
              srcset=""
            />
            <!-- <img v-else style="opacity: 0" src="@/assets/detail/right-arrow.png" alt="" srcset="" /> -->
          </div>
        </transition>
      </div>
      <div v-else class="swipper-cont swipper-cont1">
        <div class="empty-text">暂无数据</div>
      </div>
    </div>
  </div>
</template>
  
  <script>
import empty from '@/views/empty/index.vue';
import { Carousel3d, Slide } from 'vue-carousel-3d';
export default {
  components: {
    empty,
    Carousel3d,
    Slide,
  },
  data() {
    return {
      imgData: [], // 轮播图
      newsList: [], // 新闻中心
      activeTab: 1,
      activeTitle: '工作动态',
      tabList: [
        { title: '工作动态', order: 1 },
        { title: '行业新闻', order: 2 },
      ],

      imageList: [
        {
          imgUrl: require('@/assets/ztzlImg/img1.png'),
          title: '党的二十届三中全会精神',
          sort: '1',
        },
        {
          imgUrl: require('@/assets/ztzlImg/img2.png'),
          title: '党风廉政',
          sort: '2',
        },
        {
          imgUrl: require('@/assets/ztzlImg/img3.png'),
          title: '知识科普合集',
          sort: '3',
        },
        {
          imgUrl: require('@/assets/ztzlImg/img4.png'),
          title: '国家安全',
          sort: '4',
        },
        {
          imgUrl: require('@/assets/ztzlImg/img5.png'),
          title: '庆祝建党100周年',
          sort: '5',
        },
        {
          imgUrl: require('@/assets/ztzlImg/img6.png'),
          title: '高水平服务高质量发展',
          sort: '6',
        },
        {
          imgUrl: require('@/assets/ztzlImg/img7.png'),
          title: '职业病防治法',
          sort: '7',
        },
        {
          imgUrl: require('@/assets/ztzlImg/img8.png'),
          title: '安全生产15条措施',
          sort: '8',
        },
        {
          imgUrl: require('@/assets/ztzlImg/img9.png'),
          title: '信息公开',
          sort: '9',
        },
        {
          imgUrl: require('@/assets/ztzlImg/img10.png'),
          title: '学习贯彻习近平新时代中国特色社会主义思想',
          sort: '10',
        },
        {
          imgUrl: require('@/assets/ztzlImg/img11.png'),
          title: '采购意向公告',
          sort: '11',
        },
      ],
      swipperList: [],
      startIndex: 0,
      endIndex: 5,
      requestUrl: '/xwzx/gzdt/',
      rightArrowInterval: null,
      showArrow: false,
    };
  },
  mounted() {
    // 获取轮播图
    // this.getImgData();
    // 专题专栏
    //  this.getSpecialNews('/xwzx/ztzl/');
    // 新闻中心
    this.getNewstrends(this.requestUrl, '1');
    this.swipperList = this.imageList;
    // if (this.imageList.length < 5) {
    //   this.swipperList = this.imageList;
    // } else {
    //   this.swipperList = this.imageList.slice(this.startIndex, this.endIndex);
    // }
    this.$nextTick(()=>{
        this.rightArrowInterval = setInterval(()=>{
            this.rightNext()
        },3000)
    })
  },
  methods: {
    // 鼠标悬浮事件
    swipperContMousemove(){
        this.showArrow = true;
        this.rightArrowInterval && clearInterval(this.rightArrowInterval)
    },
    // 鼠标移出事件
    swipperContMouseout(){
        this.showArrow = false;
        this.rightArrowInterval = setInterval(()=>{
            this.rightNext()
        },5000)
    },
    // 获取轮播图
    getImgData() {
      this.$ajax({
        url: '/xwzx/gzdt/',
        method: 'get',
        serverName: 'nd-ss',
      }).then((r) => {
        let newArr = eval(r.data);
        if (newArr.length > 4) {
          this.imgData = newArr.slice(0, 5);
        } else {
          this.imgData = newArr;
        }
      });
    },

    // 获取新闻数据
    getNewstrends(url, flag) {
      this.$ajax({
        url: url,
        method: 'get',
        serverName: 'nd-ss',
      }).then((r) => {
        let newArr = eval(r.data);
        if (flag === '1') {
          // 轮播图
          if (newArr.length > 4) {
            this.imgData = newArr.slice(0, 5);
          } else {
            this.imgData = newArr;
          }
          if (newArr.length > 7) {
            this.newsList = newArr.slice(0, 8);
          } else {
            this.newsList = newArr;
          }
        } else if (flag === '2') {
          if (newArr.length > 7) {
            this.newsList = newArr.slice(0, 8);
          } else {
            this.newsList = newArr;
          }
          // if (newArr.length > 4) {
          //   this.imgData = newArr.slice(0, 5);
          // } else {
          //   this.imgData = newArr;
          // }
        }
      });
    },

    // 获取新闻数据
    getSpecialNews(url) {
      this.$ajax({
        url: url,
        method: 'get',
        serverName: 'nd-ss',
      }).then((r) => {
        let newArr = eval(r.data);
        this.imageList = newArr;
        if (this.imageList.length < 5) {
          this.swipperList = this.imageList;
        } else if (this.imageList.length >= 5) {
          this.swipperList = this.imageList.slice(this.startIndex, this.endIndex);
        }
      });
    },

    // 跳转详情
    handleMore(item, title) {
      this.$router.push({
        path: 'listDetailView',
        query: {
          path: item.path,
          bannerType: 1,
          breadcrumbName: title,
        },
      });
    },

    // 点击more
    toMore(mark) {
      // this.$router.push('newsCenter');
      if (mark === 1) {
        this.$router.push({
          path: 'newsCenter',
          query: {
            order: this.activeTab,
          },
        });
      } else {
        this.$router.push({
          path: 'newsCenter',
          query: {
            order: 3,
          },
        });
      }
    },

    tabActive(item, index) {
      this.activeTab = item.order;
      this.activeTitle = this.tabList[index].title;
      if (item.order === 1) {
        this.getNewstrends('/xwzx/gzdt/?a=1', '1');
      } else if (item.order === 2) {
        this.getNewstrends('/xwzx/hyxw/?a=2', '2');
      }
    },

    rightNext() {
      //   if (this.imageList.length > 4 && this.endIndex === 4) {
      //     this.startIndex = 0;
      //     this.endIndex = 5;
      //   } else if (
      //     this.startIndex === this.imageList.length - 5 &&
      //     this.endIndex === this.imageList.length
      //   ) {
      //     this.startIndex = this.imageList.length - 4;
      //     this.endIndex = this.imageList.length;
      //   } else {
      //     if (this.endIndex < this.imageList.length) {
      //       this.startIndex++;
      //       this.endIndex++;
      //     }
      //   }

      // this.startIndex++;
      // this.endIndex++;

      // this.swipperList = this.imageList.slice(this.startIndex, this.endIndex);
      try{
        this.$refs.slideCarousel.goNext();
      } catch (error){}
    },

    leftNext() {
      //   if (this.startIndex === 0 && this.endIndex === 5) {
      //     this.startIndex = 0;
      //     this.endIndex = 4;
      //   } else if (this.imageList.length > 4 && this.startIndex === this.imageList.length - 4) {
      //     this.startIndex = this.imageList.length - 5;
      //     this.endIndex = this.imageList.length;
      //   } else {
      //     this.startIndex--;
      //     this.endIndex--;
      //   }

      // this.startIndex--;
      // this.endIndex--;

      // this.swipperList = this.imageList.slice(this.startIndex, this.endIndex);
      this.$refs.slideCarousel.goPrev();
    },
    goTab(item) {
      console.log(item, '切换地址');
      this.$router.push({
        path: 'newsCenter',
        query: {
          order: 3,
          sort: item.sort,
        },
      });
    },
  },
};
</script>
  
  <style lang="scss" scoped>
@font-face {
  font-family: 'MyCustomFont';
  src: url('@/assets/fonts/impact.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
.news {
  width: 100%;
  background-color: #f7f7f7;
  padding: 20px 0;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  .news-center {
    padding: 30px 18px 30px 20px;
    margin-bottom: 20px;

    width: 1300px;
    // height: 486px;
    background: #ffffff;
    border-radius: 8px;

    .title-box {
      width: 100%;
      //   height: 48px;
      display: flex;
      justify-content: space-between;
      // align-items: center;
      margin-bottom: 20px;
      user-select: none;
      // background-color: red;
      //   border-bottom: 1px solid #e5e5e5;

      .left {
        display: flex;
        flex-direction: column;
        // align-items: center;
        height: 32px;
        // .icon {

        // }
        .title-text {
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 24px;
          color: #333333;
          line-height: 24px;
        }
      }
    }

    .main {
      display: flex;
      justify-content: space-between;
      //   height: 384px;

      .main-left {
        width: 624px;
        height: 384px;
        border-radius: 8px;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
        }
        .title {
          position: absolute;
          left: 0;
          bottom: 0px;
          padding-left: 20px;
          overflow: hidden;
          white-space: nowrap;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 24px;
          height: 40px;
          width: 100%;
          background: rgba(51, 51, 51, 0.8);
          border-radius: 0px 0px 8px 8px;
          display: flex;
          align-items: center;
        }

        ::v-deep .el-carousel__item {
          border-radius: 8px;
        }
        ::v-deep .el-carousel__button {
          width: 10px;
          height: 10px;
          // background: rgba(255, 255, 255, 0.3);
          background: #fff;
          opacity: 1;
          border-radius: 50%;
        }

        ::v-deep .el-carousel__indicator.is-active button {
          background: #ff9c00;
        }
        ::v-deep .el-carousel__indicators--horizontal {
          bottom: 2px;
          width: 100%;
          text-align: right;
          padding: 0 20px;
        }
        // ::v-deep .el-carousel__button {
        //   width: 6px;
        //   height: 6px;
        //   background: rgba(255, 255, 255, 0.3);
        //   border-radius: 50%;
        // }
        // ::v-deep .el-carousel__indicator.is-active button {
        //   background: #ED911F;
        // }
      }
      .main-right {
        // width: 624px;
        // height: 384px;
        display: flex;
        flex-direction: column;
        flex: 1;
        margin-left: 30px;

        .right-tab {
          display: flex;
          flex-direction: column;
          margin-bottom: 10px;
          .tab-box {
            display: flex;
            flex-direction: row;
            position: relative;

            .more-text {
              position: absolute;
              right: 0px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 16px;
              color: #666666;
              line-height: 24px;
              cursor: pointer;
            }
          }

          .tab-item:nth-child(1) {
            margin-right: 25px;
          }

          .tab-item {
            margin-bottom: 15px;
            cursor: pointer;
          }

          .tab-deactive {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 20px;
            color: #666666;
            line-height: 24px;
          }

          .tab-active {
            font-family: Microsoft YaHei;
            font-weight: bold;
            font-size: 20px;
            color: #ed911f;
            line-height: 24px;
          }

          .active-line {
            width: 80px;
            height: 2px;
            background: #ed911f;
          }

          .line {
            width: 100%;
            height: 1px;
            background: #e5e5e5;
          }
        }

        .right-cont {
          flex-grow: 1;
        }

        .right-item:not(:last-child) {
          // margin-bottom: 20px;
        }

        .right-item {
          display: flex;
          align-items: center;
          cursor: pointer;
          &:hover {
            box-shadow: 0px 0px 10px 0px rgba(32, 32, 32, 0.1);
          }

          .circle {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #ed911f;
            margin-right: 9px;
          }

          .left {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            // margin-right: 12px;

            .bottom {
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 14px;
              color: #999999;
              line-height: 18px;
              white-space: nowrap;
            }
          }
          .right {
            width: 500px;
            height: 41px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 16px;
            line-height: 41px;
            color: #333333;
          }
          &:hover {
            .right {
              color: #ed911f;
            }

            .bottom {
              color: #ed911f;
            }
          }
        }
      }
    }
  }
  .politics-inform {
    display: flex;
    justify-content: space-between;
    width: 1300px;
    // height: 408px;
    margin: auto;
    // background: red;
    > div {
      display: flex;
      flex-direction: column;
      width: 640px;
      // height: 408px;
      border-radius: 8px;
      padding: 16px;
      background: #ffffff;

      .title-box {
        width: 100%;
        height: 48px;
        display: flex;
        justify-content: space-between;
        // align-items: center;
        margin-bottom: 20px;
        user-select: none;
        // background-color: red;
        border-bottom: 1px solid #e5e5e5;

        .left {
          display: flex;
          // align-items: center;
          height: 32px;
          .icon {
            width: 4px;
            height: 22px;
            background: #ed911f;
            margin-right: 12px;
            margin-top: 5px;
          }
          .title-text {
            line-height: 32px;
            font-size: 24px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            color: #ed911f;
          }
        }
      }

      .main {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        // justify-content: space-between;
        gap: 20px;

        .item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 21px;
          cursor: pointer;

          .left {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 495px;
            .icon {
              width: 6px;
              height: 6px;
              height: 6px;
              background: #ed911f;
              border-radius: 50%;
            }
            .title {
              width: 481px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              color: #3886fb;
            }
          }
          .right {
            width: 78px;
            height: 18px;
            font-size: 14px;
            color: #999999;
          }

          &:hover {
            .left .title {
              color: #ed911f;
            }
          }
        }
      }
    }
  }

  .special-cont {
    width: 1300px;
    // height: 270px;
    background: #ffffff;
    border-radius: 8px;
    padding: 20px;

    .title-box {
      width: 100%;
      //   height: 48px;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      user-select: none;

      .left {
        display: flex;
        flex-direction: column;
        // height: 30px;

        .title-text {
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 24px;
          color: #333333;
          line-height: 24px;
        }
      }

      .more-text {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 24px;
        cursor: pointer;
      }
    }

    .swipper-cont1 {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }

    .swipper-cont {
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      // justify-content: center;
      height: 188px;

      .left-arrow, .right-arrow {
        position: absolute;
        // top: 50%;
        z-index: 9999;
        transform: translateY(-50%);
      }

      .left-arrow {
        left: 0;
        margin-right: 20px;
      }

      .right-arrow {
        right: 0;
        margin-left: 20px;
      }

      //   .swipper-item:not(:last-child) {
      //     margin-right: 20px;
      //   }

      .swipper-item {
        margin-right: 20px;
        cursor: pointer;

        .first-box {
          -webkit-mask-image: linear-gradient(
            to left,
            rgba(0, 0, 0, 1) calc(100% - 2em),
            transparent 8
          );
          mask-image: linear-gradient(to left, rgba(0, 0, 0, 1) calc(100% - 2em), transparent);
        }
        .last-box {
          -webkit-mask-image: linear-gradient(
            to right,
            rgba(0, 0, 0, 1) calc(100% - 2em),
            transparent 8
          );
          mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) calc(100% - 2em), transparent);
        }

        .swipper-item-box {
          height: 188px;
          overflow: hidden;

          img {
            border-radius: 8px;
            height: 100%;
          }
        }
      }
      .img-cont {
        width: 295px;
        height: 140px;
        overflow: hidden;

        .img1 {
          width: 100%;
          height: 100%;
        }
      }

      .img-cont1 {
        width: 100px;
        height: 140px;
        overflow: hidden;
      }

      .img-cont2 {
        width: 100px;
        height: 140px;
        overflow: hidden;
      }

      .swipper-title {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 18px;
        color: #333333;
        // display: flex;
        // justify-content: center;
        margin-top: 16px;
      }

      .cont-width1 {
        width: 100px;
      }

      .cont-width2 {
        width: 295px;
      }
    }
  }

  .empty-text {
    font-family: Microsoft YaHei;
    font-weight: 400;
    font-size: 24px;
    color: #999999;
    line-height: 36px;
  }
}

.carousel-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;

  .img1 {
    width: 100%;
    height: 100%;
  }

  .first-box {
    transform: translateX(50%);
    width: 50%;
    -webkit-mask-image: linear-gradient(to left, rgba(0, 0, 0, 1) 20%, transparent 8);
    mask-image: linear-gradient(to left, rgba(0, 0, 0, 1) 20%, transparent);
  }
  .last-box {
    width: 50%;
    transform: translateX(-50%);
    -webkit-mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) 20%, transparent 8);
    mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) 20%, transparent);
  }
}

::v-deep .carousel-3d-slide {
  background-color: #fff;
}

.left-2 {
  .carousel-item {
    .swipper-title {
      display: flex;
      justify-content: center;
      width: 90px;
      transform: translateX(100px);

      -webkit-mask-image: linear-gradient(
        to left,
        rgba(0, 0, 0, 1) calc(100% - 2em),
        transparent 10
      );
      mask-image: linear-gradient(to left, rgba(0, 0, 0, 1) calc(100% - 2em), transparent);
    }
  }
}

.right-2 {
  .carousel-item {
    .swipper-title {
      // white-space: nowrap;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // padding-left: 10px;
      width: 90px;
      transform: translateX(-100px);
      display: flex;
      justify-content: center;

      -webkit-mask-image: linear-gradient(
        to right,
        rgba(0, 0, 0, 1) calc(100% - 2em),
        transparent 10
      );
      mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) calc(100% - 2em), transparent);
    }
  }
}
.more-text:hover{
  color: #ed911f!important;
}
</style>
