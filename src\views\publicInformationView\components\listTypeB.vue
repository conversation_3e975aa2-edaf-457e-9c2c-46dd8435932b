<template>
  <div class="main-cont">
    <div v-if="listData.length !== 0" class="list-content">
      <div
        class="list-item"
        @click.stop="handleMore(item)"
        v-for="(item, index) in listData"
        :key="index"
      >
        <div class="item-top">
          <img class="img1" src="@/assets/detail/jzs.png" alt="" />
          <div class="title" @click.stop="handleMore(item)" :title="item.title">
            {{ item.name }}
          </div>
        </div>
        <div class="item-bottom">
          <div class="left-item">
            <div class="item">
              <div>鉴证编号：</div>
              <div>{{ item.code }}</div>
            </div>
            <div class="item">
              <div>鉴证品种：</div>
              <div>
                {{
                  item.type === '2'
                    ? '变更类'
                    : item.type === '3'
                    ? '遗失类'
                    : item.type === '4'
                    ? '注销类'
                    : ''
                }}
              </div>
            </div>
            <div class="item">
              <div>鉴证时间：</div>
              <div>{{ item.time }}</div>
            </div>
          </div>
          <div class="right-item" @click.stop="handleMore(item)">
            <div class="text">查看详情</div>
            <i class="el-icon-right"></i>
          </div>
        </div>
        <div class="line" v-if="index !== listData.length - 1"></div>
      </div>
    </div>
    <div v-else class="list-content">
      <empty :boxHeight="300"></empty>
    </div>
    <!-- 分页器 -->
    <div class="pagination">
      <nd-pagination
        :page-size="pager.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="pager.total"
        :total-page="totalPage"
        :current-page="pager.pageNo"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import ndPagination from '@/components/ndPagination.vue';
import empty from '@/views/empty/index.vue';

export default {
  name: 'list',
  components: {
    ndPagination,
    empty,
  },
  props: {
    requestUrl: {
      type: String,
      default: '',
    },
    breadcrumbName: {
      type: String,
      default: '资讯详情',
    },
    keyWords: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      showDownLoad: false,
      listData: [],
      pager: {
        pageNo: 1, // 当前页数
        pageSize: 10, // 当前页条数
        total: 0, // 总条目数
        pageSizes: [10, 30, 50, 100],
      },
      totalPage: 1,
      searchKey: '', //模糊查询
    };
  },
  mounted() {
    this.searchKey = this.$props.keyWords;
    this.getData();
    this.$bus.$on('searchData', (searchKey) => {
      this.searchKey = searchKey;
      this.getData();
    });

    if ('showDownLoad' in this.$attrs && this.$attrs.showDownLoad !== false)
      this.showDownLoad = true;
  },
  methods: {
    getData() {
      this.$ajax({
        url: this.$props.requestUrl,
        method: 'post',
        data: {
          type: 2,
          pageNo: this.pager.pageNo,
          pageSize: this.pager.pageSize,
          keyWords: this.searchKey,
        },
      }).then((res) => {
        if (res.data.code === 200) {
          this.listData = res.data.data.records;
          this.pager.total = res.data.data.total;
          this.totalPage = res.data.data.size;
        } else {
          console.log('数据格式有误');
        }
      });
    },

    // 跳转详情
    handleMore(item) {
      this.$router.push({
        path: 'listDetailView',
        query: {
          path: '',
          bannerType: 2,
          breadcrumbName: '鉴定公告',
          isHtml: true,
          id: item.id,
          times: item.time,
        },
      });
    },

    //  分页器函数
    handleCurrentChange(e) {
      this.pager.pageNo = e;
      this.getData();
    },
    handleSizeChange(e) {
      this.pager.pageSize = e;
      this.pager.pageNo = 1;
      this.getData();
    },

    // 获取年份
    // getYear(time) {
    //   if (!time) return '';
    //   return new Date(time).getFullYear();
    // },
    // 获取月份日期
    // getDate(time) {
    //   if (!time) return '';
    //   let month = new Date(time).getMonth() + 1 + '';
    //   month.padStart(2, '0');
    //   let day = new Date(time).getDate() + '';
    //   day.padStart(2, '0');
    //   return month + '-' + day;
    // },
  },
  beforeDestroy() {
    // 销毁自定义事件
    this.$bus.$off(['searchData']);
  },
};
</script>
<style lang="scss" scoped>
.main-cont {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.list-content {
  width: 1300px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e5e5;
  margin-top: 15px;
  padding: 26px 21px 17px 20px;

  .item-top {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 19px;

    .img1 {
      margin-right: 14px;
    }

    .title {
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      cursor: pointer;
    }
  }

  .item-bottom:not(:last-child) {
    margin-bottom: 20px;
  }

  .item-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;

    .left-item {
      display: flex;
      align-items: center;
      flex-direction: row;

      .item:nth-child(-n + 2) {
        margin-right: 70px;
      }

      .item {
        display: flex;
        align-items: center;
        flex-direction: row;
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
      }
    }

    .right-item {
      display: flex;
      align-items: center;
      flex-direction: row;
      color: #ed911f;
      cursor: pointer;

      .text {
        margin-right: 10px;
      }
    }
  }

  .line {
    width: 100%;
    height: 1px;
    background: #e5e5e5;
    margin-bottom: 21px;
  }
}

.pagination {
  width: 100%;
  margin-top: 14px;

  :v-deep .el-pagination button:disabled {
    background: none;
  }

  ::v-deep .el-pagination .btn-next,
  .el-pagination .btn-prev {
    background: none;
  }

  ::v-deep .el-pagination .btn-prev:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pagination .btn-next:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pager li {
    background: none;
  }

  ::v-deep .el-pager li.active {
    color: #fff;
    background: #f2ae0f;
  }

  ::v-deep .el-pager li.active:hover {
    color: #fff;
  }

  ::v-deep .el-pager li:hover {
    color: #f2ae0f;
  }
}
</style>