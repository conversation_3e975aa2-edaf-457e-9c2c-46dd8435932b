const { defineConfig } = require("@vue/cli-service");

module.exports = defineConfig({
  lintOnSave: false,
  transpileDependencies: true,
  publicPath: "./",
  devServer: {
    host: '0.0.0.0',
    port: 8080,
    proxy: {
      // 研发后端
      "/nd-base": {      
        target: "https://whnccq.com/manage", // 生产
        // target:"http://************:18080", //测试
        // target: "http://**************:8080", // 左兴国
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/nd-base': '', // 测试
        },
      },


      // 实施后端
      "/nd-ss": {
        target: "http://***********:9754/cqjy-jcms-admin/",
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/nd-ss': ''
        },
      },
    },
  },

  // filenameHashing: false,
  productionSourceMap: false,

  // chainWebpack: config => {
  //   config.resolve.alias.set('@', resolve('src'));
  //   config.performance.set('hints', false);
  //   config.output.filename('[name].[hash].js').end();
  // },
});
