<template>
  <div class="table-box">
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="{
        'text-align': 'center',
        'font-size': '14px',
        color: '#333',
        'font-weight': 'bold',
        background: '#D4F5FA',
      }"
      :cell-style="{
        'font-size': '14px',
        'font-family': 'Microsoft YaHei',
        'font-weight': '400',
        color: '#333333',
      }"
    >
      <el-table-column prop="date" label="他项权利证书编号" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="address" label="融资地区" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column
        prop="name"
        label="抵押物类型"
        width="180"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="name"
        label="贷款金额（元）"
        width="130"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="date"
        label="登记日期"
        width="130"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
    </el-table>
  </div>
</template>
  
  <script>
export default {
  data() {
    return {
      tableData: [
        {
          date: '2016-05-02',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1518 弄',
          date: '2024-04-15',
        },
        {
          date: '2016-05-04',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1517 弄',
          date: '2024-04-15',
        },
        {
          date: '2016-05-01',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄',
          date: '2024-04-15',
        },
        {
          date: '2016-05-03',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1516 弄',
          date: '2024-04-15',
        },
        {
          date: '2016-05-01',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄',
          date: '2024-04-15',
        },
        {
          date: '2016-05-03',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1516 弄',
          date: '2024-04-15',
        },
        {
          date: '2016-05-01',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄',
          date: '2024-04-15',
        },
        {
          date: '2016-05-03',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1516 弄',
          date: '2024-04-15',
        },
      ], // 表格数据
      list: [], //初始化数据列表
      options: [],
      jypz: '',
    };
  },
  mounted() {
    // this.getData();
    // this.getProType();
  },
  methods: {
    toMore() {
      this.$router.push('projectInformationView?type=1');
    },
    // 跳转 详情
    goDetail(item) {
      let type;
      let query = [];
      if (item.xmStatus == 2) type = 1;
      if (item.xmStatus == 4) type = 2;
      query = [{ name: '首页', route: '/', query: { type: type } }];
      // this.$router.push({ name: "details", query: { id: item.tendersId, title: item.proName, type: 3, route: JSON.stringify(query) } })
      if (window.location.origin == 'http://localhost:8080') {
        window.open(
          window.location.origin +
            `/#/details?id=${item.tendersId}&route=${JSON.stringify(query)}&type=3&homeType=1`,
        );
      } else {
        window.open(
          window.location.origin +
            window.location.pathname +
            `#/details?id=${item.tendersId}&route=${JSON.stringify(query)}&type=3&homeType=1`,
        );
      }
    },
    getData() {
      //---初始化数据
      this.$ajax({
        url: '/notice/noticeInfo',
        method: 'post',
        data: {
          bmEndFirst: '',
          bmEndSecond: '',
          cjDateEnd: '',
          cjDateStart: '',
          fromHome: '1',
          jymjEnd: '',
          jymjStart: '',
          keyWords: '',
          noticeType: '1',
          order: '',
          orderType: '',
          page: 1,
          proTypeId: this.jypz,
          proTypeParentId: '',
          size: 8,
          unitId: '',
          xmStatus: '',
        },
      }).then((response) => {
        if (response.data.code == 200) {
          this.list = response.data.data.records;
        }
      });
    },
    getProType() {
      //---初始化首页交易品种
      this.$ajax({
        url: '/baseInfo/webHomeProType',
        method: 'get',
      }).then((response) => {
        if (response.data.code == 200) {
          this.options = response.data.data;
        }
      });
    },
    handleChange(node) {
      this.jypz = node;
      this.getData();
    },
  },
};
</script>
  
  <style lang="scss" scoped>
  .table-box {
      ::v-deep .el-table--fit {
        border-radius: 10px 10px 0px 0px;
        border: 1px solid #d4f5fa;
      }

      ::v-deep .el-table .el-table__cell.gutter {
        background: #d4f5fa;
      }
    }
</style>
  