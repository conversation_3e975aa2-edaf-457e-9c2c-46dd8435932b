<template>
  <div class="project-info" v-loading="loading">
    <!-- tab切换 -->
    <div class="tab-box">
      <div class="w tab-type">
        <span v-for="(item, index) in tabIndexList" :key="index" :class="{ active: form.type == index + 1 }"
          @click="toggle(index)">{{ item }}</span>
      </div>
    </div>
    <template>
      <div class="search-box">
        <div class="w">
          <div class="row">
            <div class="row-left">交易品种:</div>
            <div class="row-right">
              <span :class="{ active: form.tradeVariety == item.dataKey }"
                v-for="(item, index) in formList.tradeCategoryList" :key="index" @click="changeTrade(item)">{{
                  item.dataValue }}</span>
            </div>
          </div>
          <div class="row" v-show="form.tradeVariety">
            <div class="row-left"></div>
            <div class="row-right child">
              <span :class="{ active: form.childTradeVariety == item.dataKey }"
                v-for="(item, index) in formList.tradeCategoryList2" :key="index"
                @click="form.childTradeVariety = item.dataKey">{{ item.dataValue }}</span>
            </div>
          </div>
          <div class="row">
            <div class="row-left">所属地区:</div>
            <div class="row-right">
              <span :class="{ active: form.projectArea == item.id }" v-for="(item, index) in formList.projectAreaList"
                :key="index" @click="changeArea(item, 1)">{{ item.name }}</span>
            </div>
          </div>
          <div class="row" v-show="form.projectArea && formList.projectAreaList2">
            <div class="row-left"></div>
            <div class="row-right child">
              <div>
                <span :class="{ active: form.projectArea2 == item.id }"
                  v-for="(item, index) in formList.projectAreaList2" :key="index" @click="changeArea(item, 2)">{{
                    item.name }}</span>
              </div>
              <!-- <div v-show="form.projectArea2 && formList.projectAreaList3">
                <span :class="{ active: form.projectArea3 == '' }" style="margin-right: 16px"
                  @click="changeArea(item, 3)">全部</span>
                <ndRadio v-model="form.projectArea3" v-for="(item, index) in formList.projectAreaList3" :key="index"
                  :label="item.id">{{ item.name }}</ndRadio>
              </div>
              <div> -->
              <span :class="{ active: form.projectArea3 == item.id }"
                v-show="form.projectArea2 && formList.projectAreaList3"
                v-for="(item, index) in formList.projectAreaList3" :key="index" @click="changeArea(item, 3)">{{
                  item.name }}</span>

              <div v-show="form.projectArea3 && formList.projectAreaList4">
                <span :class="{ active: form.projectArea4 == '' }" style="margin-right: 16px"
                  @click="changeArea({ id: '', name: '全部' }, 4)">全部</span>
                <ndRadio v-model="form.projectArea4" v-for="(item, index) in formList.projectAreaList4" :key="index"
                  :label="item.id">{{ item.name }}</ndRadio>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="row-left">供需状态:</div>
            <div class="row-right">
              <span :class="{ active: form.status == item.dataKay }" v-for="(item, index) in formList.projectStatusList"
                :key="index" @click="form.status = item.dataKay">{{ item.dataValue }}</span>
            </div>
          </div>

          <div>
            <div class="row">
              <div class="row-left">交易面积:</div>
              <div class="row-right">
                <span :class="{ active: form.areaRange == item.dataKey }"
                  v-for="(item, index) in formList.areaRangeList" :key="index" @click="form.areaRange = item.dataKey">{{
                    item.dataValue }}</span>
              </div>
            </div>
          </div>
          <div style="display:flex;justify-content:space-between">
            <div class="row">
              <div class="row-left">项目查询:</div>
              <div class="row-right">
                <nd-input :width="'415px'" maxlength="200" v-model.trim="form.codeOrName" v-if="form.type == 1"
                  placeholder="请输入项目名称/供给编号"></nd-input>
                <nd-input :width="'415px'" maxlength="200" v-model.trim="form.codeOrName" v-if="form.type == 2"
                  placeholder="请输入项目名称/需求编号"></nd-input>
              </div>
            </div>
          </div>
          <div class="btn-box">
            <nd-button type="primary" @click="search" style="height: 34px;width: 90px;background: rgb(237, 145, 31);border: 1px solid rgb(237, 145, 31);color: rgb(255, 255, 255);">查询</nd-button>
            <nd-button @click="reset"
              style="height: 34px; width: 90px;color: rgb(237, 145, 31);border: 1px solid rgb(237, 145, 31);">重置</nd-button>
          </div>

        </div>
      </div>
      <!-- sort 排序 -->
      <div class="sort-box w">
        <div class="w sort">
          <div class="item1" v-for="(item, index) in sortList" :key="index" @click="sort(item, index)">
            <span :class="{ active: item.dataKey == orderField }">{{ item.dataValue }}</span>
            <div class="iconBox" v-if="item.isJT">
              <img v-if="item.dataKey == orderField && item.isFlag" src="@/assets/projectInformation/top.png" alt="" />
              <img v-else-if="item.dataKey == orderField && !item.isFlag" src="@/assets/projectInformation/bottom.png"
                alt="" />
            </div>
          </div>
        </div>
      </div>
      <!-- list 数据 -->
      <div class="list-box w">
        <template v-if="dataList.length > 0">
          <div class="table-box">
            <div class="item" v-for="(item, index) in dataList" :key="index">
              <div class="img">
                <div class="title-tag1" v-if="item.status == 4">无洽谈</div>
                <div class="title-tag2" v-if="item.status == 5">洽谈中</div>
                <div class="title-tag3" v-if="item.status == 6">意向成交</div>
                <el-carousel :interval="5000" class="carousel" v-if="item.picList && item.picList.length > 0">
                  <el-carousel-item v-for="(item, index) in item.picList" :key="index">
                    <img src="@/assets/images/Pdf.png" alt="" style="width: 100%; height: 100%;object-fit: contain;"
                      v-if="item.includes('.pdf')" />
                    <img :src="item" alt="" style="width: 100%; height: 100%" v-else />
                  </el-carousel-item>
                </el-carousel>
                <img v-else :src="defaultUrl" alt="" style="width: 100%;height: 100%;">
              </div>
              <!-- 哈哈 -->
              <div class="content">
                <div class="title">
                  <div class="name">
                    <el-tooltip class="box-item" effect="dark" :content="item.projectName" placement="top">
                      <div class="name">
                        {{ item.projectName }}
                      </div>
                    </el-tooltip>
                  </div>
                  <div class="right">
                    <div class="dw">
                      <img src="@/assets/images/vector.png" alt="">
                      <div class="dw-name">
                        <el-tooltip class="box-item" effect="dark"
                          :content="item.cityName + item.countyName + item.townName + item.villageName" placement="top">
                          <div class="dw-name">
                            {{ item.cityName }}{{ item.countyName }}{{ item.townName }}{{
                              item.villageName }}
                          </div>
                        </el-tooltip>
                      </div>
                    </div>
                    <div class="dw" style="margin-left: 20px;">
                      <img style="width: 16px;height: 16px;" src="@/assets/images/view.png" alt="">
                      <div class="dw-name">
                        <el-tooltip class="box-item" effect="dark" :content="item.childTradeVarietyName"
                          placement="top">
                          <div class="dw-name">{{ item.childTradeVarietyName }}</div>
                        </el-tooltip>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="info">
                  <div class="info-item">
                    {{ form.type == 1 ? '供应' : '意向' }}报价：
                    <!-- <div class="tot">{{ item.tradePriceB }}</div> -->
                    <el-tooltip class="box-item" effect="dark" :content="item.tradePriceB" placement="top">
                        <div class="tot">{{ item.tradePriceB }}</div>
                      </el-tooltip>
                    {{
                      item.tradeUnitName }}
                  </div>
                  <div class="info-item">
                    {{ form.type == 1 ? '供应' : '意向' }}面积：
                    <!-- <div class="tot">{{ item.tradeArea }}</div> -->
                    <el-tooltip class="box-item" effect="dark" :content="item.tradeArea" placement="top">
                        <div class="tot">{{ item.tradeArea }}</div>
                      </el-tooltip>
                    {{
                      item.tradeAreaUnitName }}
                  </div>
                  <div class="info-item">
                    流转期限：
                    <!-- <div class="tot">{{ item.yearNum }}</div> -->
                    <el-tooltip class="box-item" effect="dark" :content="item.yearNum" placement="top">
                        <div class="tot">{{ item.yearNum }}</div>
                      </el-tooltip>
                    年
                  </div>
                  <div class="info-item">
                    联系方式：<div class="tot">
                      <el-tooltip class="box-item" effect="dark" :content="item.linker" placement="top">
                        <div class="tot">{{ item.linker }}</div>
                      </el-tooltip>
                    </div>
                    <!-- <div class="tot">{{ item.linkPhone }}</div> -->
                    <el-tooltip class="box-item" effect="dark" :content="item.linkPhone" placement="top">
                        <div class="tot">{{ item.linkPhone }}</div>
                      </el-tooltip>
                  </div>
                </div>
                <div class="text">
                  <div class="text-div">
                    <span class="text-title">意向描述： </span>
                    <el-tooltip class="box-item" effect="dark" :content="item.description" placement="top">
                      <span class="text-content">
                        {{ item.description }}
                      </span>
                    </el-tooltip>

                  </div>
                </div>
                <div class="btn">
                  <div class="qtcs">{{ item.talkTimes > 0 ? item.talkTimes + '次洽谈' : '' }}</div>
                  <div>
                    <nd-button style="background: rgb(237, 145, 31);border: 1px solid rgb(237, 145, 31);color: rgb(255, 255, 255);" @click="goNegotiation(item)" type="primary" v-if="item.status != 6">
                      <img style="margin-right: 6px;" src="@/assets/images/phone.png" alt="">
                      意向洽谈</nd-button>
                    <!-- <nd-button @click="goDetail(item)" icon="Check" type="success">意向成交</nd-button> -->
                  </div>
                </div>
              </div>
            </div>



          </div>

        </template>

        <template v-else>
          <div class="noneBox">
            <div class="noneImageBox">
              <img src="@/assets/projectInformation/noneData.png" alt="" />
            </div>
            <div class="noneText">暂无数据</div>
          </div>
        </template>
      </div>
      <!-- 分页器 -->
      <div class="w">
        <nd-pagination :page-size="pager.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pager.total"
          :total-page="totalPage" :current-page="pager.pageNo" @current-change="handleCurrentChange" />
      </div>




    </template>
    <el-dialog v-model="dialogVisible" :visible.sync="dialogVisible" title="意向洽谈" width="36vw">
      <div class="dialog-box">
        <div class="name" v-show="this.form.type == 1">如对此供给项目感兴趣，可按如下联系方式与供给方洽谈</div>
        <div class="name" v-show="this.form.type == 2">如对此需求项目感兴趣，可按如下联系方式与需求方洽谈</div>
        <img src="@/assets/images/phoneTwo.png" alt="">
        <div class="contacts">
          <div class="wz">联系人： </div>
          <div class="totls">{{ infoObj.linker }}</div>
        </div>
        <div class="contacts">
          <div class="wz">联系电话： </div>
          <div class="totls">{{ infoObj.linkPhone }}</div>
        </div>
      </div>
    </el-dialog>
  </div>


</template>

<script>
import ndButton from '@/components/ndButton.vue';
import ndInput from '@/components/ndInput.vue';
import ndPagination from '@/components/ndPagination.vue';
import ndDatePicker from '@/components/ndDatePicker.vue';
import ndRadio from '@/components/ndRadio.vue';
// import ndbUploadDialog from "@/components/business/ndbUpload/ndbUploadDialog.vue";

let isRouter = '';
export default {
  components: {
    ndInput,
    ndButton,
    ndDatePicker,
    ndPagination,
    ndRadio,
  },
  data() {
    return {
      dialogVisible: false,
      infoObj: {},
      defaultUrl: '',
      loading: false,
      tabIndexList: ['供给信息', '需求信息'],
      // search查询
      form: {
        type: 1,
        areaRange: '',
        tradeCategory: '',
        childTradeVariety: '',
        tradeVariety: '',
        projectArea: '',
        projectArea2: '',
        projectArea3: '',
        projectArea4: '',
        status: '',
        codeOrName: '',
        level: '',
      },
      // search查询 数组
      formList: {
        // 交易品种
        tradeCategoryList: [],
        // 交易品种2
        tradeCategoryList2: [],
        // 项目地区
        projectAreaList: [],
        // 项目地区2
        projectAreaList2: [],
        // 项目地区3
        projectAreaList3: [],
        // 项目地区4
        projectAreaList4: [],
        // 面积
        areaRangeList: [],
        // 项目状态
        projectStatusList: [
          { dataKay: '', dataValue: '全部' },
          { dataKay: '4', dataValue: '无洽谈' },
          { dataKay: '5', dataValue: '洽谈中' },
          { dataKay: '6', dataValue: '意向成交' },
        ],
      },
      // sort 排序
      orderField: '',
      orderSort: '', //  默认倒序 1倒序 2正序
      // sort 排序 数组
      sortList: [
        { dataKey: '', dataValue: '默认', isJT: false, isFlag: true },
        { dataKey: '1', dataValue: '按价格', isJT: true, isFlag: true },
        { dataKey: '2', dataValue: '按面积', isJT: true, isFlag: true },
        { dataKey: '3', dataValue: '按流转期限', isJT: true, isFlag: true },

      ],
      // list 列表
      dataList: [],
      // 分页器
      pager: {
        pageNo: 1, // 当前页数
        pageSize: 10, // 当前页条数
        total: 0, // 总条目数
      },
    };
  },
  computed: {
    totalPage() {
      return Math.ceil(this.pager.total / this.pager.pageSize);
    },
  },
  beforeRouteEnter(to, from, next) {
    console.log('前置守卫', from.fullPath);
    isRouter = from.fullPath;
    next();
  },

  mounted() {
    this.getTypeAll();
    this.getProjectArea();
    this.getData();
    this.defaultUrl = ipConfig.defaultUrl;
  },
  methods: {

    //获取数据 ============================
    getData() {
      var params = {
        areaId: this.form.projectArea4 || this.form.projectArea3 || this.form.projectArea2 || this.form.projectArea,
        areaRange: this.form.areaRange,
        childTradeVariety: this.form.childTradeVariety,
        orderField: this.orderField,
        orderSort: this.orderSort,
        cityId: '',
        codeOrName: this.form.codeOrName,
        status: this.form.status,
        tradeVariety: this.form.tradeVariety,
        type: this.form.type,
        townId: '',
        countyId: '',
        pageNo: this.pager.pageNo,
        pageSize: this.pager.pageSize,
      };
      params.level = this.form.level
      console.log('请求参数', params);
      this.loading = true;
      this.$ajax({
        method: 'post',
        url: '/web/supply/supplyGlobList',
        data: params,
      }).then((res) => {
        if (res.data.code == 200) {
          this.pager.total = res.data.data.total;
          this.dataList = res.data.data.records;
        }
        this.loading = false;
      });


    },
    // tab切换 事件 ====================================
    toggle(index) {
      this.form.type = index + 1; // 切换选中状态
      this.reset(); // 重置筛选条件
      this.getData(); // 获取数据
    },

    // 搜索条件 =================================
    // 交易品种切换
    changeTrade(item) {
      this.formList.tradeCategoryList2 = JSON.parse(JSON.stringify(item.childList))
      this.formList.tradeCategoryList2.unshift({
        dataKey: '',
        dataValue: '全部',
        childList: []
      })
      this.form.tradeVariety = item.dataKey
      console.log(this.form.tradeVariety, 'this.form.tradeVariety', item);

    },


    getTypeAll() {
      this.$ajax({
        method: 'get',
        url: '/web/supply/getVariety',
        data: {
          projectType: ''
        },
      }).then((res) => {
        if (res.data.code == 200) {
          this.formList.tradeCategoryList = res.data.data
          this.formList.tradeCategoryList.unshift({
            dataKey: '',
            dataValue: '全部',
            childList: []
          })
        }
      });

      this.$ajax({
        method: 'get',
        url: '/web/supply/getBaseCodeListByType/SUPPLY_AREA_RANGE',
      }).then((res) => {
        if (res.data.code == 200) {
          this.formList.areaRangeList = res.data.data
          this.formList.areaRangeList.unshift({
            dataKey: '',
            dataValue: '全部',
            childList: []
          })
        }
      });

    },

    // 项目地区切换
    changeArea(item, flag) {
      if (flag == 1) {
        this.form.projectArea2 = '';
        this.form.projectArea3 = '';
        this.form.projectArea4 = '';
        this.form.projectArea = item.id;
        this.form.level = item.level;
        this.formList.projectAreaList2 = this.getProjectArea(item.id, 2);
      } else if (flag == 2) {
        this.form.projectArea3 = '';
        this.form.projectArea4 = '';
        this.form.projectArea2 = item.id;
        this.form.level = item.level;
        this.formList.projectAreaList3 = this.getProjectArea(item.id, 3);
      } else if (flag == 3) {
        console.log(item);

        this.form.projectArea4 = '';
        this.form.projectArea3 = item.id;
        this.form.level = item.level;
        this.formList.projectAreaList4 = this.getProjectArea(item.id, 4);
      } else {
        this.form.level = item.level;
        this.form.projectArea4 = item.id;
      }
    },

    // 获取项目地区的接口
    getProjectArea(areaId, flag) {
      return new Promise((resolve, reject) => {
        if (areaId) {
          this.$ajax({
            method: 'get',
            url: '/web/supply/getAreaTree',
            data: {
              areaId: areaId,
            },
          }).then((res) => {
            if (res.data.code == 200) {
              if (flag == 2)
                this.formList.projectAreaList2 = [{ id: '', name: '全部' }, ...res.data.data];
              if (flag == 3) {
                this.formList.projectAreaList3 = [{ id: '', name: '全部' }, ...res.data.data];
              }
              if (flag == 4) this.formList.projectAreaList4 = res.data.data;
            }
          });
        } else {
          this.$ajax({
            method: 'get',
            url: '/web/supply/getAreaTree',
            data: {
              areaId: '',
            },
          }).then((res) => {
            if (res.data.code == 200) {
              this.formList.projectAreaList = [{ id: '', name: '全部' }, ...res.data.data[0].children];
            }
          });
        }
        resolve();
      });
    },

    // 查询 事件
    search() {
      this.orderField = '';
      this.orderSort = '';

      this.getData();
    },

    // 重置 事件
    reset() {
      // 搜索条件
      this.form.areaRange = ''
      this.form.tradeCategory = ''
      this.form.childTradeVariety = ''
      this.form.tradeVariety = ''
      this.form.projectArea = ''
      this.form.projectArea2 = ''
      this.form.projectArea3 = ''
      this.form.projectArea4 = ''
      this.form.status = ''
      this.form.codeOrName = ''
      this.form.level = ''

      // 排序
      this.orderField = '';
      this.orderSort = '';
      // 分页器
      this.pager.pageNo = 1;
      this.pager.pageSize = 16;
    },


    // 排序 处理函数
    sort(item, index) {
      // 1升序 2降序
      if (!item.isJT) {
        // 默认
        this.orderField = item.dataKey;
        this.orderSort = '';
      } else {
        // 默认以外的
        if (item.dataKey == this.orderField) {
          this.sortList[index].isFlag = !this.sortList[index].isFlag;
          this.orderSort = this.orderSort == 1 ? 2 : 1;
        } else {
          this.sortList[index].isFlag = true;
          this.orderSort = 1;
        }
        this.orderField = item.dataKey;
      }

      this.getData();
    },

    // 洽谈
    goNegotiation(val) {
      console.log(213456);
      this.$ajax({
        method: 'post',
        url: '/web/supply/talk',
        data: {
          id: val.id
        }
      }).then((res) => {
        if (res.data.code == 200) {
          this.dialogVisible = true
          this.infoObj = val
        }
      });

    },

    //  分页器函数
    handleCurrentChange(e) {
      console.log('当前页条数，变话', e);
      this.pager.pageNo = e;
      this.getData();
    },
  },
};
</script>

<style lang="scss" scoped>
.w {
  position: relative;
  width: 1200px;
  margin: 0 auto;
}

.project-info {
  width: 100%;
  background-color: #ffffff;
  padding-bottom: 20px;

  .tab-box {
    width: 100%;
    height: 50px;
    background-color: #f7f7f7;

    .tab-type {
      display: flex;

      span {
        width: 130px;
        line-height: 50px;
        font-weight: 400;
        color: #333333;
        font-size: 20px;
        text-align: center;
        cursor: pointer;
      }

      .active {
        color: #ffffff;
        font-weight: bold;
        background-color: #ed911f;
      }
    }
  }

  .search-box {
    padding-top: 27px;
    padding-bottom: 34px;

    // background-color: antiquewhite;
    .row {
      display: flex;
      min-height: 24px;
      font-size: 14px;

      // margin-bottom: 16px;
      // border-bottom: 1px solid #000;
      .row-left {
        width: 91px;
        color: #333333;
        font-weight: bold;
        line-height: 40px;
        // background-color: #0084ff;
        margin-right: 18px;
      }

      .row-right {
        width: calc(100% - 109px);
        line-height: 40px;
        // padding: 0 12px;
        padding-left: 12px;
        font-size: 18px;
        color: #333333;

        span {
          display: inline-block;
          padding: 0 15px;
          //   line-height: 23px;
          height: 23px;
          cursor: pointer;
          line-height: 23px;
        }

        span:hover {
          background: #ed911f;
          border-radius: 6px;
          color: white;
        }

        .active {
          background: #ed911f;
          border-radius: 6px;
          color: white;
        }
      }

      .flex {
        display: flex;
        color: #666666;
        font-size: 14px;

        .mar {
          display: inline-block;
          //   margin: 0 5px;
          width: 15px;
          text-align: center;
        }

        .mar2 {
          margin-left: 3px;
        }
      }

      .child {
        background-color: #f7f7f7;
        border-radius: 5px;
        font-size: 16px;
        // padding: 0 10px;
      }
    }

    .btn-box {
      // position: absolute;
      // right: 0;
      // bottom: -4px;
      text-align: center;
      margin-top: 10px;
    }
  }

  .sort-box {
    border-top: 1px solid #e5e5e5;
    border-bottom: 1px solid #e5e5e5;

    .sort {
      display: flex;
      align-items: center;
      height: 50px;
      font-size: 14px;
      color: #333333;

      .item1 {
        display: flex;
        align-items: center;
        height: 18px;
        margin-right: 30px;
        cursor: pointer;

        .iconBox {
          margin-left: 3px;
          height: 18px;
          // position: relative;
          display: flex;
          // align-items: center;
          // flex-direction: column;
          // height: 15px;
          // background-color: #e5e5e5;
          // // margin-left: 2px;
          // font-size: 14px;
          // font-weight: bold;
          // transform: translateY(-2px);

          // .top {
          //   position: absolute;
          //   left: 0;
          //   top: -5px;
          // }
          // .bottom {
          //   position: absolute;
          //   left: 0;
          //   bottom: -8px;
          // }
          padding-top: 4.2px;

          img {
            width: 4px;
            height: 11px;
          }
        }
      }

      .active {
        color: #ed911f;
        font-weight: 400;
      }
    }
  }

  .list-box {
    padding-top: 29px;

    .table-box {


      .item {
        border-radius: 5px;
        background: #fff;
        padding: 15px;
        margin-top: 15px;
        display: flex;

        .img {
          width: 285px;
          height: 200px;
          margin-right: 20px;
          position: relative;

          .title-tag1 {
            position: relative;
            z-index: 99;
            width: 70px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            border-radius: 0 15px 15px 0;
            background: #0B8DF1;
            font-family: Microsoft YaHei;
            font-size: 12px;
            font-weight: normal;
            color: #fff;
            position: absolute;
            left: 0;
            top: 10px;
          }

          .title-tag2 {
            position: relative;
            z-index: 99;
            width: 70px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            border-radius: 0 15px 15px 0;
            background: #EA9921;
            font-family: Microsoft YaHei;
            font-size: 12px;
            font-weight: normal;
            color: #fff;
            position: absolute;
            left: 0;
            top: 10px;
          }

          .title-tag3 {
            position: relative;
            z-index: 99;
            width: 70px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            border-radius: 0 15px 15px 0;
            background: #67C23A;
            font-family: Microsoft YaHei;
            font-size: 12px;
            font-weight: normal;
            color: #fff;
            position: absolute;
            left: 0;
            top: 10px;
          }

          .carousel {
            height: 200px;
            width: 100%;
          }
        }

        // 哈哈
        .content {
          flex: 1;

          .title {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .name {
              max-width: 550px;
              margin-right: 20px;
              color: #222222;
              font-family: Microsoft YaHei;
              font-size: 16px;
              font-weight: bold;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .right {
              width: calc(100% - 650px);
              display: flex;
              align-items: center;
              justify-content: right;

              .dw {
                display: flex;
                align-items: center;
                color: #909399;
                font-family: Microsoft YaHei;
                font-size: 14px;
                font-weight: normal;

                img {
                  width: 11px;
                  height: 14px;
                  margin-right: 8px;
                }

                .dw-name {
                  max-width: 100px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }

            }
          }

          .info {
            display: flex;
            align-items: center;
            margin-top: 14px;

            .info-item {
              display: flex;
              align-items: center;
              margin-right: 30px;
              color: #909399;
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 14px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;

              .tot {
                max-width: 80px;
                font-family: Microsoft YaHei;
                font-weight: 600;
                font-size: 16px;
                color: #444;
                margin-right: 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }

          .text {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            margin-top: 14px;
            line-height: 24px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;

            .text-div {
              display: block;
            }

            .text-title {
              color: #909399;
            }

            .text-content {
              color: #444;
            }
          }

          .btn {
            border-top: 2px solid #e7e7e7;
            margin-top: 14px;
            padding-top: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .qtcs {
              color: #0B8DF1;
              font-family: Microsoft YaHei;
              font-size: 13px;
              font-weight: normal;
            }
          }
        }
      }

    }


    .item:nth-child(4n) {
      margin-right: 0;
    }

    .noneBox {
      display: flex;
      flex-direction: column;
      // justify-content: center;
      align-items: center;
      width: 100%;

      .noneImageBox {
        width: 369px;
        height: 333px;
        // background-color: red;
        margin-top: 161px;
        margin-bottom: 40px;
      }

      .noneText {
        font-size: 24px;
        font-weight: 400;
        color: #6cb8ff;
        margin-bottom: 300px;
      }
    }
  }



  .dialog-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .name {
      font-family: Microsoft YaHei UI;
      font-size: 18px;
      font-weight: normal;
      color: #888888;
    }

    img {
      width: 120px;
      height: 128px;
      margin-top: 20px;
    }

    .contacts {
      font-family: Microsoft YaHei UI;
      font-size: 18px;
      font-weight: normal;
      color: #909399;
      display: flex;
      align-items: center;
      margin-top: 14px;
      padding-left: 40px;

      .wz {
        width: 100px;
        text-align: right;
      }

      .totls {
        color: #444444;
        width: 140px;
      }
    }
  }
}
</style>
