<template>
  <nd-dialog ref="dialog" title="PDF预览" center height="70vh" width="800px" append-to-body>
    <vue-office-pdf ref='canvasDOM' :options="options" :src="pdfSrc" @rendered="rendered" />
    <template #footer>
      <nd-button @click="exportPdf" v-show="displayExport">
        导出
      </nd-button>
      <nd-button @click="close">
        关闭
      </nd-button>
    </template>
  </nd-dialog>
</template>
<script>
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import vueOfficePdf from '@vue-office/pdf'
export default {
  props: {
    // 是否显示导出按钮
    displayExport: {
      type: Boolean,
      default: false,
    },
    // 文书id
    documentId: {
      type: String,
      default: "",
    },
  },
  components: {
    ndDialog,
    ndButton,
    vueOfficePdf
  },
  data() {
    return {
      pdfSrc: "",
      options: {
        width: 700,
      },
      pdfName:"",
    };
  },
  mounted() {

  },
  methods: {
    // 打开
    open(src) {
      this.$refs.dialog.open();
      this.$nextTick(() => {
        this.pdfSrc = src;
      });
    },
    // 渲染完成
    rendered() {
      console.log("渲染完成")
    },
    // 导出
    exportPdf() {
      let params = {
        docId: this.documentId,//文书id
      }
      this.$ajax({
        method: "GET",
        url: "" + "/resources/download",
        data: params,
        serverName: "nd-village",
        responseType: "blob",
      }).then(async (res) => {
        if (!res) return;
        await this.getPdfName()
        const blob = new Blob([res.data], {
          type: "application/pdf;chartset=UTF-8",
        }); // 构造一个blob对象来处理数据，并设置文件类型
        if (window.navigator.msSaveOrOpenBlob) {
          //兼容IE10
          navigator.msSaveBlob(blob, this.pdfName);
        } else {
          const URL = window.URL || window.webkitURL
          const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
          const a = document.createElement("a"); //创建a标签
          a.style.display = "none";
          a.href = href; // 指定下载链接
          a.setAttribute("download", this.pdfName);
          //a.download = this.filename; //指定下载文件名
          a.click(); //触发下载
          window.URL.revokeObjectURL(a.href); //释放URL对象
        }
      }).catch((err) => {
        console.log(err);
      });
    },
    // 获得PDF文件名
    getPdfName() {
      return new Promise((resolve) => {
        let params = {
          docId: this.documentId,//文书id
        }
        this.$ajax({
          method: "GET",
          url: "" + "/resources/downloadName",
          data: params,
          serverName: "nd-village",
        }).then((res) => {
          if (res.data.code === 200) {
            this.pdfName=res.data.data
          } else {
            this.pdfName="获取文书名称失败.pdf"
          }
          resolve(true)
        })
      })
    },
    // 关闭
    close() {
      this.$refs.dialog.close();
    }
  }
};
</script>
<style lang="scss" scoped>
// .pdf-view-box {
//   width: 500px;
// }
</style>