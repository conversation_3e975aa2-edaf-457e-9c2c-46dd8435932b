<template>
  <div class="nd-tree-box">
    <el-tree v-bind="$attrs" ref="ndTree" v-on="$listeners" @node-click="handleNodeClick">
    </el-tree>
  </div>
</template>

<script>
import debounce from "lodash.debounce";
export default {
  data() {
    return {
      clickCount: 0,
    };
  },
  methods: {
    // 单击变双击
    handleNodeClick() {
      const args = arguments;
      // 发送单击事件,用户自己会注册单击事件,不用这里暴露
      // this.$emit("node-click", ...args);
      // 发送双击事件
      this.clickCount++;
      const emitDblClick = debounce(() => {
        if (this.clickCount > 1) {
          this.$emit("node-dblclick", ...args);
        }
        this.clickCount = 0;
      }, 500);
      emitDblClick();
    },
  },
};
</script>

<style lang="scss" scoped>
.nd-tree-box {
  ::v-deep .el-tree-node__label {
    font-size: 12px;
  }

  ::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: #c8ecff;
  }

  ::v-deep .el-tree-node__content {
    &:hover {
      background-color: #e8f7ff;
    }
  }
}
</style>