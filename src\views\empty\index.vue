<template>
  <div class="empty-cont" :style="{ height: boxHeight + 'px' }">
    <div class="empty-img"><img src="@/assets/detail/empty.png" class="img1" alt="" /></div>
    <div class="empty-text">暂无数据</div>
  </div>
</template>
  
  <script>
export default {
  props: {
    boxHeight: {
      type: Number,
      default: 300,
    },
  },
  data() {
    return {};
  },
};
</script>
  
<style lang="scss" scoped>
.empty-cont {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .empty-img {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 14px;
  }

  .empty-text {
    font-family: Microsoft YaHei;
    font-weight: 400;
    font-size: 24px;
    color: #999999;
    position: relative;
    left: -23px;
  }
}
</style>