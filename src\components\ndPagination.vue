<template>
  <div class="nd-pagination-box">
    <el-pagination ref="pagination" :total="total" :current-page="currentPage" :total-page="totalPage" :page-size="pageSize" :page-sizes="pageSizes" layout="slot, prev, pager, next, jumper" v-bind="$attrs" v-on="$listeners">
      <span class="total">共<span class="total-count">{{ total }}</span>条</span>
      <!-- <span class="current">{{ currentPage }}/{{ totalPage }}</span> -->
      <!-- <span class="go">GO</span> -->
    </el-pagination>
  </div>
</template>

<script>
export default {
  props: {
    total: {
      type: Number,
      default: 0,
    },
    currentPage: {
      type: Number,
      default: 0,
    },
    totalPage: {
      type: Number,
      default: 0,
    },
    pageSize: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      pageSizes: [10, 30, 50, 100, 300],
    }
  },
  mounted() {
    let pageText = document.getElementsByClassName('el-pagination__jump')[0]
    if (pageText) {
      pageText.childNodes[0].nodeValue = '去到'
    }
  },
  methods: {

  },
};
</script>

<style lang="scss" scoped>
.nd-pagination-box {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;

  ::v-deep .el-pagination {
    padding: 10px 5px;
  }

  ::v-deep .el-pagination span:not([class*='suffix']) {
    min-width: 0px;
  }

  .total {
    font-size: 13px;
    font-weight: normal;
    margin-right: 10px;
    height: 28px;
    line-height: 28px;

    .total-count {
      padding-left: 5px;
      padding-right: 5px;
    }
  }

  .current {
    font-size: 13px;
    font-weight: normal;
    height: 28px;
    line-height: 28px;
    margin-right: 5px;
  }
}
::v-deep .el-pager li {
  font-size: 14px !important;
  padding: 0 12px;
  border: 1px solid #e5e5e5;
  font-weight: 400;
  border-radius: 4px;
  margin: 0 5px;
}
::v-deep .el-pager li.active {
  background-color: #10AEC2;
  color: #ffffff;
}
::v-deep .el-pager li.active:hover {
  background-color: #10AEC2;
  color: #ffffff;
}
::v-deep .el-pager li:hover {
  color: #10AEC2;
}
::v-deep .el-input__inner:focus {
    border-color: #10AEC2;
    outline: 0;
}
.go {
  height: 28px;
}
</style>