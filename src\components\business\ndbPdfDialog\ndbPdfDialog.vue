<template>
  <div class="nd-pdf-dialog-box">
    <ndb-pdf-dialog-chrome ref="pdfChrome" :displayExport="displayExport" :documentId="documentId"/>
    <ndb-pdf-dialog-legacy ref="pdfLegacy" :displayExport="displayExport" :documentId="documentId"/>
  </div>
</template>
<script>
import ndbPdfDialogChrome from "./ndbPdfDialogChrome.vue";
import ndbPdfDialogLegacy from "./ndbPdfDialogLegacy.vue";
export default {
  props: {
    displayExport:{//是否显示导出按钮
      type:Boolean,
      default:false,
    },
    documentId:{//文书id
      type:String,
      default:"",
    },
  },
  components: {
    ndbPdfDialogChrome,
    ndbPdfDialogLegacy
  },
  data() {
    return {

    };
  },
  mounted() {
  },
  methods: {
    // 打开
    open(src) {
      var userAgent = navigator.userAgent;
      if (userAgent.indexOf('Safari') > -1) {
        this.$refs.pdfChrome.open(src);
      } else {
        this.$refs.pdfLegacy.open(src);
      }
    },
  }
};
</script>
<style lang="scss" scoped>
.nd-pdf-dialog-box {
  width: auto;
  height: auto;
}
</style>