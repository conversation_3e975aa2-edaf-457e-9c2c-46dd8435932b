<template>
  <div>
    <div class="footer-whc">
      <div class="footer-top-wh">
        <div class="top-item-wh">
          <div class="item-select-wh">
            <div class="select-text-wh">{{ selectValue.value1 }}</div>
            <div><i class="el-icon-arrow-down" style="color: #333"></i></div>
          </div>
          <div class="item-show-wh">
            <div class="show-cont-wh">
              <div class="list-cont-wh" v-for="(item, index) in nationList" :key="index">
                <div class="list-item-wh" :class="[{ active: item.name === selectValue.value1 }]"
                  @click="handleChange(item, 'value1')">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="top-item-wh">
          <div class="item-select-wh">
            <div class="select-text-wh">{{ selectValue.value2 }}</div>
            <div><i class="el-icon-arrow-down" style="color: #333"></i></div>
          </div>
          <div class="item-show-wh">
            <div class="show-cont-wh">
              <div class="list-cont-wh" v-for="(item, index) in gzList" :key="index">
                <div class="list-item-wh" :class="[{ active: item.name === selectValue.value2 }]"
                  @click="handleChange(item, 'value2')">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="top-item-wh">
          <div class="item-select-wh">
            <div class="select-text-wh">{{ selectValue.value3 }}</div>
            <div><i class="el-icon-arrow-down" style="color: #333"></i></div>
          </div>
          <div class="item-show-wh">
            <div class="show-cont-wh">
              <div class="list-cont-wh" v-for="(item, index) in propertyList" :key="index">
                <div class="list-item-wh" :class="[{ active: item.name === selectValue.value3 }]"
                  @click="handleChange(item, 'value3')">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer-center-wh">
        <!-- part1 -->
        <div class="right-text-wh">
          <div>
            <span @click="goTab(1)">网站首页</span> <span class="line">|</span>
            <span @click="goTab(2)">新闻中心</span> <span class="line">|</span>
            <span @click="goTab(3)">公示公告</span>
            <!-- <span class="line">|</span>&nbsp; -->
          </div>
          <div>
            <span @click="goTab(4)">业务规则</span> <span class="line">|</span>
            <span @click="goTab(5)">政策法规</span> <span class="line">|</span>
            <span @click="goTab(6)">资料下载</span>
            <!-- <span class="line">|</span>&nbsp; -->
          </div>
          <div>
            <span @click="goTab(7)"> 关于我们</span> <span class="line">|</span>
            <span @click="goTab(8)">互动交流</span>
          </div>
        </div>
        <!-- part2 -->
        <div class="left-text-wh">
          <div>建设单位：{{ commonData.project }}</div>
          <div style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 586px;
          ">
            <div>地址：{{ commonData.address }}</div>

          </div>
          <div>办公时间：{{ commonData.workTime }}</div>
          <!-- <div>服务窗口电话：{{ commonData.contactUs }}</div> -->
        </div>
        <!-- partnew -->
        <div class="QrCode-div3">
          <div class="cont">
            <div class="textb">黄陂区-江岸区-江汉区</div>
            <div class="textc"> 18627002264</div>
          </div>
          <div class="cont">
            <div class="textb">武汉经开区-汉阳区</div>
            <div class="textc"> 17611236351</div>
          </div>
          <div class="cont">
            <div class="textb">江夏区-武昌区-洪山区</div>
            <div class="textc"> 13554084747</div>
          </div>

        </div>
        <div class="QrCode-div3">
          <div class="cont">
            <div class="textb">新洲区-东湖高新区</div>
            <div class="textc"> 17762538140</div>
          </div>

          <div class="cont">
            <div class="textb">长江新区-青山区-东湖风景区</div>
            <div class="textc"> 13627249510</div>
          </div>
          <div class="cont">
            <div class="textb">蔡甸区&nbsp;硚口区</div>
            <div class="textc">13407153162</div>
          </div>
        </div>
        <!-- part3 -->
        <!-- <div class="left-text-wh left-text-wh-div">
        <div>备案号：{{ commonData.recordNumber }}</div>
        <div class="bot-link-wh-div"><img src="@/img/jh.png" alt="">{{ commonData.psbNumber }}</div>
      </div> -->
      </div>
      <!-- <div class="line-gap-wh"></div>
    <div class="footer-bottom-wh">
      <div class="bottom-item-wh">{{ commonData.info }}</div>
    </div> -->
    </div>
    <div class="recordFiling">
      <div class="left-text-wh left-text-wh-div">
        <div>备案号：{{ commonData.recordNumber }}</div>
        <div class="bot-link-wh-div"><img src="@/img/jh.png" alt="">{{ commonData.psbNumber }}</div>
      </div>
    </div>
  </div>

</template>

<script>
export default {
  data() {
    return {
      commonData: {}, //公共配置数据
      selectValue: {
        value1: '国家级网站',
        value2: '湖北省内网站',
        value3: '产权交易中心网站',
      },

      nationList: [
        { name: '国家级网站', value: '' },
        { name: '中央人民政府', value: 'https://www.gov.cn/' },
        { name: '农业农村部', value: 'http://www.moa.gov.cn/' },
        { name: '自然资源部', value: 'https://www.mnr.gov.cn/' },
      ],
      gzList: [
        { name: '湖北省内网站', value: '' },
        { name: '湖北省人民政府', value: 'https://www.hubei.gov.cn/' },
        { name: '湖北省农业农村厅', value: 'http://nyt.hubei.gov.cn/' },
        { name: '武汉市财政局', value: 'https://czj.wuhan.gov.cn/' },
        { name: '武汉市国资委', value: 'https://gzw.wuhan.gov.cn/index/' },
        {
          name: '武汉市司法局',
          value: 'https://sfj.wuhan.gov.cn/',
        },
        { name: '武汉市审计局', value: 'https://sjj.wuhan.gov.cn/' },
        { name: '武汉市水务局', value: 'https://swj.wuhan.gov.cn/' },
        { name: '武汉市农业农村局', value: 'https://nyncj.wuhan.gov.cn/' },
        { name: '武汉市科技创新局', value: 'https://kjj.wuhan.gov.cn/' },
        { name: '武汉市金融工作局', value: 'https://jrj.wuhan.gov.cn/index/' },
        { name: '武汉市园林和林业局', value: 'https://ylj.wuhan.gov.cn/' },
        { name: '武汉市市场监督管理局', value: 'https://scjgj.wuhan.gov.cn/' },
        { name: '武汉市自然资源和规划局', value: 'https://zrzyhgh.wuhan.gov.cn/' },
        { name: '武汉市发展和改革委员会', value: 'https://fgw.wuhan.gov.cn/' },
        { name: '武汉市人力资源和社会保障局', value: 'https://rsj.wuhan.gov.cn/' },
        { name: '武汉市住房保障和房屋管理局', value: 'https://fgj.wuhan.gov.cn/' },
      ],
      propertyList: [
        { name: '产权交易中心网站', value: '' },
        { name: '湖北省农村资产交易平台', value: 'http://nyt.hubei.gov.cn/nczcjy/' },
        { name: '江苏省农村产权交易平台', value: 'http://www.jsnc.gov.cn/' },
        { name: '广西农村集体资产线上交易平台', value: 'http://116.1.238.202:8581/#/' },
        { name: '广州农村产权交易所', value: 'http://14.22.81.56:9080/#/' },
        {
          name: '温州农村产权服务中心',
          value: 'https://www.wznccq.com/app-project-index?page=2092#/',
        },
        { name: '沈阳农村综合产权交易网', value: 'https://www.ln-synccq.org.cn/#/home/<USER>' },
        { name: '成都农村产权交易所', value: 'https://zc.cdaee.com:18080/' },
        {
          name: '北京农村产权交易所',
          value: 'https://www.bjraee.com/index/',
        },
        { name: '重庆产权交易网', value: 'https://www.cquae.com/' },
      ],
    };
  },
  mounted() {
    // console.log(commonData,"配置数据");
    this.commonData = commonData;
    console.log(this.commonData, '配置数据');
  },
  // 销毁定时器

  methods: {
    // 无缝滚动
    infinitescRol(flage) {
      this.$nextTick(() => {
        let that = this;
        if (flage) {
          let width = document.getElementsByClassName('sponsor-item-box')[0].offsetWidth;
          let a = 0;
          function rollStart() {
            clearInterval(that.roll);
            that.roll = setInterval(() => {
              document.getElementsByClassName('sponsor-item-box')[0].style.left = a + 'px';
              document.getElementsByClassName('sponsor-item-box')[1].style.left = a + width + 'px';
              a--;
              if (a <= -width) {
                a = 0;
              }
            }, 10);
          }
          rollStart();
          document.getElementById('box').onmouseenter = function () {
            clearInterval(that.roll);
          };

          document.getElementById('box').onmouseout = function () {
            rollStart();
          };

          document.querySelectorAll('.sponsor-item img').forEach((item) => {
            item.onmouseenter = function () {
              clearInterval(that.roll);
            };

            item.onmouseout = function () {
              rollStart();
            };
          });
        } else {
          clearInterval(that.roll);
        }
      });
    },

    // 友情链接跳转
    handleChange(item, type) {
      this.selectValue[type] = item.name;
      if (item.value) window.open(item.value);
    },

    goTab(type) {
      if (type === 1) this.$router.push('/');
      else if (type === 2) this.$router.push('newsCenter');
      else if (type === 3) this.$router.push('publicInformationView');
      else if (type === 4) this.$router.push('businessRule');
      else if (type === 5) this.$router.push('lawsRegulation');
      else if (type === 6) this.$router.push('profileDownload');
      else if (type === 7) this.$router.push('aboutUs');
      else if (type === 8) this.$router.push('interacteCommunication');
    },
  },
};
</script>

<style lang="scss" scoped>
.footer-whc {
  width: 100%;
  background: #8dd1ff;
  // padding: 30px 0px 15px 0px;
  padding: 30px 0px 0px 0px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .footer-top-wh {
    // padding: 0px 360px 0px 360px;
    width: 1300px;
    display: flex;
    flex-direction: row;
    align-items: center;
    // justify-content: space-between;
    // column-gap: 21px;
    margin-bottom: 50px;
    background: #8dd1ff;

    .top-item-wh {
      flex: 1;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background: #fff;
      position: relative;
      margin-right: 20px;
      cursor: pointer;

      &:nth-child(3) {
        margin-right: 0;
      }

      .item-select-wh {
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: space-between;
        cursor: pointer;

        .select-text-wh {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 16px;
          color: #333;
          line-height: 32px;
          margin-right: 28px;
          white-space: nowrap;
        }
      }

      .item-show-wh {
        display: none;
        position: absolute;
        left: 0px;
        top: 41px;
        background: #8dd1ff;
        width: 100%;
        height: 170px;

        // overflow: auto;
        .show-cont-wh {
          background: #fff;
          width: 100%;
          height: 160px;
          overflow: auto;
          position: absolute;
          bottom: 0px;

          .list-cont-wh {
            .list-item-wh {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 40px;
              line-height: 40px;
            }

            .active {
              color: #fff;
              background: #0594f3;
              // color: #8DD1FF;
            }
          }
        }
      }

      &:hover .item-show-wh {
        display: block;
      }
    }
  }

  .footer-center-wh {
    // padding: 0px 360px 0px 360px;
    width: 1300px;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 30px;

    .left-text-wh {
      padding-left: 20px;
      width: 386px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 34px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;

      // gap: 76px;
      div {
        width: 100%;
      }
    }

    .right-text-wh {
      width: 250px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 34px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;

      div {
        width: 100%;
      }

      span {
        cursor: pointer;
      }

      .line {
        padding: 5px;
      }
    }

    .QrCode-div3 {
      // width: 660px;
      width: 330px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 34px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;

      .cont {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        align-content: center;
        // flex-direction: column;
        flex-wrap: nowrap;

        .textb {
          width: 200px;
          text-align: left;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;

          // margin-bottom: 10px;

        }

        .textc {
          text-align: left;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          // color: #ed911f;
          white-space: nowrap;
          // text-indent: 1em;
        }
      }
    }

    .left-text-wh-div {
      width: 586px;

      // justify-content: center;
      div {
        // margin: 0 90px;
      }

      .bot-link-wh-div {
        display: flex;
        align-items: center;
        padding-right: 4px;

        img {
          height: 15px;
          margin-right: 5px;
        }
      }
    }

  }

  .line-gap-wh {
    height: 1px;
    width: 100%;
    background: #fff;
    margin-bottom: 19px;
  }

  .footer-bottom-wh {
    width: 1300px;
    display: flex;
    flex-direction: column;

    .bottom-item-wh {
      display: flex;
      align-items: center;
      justify-content: center;

      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 24px;
    }

    .bot-link-wh {
      cursor: pointer;
    }
  }
}

.recordFiling {
  width: 100%;
  background: #8dd1ff;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-top: 1px solid #fff;
  padding: 10px;

  .left-text-wh {
    font-family: Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 34px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    text-align: center;


    div {

      width: 100%;
      text-align: center;
    }
  }
}
</style>
