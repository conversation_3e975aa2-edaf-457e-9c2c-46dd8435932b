<template>
  <div class="tab">
    <div
      :class="tabActive == index ? 'tab-item active' : 'tab-item'"
      v-for="(item, index) in tabList"
      :key="index"
      @click="tabClick(index)"
    >
      {{ item }}
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tabList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    value: {
      default: 0,
    },
  },
  data() {
    return {
      tabActive: 0,
    };
  },
  mounted() {
    this.tabActive = this.value;
    this.$emit('input', this.tabActive);
    // this.$emit('tabClick');
  },
  methods: {
    tabClick(e) {
      this.tabActive = e;
      this.$emit('input', this.tabActive);
      this.$emit('tabClick');
    },
  },
};
</script>

<style lang="scss" scoped>
.tab {
  display: flex;

  .tab-item {
    padding: 0 26px;
    font-size: 20px;
    height: 50px;
    display: flex;
    align-items: center;
    cursor: pointer;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #666666;
    background-image: url('@/assets/index/active.png');
    background-repeat: no-repeat;
    background-size: 0% 100%;
    user-select: none;
    // transition: 0.3s;
  }

  .active {
    font-size: 20px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #ffffff;
    background-size: 100% 100%;
    transition: 0.3s;
  }
}
</style>