<!-- 鉴证公告 -->
<template>
    <div class="wrap-container">
        <!-- 注销类 -->
        <listTypeC v-if="currentTab == '1'" requestUrl="/jzgg/zxl/" :breadcrumbName="'鉴证公告'" :showTab="2"></listTypeC>
        <!-- 注销类 -->
        <!-- 变更类 -->
        <listTypeC v-if="currentTab == '2'" requestUrl="/jzgg/bgl/" :breadcrumbName="'鉴证公告'" :showTab="2"></listTypeC>
        <!-- 变更类 -->
        <!-- 遗失类 -->
        <listTypeC v-if="currentTab == '3'" requestUrl="/jzgg/ysl/" :breadcrumbName="'鉴证公告'" :showTab="2"></listTypeC>
        <!-- 遗失类 -->
    </div>
</template>
<script>
import listTypeC from '@/views/newsListView/components/listTypeC.vue';
export default {
    props: {
        currentTab: {
            type: String,
            default: '1',
        },
    },
    components: {
        listTypeC,
    },
    data() {
        return {
        };
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
.wrap-container {
    min-width: 1000px;
    width: 100%;
    overflow: auto;
    height: 100%;
    background: #fff;
    position: relative;
    padding-bottom: 20px;
}
</style>