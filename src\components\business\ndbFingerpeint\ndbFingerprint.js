import { Wellcom } from "./wellcomFinger";
/**
 * 指纹仪的方法对象
 */
let fingerObj = {};

/**
 * 浏览器是否支持 websocket
 * 支持：true
 * 不支持：false
 */
function isSupport() {
    // 判断是否支持 WebSocket
    if ("WebSocket" in window) {
        fingerObj = Wellcom;
    } else if (!!window.ActiveXObject || "ActiveXObject" in window) {
        // finger = FingerIE;
    } else {
        // 不支持
        alert("您的浏览器不支持 WebSocket!");
        this.$message({
            message: "您的浏览器不支持 WebSocket!",
            type: 'error'
        })
        return false;
    }
    return true;
}

/*
 * 判断是否连接设备
 * 连接：true
 * 未连接：false
 */
async function isConnect() {
    // 支持
    if (isSupport()) {
        fingerObj.get_device((data) => {
            // 是否连接
            if (data["iRet"] >= 0) {
                Wellcom.setPortAttr(data["iRet"]);
                return true;
            } else {
                return false;
            }
        })
    }
    return false;
}

/**
 * 获取指纹模板数据(回调函数)
 * 返回指纹的数据
 * @code：0 成功，1失败，-1设备异常
 * @data：指纹数据，失败为空字符串
 */
function fingerprintEntry(callback) {
    if (isSupport()) {
        fingerObj.get_device((data) => {
            // 是否连接
            if (data["iRet"] >= 0) {
                Wellcom.setPortAttr(data["iRet"]);
                setTimeout(() => {
                    fingerObj.get_template(function (data) {
                        if (data["iRet"] == 0) {
                            //成功
                            callback({
                                code: 0,
                                data: data.msg,
                                message: "获取成功！"
                            });
                        } else {
                            //失败
                            callback({
                                code: 1,
                                data: "",
                                message: "获取失败请重新获取！"
                            });
                        }
                    });
                }, 500);
            } else {
                callback({
                    code: -1,
                    data: "",
                    message: data.msg
                });
            }
        })
    }
}

/**
 * 获取指纹特征(回调函数)
 * 返回指纹数据
 * @code：0 成功，1失败，-1设备异常
 * @data：指纹数据，失败为空字符串
 * @ 回调的值 {
 * code:0,
 * data:"",
 * message:""
 * }
 */
function fingerprintCharacteristic(callback) {
    if (isSupport()) {
        fingerObj.get_device((data) => {
            // 是否连接
            if (data["iRet"] >= 0) {
                Wellcom.setPortAttr(data["iRet"]);
                setTimeout(() => {
                    fingerObj.get_feature(function (data) {
                        if (data["iRet"] == 0) {
                            //成功
                            callback({
                                code: 0,
                                data: data.msg,
                                message: "获取成功！"
                            });
                        } else {
                            //失败
                            callback({
                                code: 1,
                                data: "",
                                message: "获取失败请重新获取！"
                            });
                        }
                    });
                }, 500);
            } else {
                callback({
                    code: -1,
                    data: "",
                    message: data.msg
                });
            }
        })
    }
}

/**
 * 对比指纹( 回调函数)
 * @mb：录入的指纹模板
 * @tz：录入的指纹特征
 * @code：0成功，1失败，-1未连接
 */
function fingerprintContrast(mb, tz, callback) {
    if (isSupport()) {
        fingerObj.get_device((data) => {
            // 是否连接
            if (data["iRet"] >= 0) {
                Wellcom.setPortAttr(data["iRet"]);
                setTimeout(() => {
                    fingerObj.match(mb,tz,function (data) {
                        if (data["iRet"] == 0) {
                            //成功
                            callback({
                                code: 0,
                                data: data.msg,
                                message: "对比成功"
                            });
                        } else {
                            //失败
                            callback({
                                code: 1,
                                data: "",
                                message: "对比失败"
                            });
                        }
                    });
                }, 500);
            } else {
                callback({
                    code: -1,
                    data: "",
                    message: data.msg
                });
            }
        })
    }
}

export {
    isSupport,
    isConnect,
    fingerprintEntry,
    fingerprintCharacteristic,
    fingerprintContrast,
};