<template>
  <div>
    <el-dialog ref="ndbDataprocessingRef" :visible.sync="dialogVisible" :show-close="false" width="200px" top="40vh" :close-on-click-modal="false" :vclose-on-press-escape="false" center>
      <div class="data-processing-box">
        <img src="@/assets/Dataprocessing.png" alt="">
        <div>数据处理中...</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ndDialog from '@/components/ndDialog.vue';
export default {
    components: {
        ndDialog,
    },

    data() {
        return {
            dialogVisible:false
        };
    },

    mounted() {
        
    },

    methods: {
        //打开弹窗
        open() {
            this.dialogVisible = true;
        },
        //关闭弹框
        close() {
            this.dialogVisible = false;
        },
    },
};//
</script>

<style lang="scss" scoped>
    .data-processing-box{
        padding: 18px 0;
        background-color: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img{
            width: 30px;
            height: 30px;
            margin-bottom: 10px;
        }
        div{
            color: #555;
            font-size: 14px;
        }
    }

    ::v-deep .el-dialog__header{
        padding: 0;
    }

    ::v-deep .el-dialog{
        box-shadow: 0px 0px 20px 0px rgb(0 0 0 / 20%);
        border-radius: 6px;
    }
</style>