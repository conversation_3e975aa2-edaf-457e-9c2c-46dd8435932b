<template>
  <div class="details-box">

    <div class="breadcrumbs-box">
      <breadcrumbs :titles="['抵押公告', '项目详情']" />
    </div>

    <div class="content">
      <div class="header">{{ title }}</div>
      <div class="line"></div>
      <!-- <div v-html="content"></div> -->
      <div class="ql-snow">
          <div class="notes ql-editor" v-html="content"></div>
        </div>
    </div>

  </div>
</template>

<script>
import breadcrumbs from './breadcrumbs.vue'
export default {
  components: {
    breadcrumbs
  },
  data() {
    return {
      title: '',
      content: '',
    };
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      let data = {
        id: this.$route.query.id,
      };
      this.$ajax({
        // url: 'http://**************:8080/manage/notice/mortgageDetailById',
        url: '/notice/mortgageDetailById',
        method: 'get',
        // needServerPath:false,
        data,
      }).then((res) => {
        if (res.data.code === 200) {
          this.content = res.data.data.announcementContent
          this.title = res.data.data.announcementTitle
        }
      });
    },
  },
};
</script>
<style lang="scss">
@import '@/assets/css/vue-quill.snow.css';
</style>
<style lang="scss" scoped>
.breadcrumbs-box {
  padding-left: calc((100% - 1200px) / 2);
  background-color: #f8f8f8;
}

.details-box {
  width: 100%;
  border-top: 1px solid #CBCBCB;

  .content {
    width: 1200px;
    margin: 0 auto;
    border: 1px solid #e8e8e8;
    background: #fff;
    min-height: 300px;
    margin-top: 20px;
    padding: 20px;

    .header {
      text-align: center;
      font-size: 24px;
      font-weight: bold;
      color: #151515;
    }

    .line {
      width: 100%;
      height: 2px;
      background: #ddd;
      margin: 16px 0;
    }

  }
}
</style>