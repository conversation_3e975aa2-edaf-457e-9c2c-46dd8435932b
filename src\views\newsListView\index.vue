<template>
  <div class="wrap-container">
    <div style="position: sticky; top: 0; z-index: 9">
      <tabs v-model="currentTab" :tab-names="tabNames" />
    </div>
    <!-- 新闻动态 -->
    <list v-show="currentTab == 0" requestUrl="/xwzx/xwdt/"></list>
    <!-- 地方要闻 -->
    <list v-show="currentTab == 1" requestUrl="/xwzx/dfyw/"></list>
    <!-- 专家观点 -->
    <list v-show="currentTab == 2" requestUrl="/xwzx/zjgd/"></list>
  </div>
</template>
<script>
import tabs from './components/tabs.vue';
import list from './components/list.vue';
export default {
  components: {
    tabs,
    list,
  },
  data() {
    return {
      currentTab: 0,
      tabNames: [{ name: '新闻动态' }, { name: '地方要闻' }, { name: '专家观点' }],
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.wrap-container {
  width: 100%;
  overflow: auto;
  height: 100%;
  background: #fff;
  position: relative;
  padding-bottom: 20px;
}
</style>
