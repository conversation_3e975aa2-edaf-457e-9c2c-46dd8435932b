<template>
  <div class="main-box" ref="mainBoxRef">
    <!-- 头部 -->
    <div class="top-cont">
      <div class="topc-top">
        <div class="topct-left">
          <div class="topct-text" @click="toHome">
            <img src="@/assets/header/whHeaderImg1.png" alt="" srcset="" />
          </div>
          <!-- <el-select
            v-if="pageData.activeTab === 0"
            style="width: 101px; height: 32px"
            ref="ndSelect"
            v-model="nodeName"
            placeholder=""
          >
            <template slot="prefix">
              <i style="width: 10px; height: 14px" class="el-icon-location"></i>
            </template>
<el-option :value="nodeName">
  <el-tree id="tree-option" ref="tree" highlight-current lazy :props="defaultProps" node-key="id"
    :current-node-key="currentNodekey" @node-click="handleNodeClick" :expand-on-click-node="false" :load="getAreaTree">
  </el-tree>
</el-option>
</el-select> -->
        </div>
        <div class="topct-right">
          <div class="topct-inner">
            <div class="innner-search" v-if="pageData.activeTab !== 2">
              <el-input v-model="searchKey" style="width: 300px" placeholder="请输入关键字查询" class="custom-placeholder"
                clearable>
                <el-button @click="handleSearch" slot="append" icon="el-icon-search"></el-button>
                <!-- <template slot="append"> <i  class="el-icon-search"></i></template> -->
              </el-input>
            </div>
          </div>
        </div>
      </div>
      <div class="topc-bot" v-if="hasTab">
        <div class="topcb-right">
          <div class="tab-cont">
            <div class="tab-item-box" v-for="item in tabList" :key="item.order">
              <div :class="[pageData.activeTab === item.order ? 'tab-active' : 'tab-item']" @click="tabActive(item)">
                {{ item.label }}
              </div>
              <div v-if="pageData.activeTab === item.order" class="active-line"></div>
              <div class="active-line active-line-shadow"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 大背景图 -->
    <div class="big-bg" v-if="hasTab">
      <div class="carousel-box" v-if="pageData.activeTab === 0 && pageData.isMain == '0'">
        <el-carousel height="400px" :interval="3000" arrow="never" @change="carouselChange">
          <el-carousel-item v-for="(item, index) in imgList" :key="index">
            <img class="carousel-img" :src="item.url" alt="" />
          </el-carousel-item>
        </el-carousel>
        <!-- 绝对定位文字 -->
        <div class="word-img">
          <img :src="imgList[wordImgIndex].wordUrl" alt="" />
        </div>
      </div>
      <div class="top-bg" v-if="pageData.activeTab === 0 && pageData.isMain == '1'">
        <img class="top-imgbg" src="@/assets/detail/zbfwBg.png" alt="" srcset="" />
        <img class="top-imgword" src="@/assets/detail/zsfwWord.png" alt="" srcset="" />
      </div>
      <div class="top-bg" v-if="pageData.activeTab === 0 && pageData.isMain == '2'">
        <img class="top-imgbg" src="@/assets/detail/gxdtBg.png" alt="" srcset="" />
        <img class="top-imgword" src="@/assets/detail/jrcp-back.png" alt="" srcset="" />
      </div>
      <div class="top-bg" v-if="pageData.activeTab === 1">
        <img class="top-imgbg" src="@/assets/detail/xwzxBg.png" alt="" srcset="" />
        <img class="top-imgword" src="@/assets/detail/xwzxWord.png" alt="" srcset="" />
      </div>
      <div class="top-bg" v-if="pageData.activeTab === 2">
        <img class="top-imgbg" src="@/assets/detail/gsggBg.png" alt="" srcset="" />
        <img class="top-imgword" src="@/assets/detail/gsggWord.png" alt="" srcset="" />
      </div>
      <div class="top-bg" v-if="pageData.activeTab === 3">
        <img class="top-imgbg" src="@/assets/detail/ywgzBg.png" alt="" srcset="" />
        <img class="top-imgword" src="@/assets/detail/ywgzWord.png" alt="" srcset="" />
      </div>
      <div class="top-bg" v-if="pageData.activeTab === 4">
        <img class="top-imgbg" src="@/assets/detail/zcfgBg.png" alt="" srcset="" />
        <img class="top-imgword" src="@/assets/detail/zcfgWord.png" alt="" srcset="" />
      </div>
      <div class="top-bg" v-if="pageData.activeTab === 5">
        <img class="top-imgbg" src="@/assets/detail/zlxzBg.png" alt="" srcset="" />
        <img class="top-imgword" src="@/assets/detail/zlxzWord.png" alt="" srcset="" />
      </div>
      <div class="top-bg" v-if="pageData.activeTab === 6">
        <img class="top-imgbg" src="@/assets/detail/gywmBg.png" alt="" srcset="" />
        <img class="top-imgword" src="@/assets/detail/gywmWord.png" alt="" srcset="" />
      </div>
      <div class="top-bg" v-if="pageData.activeTab === 7">
        <img class="top-imgbg" src="@/assets/detail/hdjlBg.png" alt="" srcset="" />
        <img class="top-imgword" src="@/assets/detail/hdjlWord.png" alt="" srcset="" />
      </div>
      <div class="top-bg" v-if="pageData.activeTab === 8">
        <img class="top-imgbg" src="@/assets/detail/gxdtBj.png" alt="" srcset="" />
        <!-- <img class="top-imgword" src="@/assets/detail/hdjlWord.png" alt="" srcset="" /> -->
      </div>
    </div>

    <!-- 快捷链接 -->
    <div class="goBox" v-if="pageData.activeTab === 0 && pageData.isMain == '0' && hasTab">
      <div class="item-box" v-for="(item, index) in linkList" :key="index">
        <div class="item" :style="{
          'background-image': 'url(' + item.url + ')',
          'background-repeat': 'no-repeat',
          'background-size': '100% 100%',
        }" @click="goIpConfig(item, index)">
          <span class="link-text">{{ item.title }}</span>
        </div>
      </div>

      <!-- 广告展示 -->
      <div class="advertise-box" ref="advertiseRef" v-if="showAdverTise && pageData.activeTab === 0">
        <!-- 关闭 -->
        <div class="close-img" @click="closeAdverTise">
          <img src="@/assets/header/close.png" alt="" />
        </div>
        <!-- 图片 -->
        <div>
          <img class="img-box" @click="jumpLink" :src="imgUrl" alt="" />
        </div>
        <!-- @/assets/header/advertise.png -->
        <!-- imgUrl -->
      </div>
    </div>
    <!-- app展示 -->
    <div class="right-app" v-if="pageData.activeTab === 0 && pageData.isMain == '0' && hasTab">
      <div class="common-div">
        <div class="common-item1">
          <img src="@/assets/header/gzhicon.png" class="img1" alt="" />
          <div class="text1">公众号</div>
          <div class="QrCode-div1">
            <div class="qrcode">
              <img src="@/assets/detail/gzh.png" alt="" />
            </div>
            <div class="texta">公众号</div>
          </div>
        </div>
        <div class="common-item1">
          <img src="@/assets/header/whxcx.png" class="img1" alt="" />
          <div class="text1">小程序</div>
          <div class="QrCode-div1">
            <div class="qrcode">
              <img src="@/assets/detail/code_new.jpg" alt="" />
            </div>
            <div class="texta">小程序</div>
          </div>
        </div>
        <div class="common-item2">
          <img src="@/assets/header/whzxdh.png" class="img2" alt="" />
          <div class="text2">咨询电话</div>
          <div class="QrCode-div2">
            <!-- <div class="cont">
              <div class="textb">咨询电话</div>
              <div class="textc">027—65770376</div>
            </div> -->
            <div class="cont">
              <div class="textb">黄陂区、江岸区、江汉区</div>
              <div class="textc"> 18627002264</div>
            </div>
            <div class="cont">
              <div class="textb">江夏区、武昌区、洪山区</div>
              <div class="textc"> 13554084747</div>
            </div>
            <div class="cont">
              <div class="textb">新洲区、东湖高新区</div>
              <div class="textc"> 17762538140</div>
            </div>
            <div class="cont">
              <div class="textb">武汉经开区、汉阳区</div>
              <div class="textc"> 17611236351</div>
            </div>
            <div class="cont">
              <div class="textb">长江新区、青山区、东湖风景区</div>
              <div class="textc"> 13627249510</div>
            </div>
            <div class="cont">
              <div class="textb">蔡甸区、硚口区</div>
              <div class="textc">13407153162</div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="active-div">
        <img src="@/assets/header/appActive.png" class="img1" alt="" />
        <div class="text1">APP下载</div>
        <img src="@/assets/header/phoneActive.png" class="img2" alt="" />
        <div class="text1">咨询电话</div>
      </div> -->
    </div>

    <div class="right-top" id="backToTopButton" @click="toTop"
      v-if="pageData.activeTab === 0 && pageData.isMain == '0' && hasTab">
      <div class="common-div">
        <img src="@/img/toTop.png" class="img1" alt="" />
        <div class="text1">返回顶部</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    hasTab: { type: Boolean, default: true },
  },
  data() {
    return {
      showAdverTise: true, //是否展示广告
      imgUrl: '',
      imgToRul: '',
      nodeName: '',
      unitId: '',
      level: '',
      defaultProps: {
        id: 'id',
        level: 'jiBie',
        label: 'name',
        children: 'children',
        isLeaf: 'isLeaf',
      },
      currentNodekey: '',
      searchKey: '', // 搜索关键字
      // tab切换栏
      tabList: [
        { label: '首页', order: 0 },
        { label: '新闻中心', order: 1 },
        { label: '公示公告', order: 2 },
        { label: '供需大厅', order: 8 },
        { label: '业务规则', order: 3 },
        { label: '政策法规', order: 4 },
        { label: '资料下载', order: 5 },
        { label: '关于我们', order: 6 },
        { label: '互动交流', order: 7 },
      ],
      pageData: {
        activeTab: 0, //选中的tab页
        isMain: '0',
      },
      currentTime: '',
      wordImgIndex: 0,
      imgList: [
        {
          url: require('@/assets/header/whbg_new1.png'),
          // wordUrl: require('@/assets/header/whword1.png'),
        },
        {
          url: require('@/assets/header/whbg_new2.png'),
          // wordUrl: require('@/assets/header/whword2.png'),
        },
        {
          url: require('@/assets/header/whbg_new3.png'),
          // wordUrl: require('@/assets/header/whword1.png'),
        },
      ],

      linkList: [
        {
          url: require('@/assets/header/whcqjy.png'),
          title: '产权交易',
          link: 'https://whnccq.com/whmanager/login/toLogin#/',
          router: null,
          isRouter: false,
        },
        {
          url: require('@/assets/header/whjrcs.png'),
          title: '金融超市',
          router: null,
          isRouter: false,
        },
        // {
        //   url: require('@/assets/header/whhygl.png'),
        //   title: '会员管理',
        //   router: null,
        //   isRouter: false,
        // },
        {
          url: require('@/assets/header/whkhzx.png'),
          title: '客户中心',
          link: 'https://whnccq.com/cq-hy/#/',
          router: null,
          isRouter: false,
        },
        {
          url: require('@/assets/header/zjrz.png'),
          title: '会员入驻',
          link: 'https://whnccq.com/cq-hy/#/',
          router: 'applicationIntermediarySettlementView',
          isRouter: true,
        },
      ],
      watchHeight: null,
    };
  },
  watch: {
    $route: {
      immediate: true,
      deep: true,
      handler() {
        // console.log(this.$route, '======');
        this.$nextTick(() => {
          this.$refs.mainBoxRef.style.position = 'static';
        });
        window.scroll(0, 0);
        if (this.$route.name === 'newsCenter') {
          this.pageData.activeTab = 1;
        } else if (this.$route.name === 'details') {
          this.pageData.activeTab = 2;
          //   this.$nextTick(() => {
          //     this.updateHeight();
          //     window.addEventListener("resize", this.updateHeight);
          //     this.$refs.mainBoxRef.style.position = 'absolute';
          //   });
        } else if (this.$route.name === 'publicInformationView') {
          this.pageData.activeTab = 2;
        } else if (this.$route.name === 'businessRule') {
          this.pageData.activeTab = 3;
        } else if (this.$route.name === 'lawsRegulation') {
          this.pageData.activeTab = 4;
        } else if (this.$route.name === 'profileDownload') {
          this.pageData.activeTab = 5;
        } else if (this.$route.name === 'aboutUs') {
          this.pageData.activeTab = 6;
        } else if (this.$route.name === 'interacteCommunication') {
          this.pageData.activeTab = 7;
        } else if (this.$route.name === 'supplyAndDemandHall') {
          this.pageData.activeTab = 8;
        } else {
          this.pageData.activeTab = 0;
          // this.getTradeCategoryList();
        }

        if (this.$route.query.bannerType == '1') {
          this.pageData.activeTab = 1;
        } else if (this.$route.query.bannerType == '2') {
          this.pageData.activeTab = 2;
        } else if (this.$route.query.bannerType == '3') {
          this.pageData.activeTab = 3;
        } else if (this.$route.query.bannerType == '4') {
          this.pageData.activeTab = 4;
        } else if (this.$route.query.bannerType == '5') {
          this.pageData.activeTab = 5;
        } else if (this.$route.query.homeType == 1) {
          this.pageData.activeTab = 1;
        } else if (this.$route.query.homeType == 2) {
          this.pageData.activeTab = 2;
        }

        if (this.$route.name === 'details') {
          this.$nextTick(() => {
            setTimeout(() => {
              this.updateHeight();
              window.addEventListener('resize', this.updateHeight);
              this.$refs.mainBoxRef.style.position = 'absolute';
            }, 100);
          });
        }
      },
    },
  },
  mounted() {
    window.addEventListener('beforeunload', this.getRefresh);
    // console.log(commonData,"后台数据");
    // this.$ajax({
    //     method: 'get',
    //     url: '/baseInfo/getWebSiteName',
    //     data: {},
    //   }).then((res) => {
    //     if (res.data.code == 200) {
    //       this.webName = res.data.data.memberWebName
    //     }
    //   });
    this.getCurrentTime();
    this.getNewstrends();
    this.$bus.$on('searchData', (searchKey) => {
      this.searchKey = searchKey;
    });
    window.addEventListener('scroll', () => {
      var scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
      if (scrollPosition > 0) {
        // 显示返回顶部按钮
        try {
          document.getElementById('backToTopButton').style.display = 'block';
        } catch (error) { }
      } else {
        // 隐藏返回顶部按钮
        try {
          document.getElementById('backToTopButton').style.display = 'none';
        } catch (error) { }
      }

      this.$nextTick(() => {
        try {
          if (scrollPosition > 660) {
            this.$refs.advertiseRef.style.position = 'fixed';
            this.$refs.advertiseRef.style.top = '60px';

            console.log(this.$refs.advertiseRef.offsetTop);
          } else {
            this.$refs.advertiseRef.style.position = 'absolute';
            this.$refs.advertiseRef.style.top = '200px';
          }
        } catch (error) { }
      });
    });
  },
  beforeDestroy() {
    // 销毁自定义事件
    this.$bus.$off(['searchData']);
    window.removeEventListener('beforeunload', this.getRefresh);
    window.removeEventListener('resize', this.updateHeight);
  },

  methods: {
    toHome() {
      this.$router.push({
        path: '/',
      });
    },
    getRefresh() {
      this.pageData.isMain = '0';
    },

    // 获取新闻数据
    getNewstrends() {
      this.$ajax({
        url: '/syxfctp/syxfctp/',
        method: 'get',
        serverName: 'nd-ss',
      }).then((r) => {
        let newArr = eval(r.data);
        console.log(newArr, '图片===========');
        this.imgUrl = newArr[0].imgUrl;

        // this.imgToRul = newArr[0].path
      });
    },
    getDealInformationAdd(val) {
      this.pageData.isMain = val;
      console.log(this.pageData.isMain, val, '3');
    },
    tabActive(e) {
      this.pageData.isMain = '0';
      console.log(e.order, '选中的tab');
      this.pageData.activeTab = e.order;
      // if (e.order !== 5 && e.order !== 6 && e.order !== 7) {
      //   this.pageData.activeTab = e.order;
      // }
      switch (e.order) {
        case 0:
          this.$router.push('/');
          break;
        case 1:
          this.$router.push('newsCenter');
          break;
        case 2:
          this.$router.push('publicInformationView');
          break;
        case 3:
          this.$router.push('businessRule');
          break;
        case 4:
          this.$router.push('lawsRegulation');
          break;
        case 5:
          this.$router.push('profileDownload');
          break;
        case 6:
          this.$router.push('aboutUs');
          break;
        case 7:
          this.$router.push('interacteCommunication');
          break;
        case 8:
          this.$router.push('supplyAndDemandHall');
          break;
      }
    },

    toTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth', // 平滑滚动效果
      });
    },

    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = this.padNumber(now.getMonth() + 1); // 月份是从0开始的
      const day = this.padNumber(now.getDate());
      const dayOfWeek = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][
        now.getDay()
      ];
      this.currentTime = `今天是${year}年${month}月${day}日 ${dayOfWeek}`;
      console.log(this.currentTime, '当前时间');
      // return `${year}年${month}月${day}日`;
    },

    padNumber(num) {
      return num < 10 ? '0' + num : num;
    },

    // 节点点击
    handleNodeClick(node) {
      console.log('handleNodeClick', node);
      this.nodeName = node[this.defaultProps.label];
      this.unitId = node[this.defaultProps.id];
      this.level = node.level;
      // this.getData();
      this.$refs.ndSelect.blur();
      this.$bus.$emit('refreshList', { unitId: this.unitId, level: this.level });
    },

    // 获取地区树
    getAreaTree(node, resolve) {
      console.log('node:', node);
      if (node.level === 0) {
        this.$ajax({
          method: 'get',
          url: '/area/webServiceLazyLoadRegionTree',
          data: {},
        }).then((res) => {
          if (res.data.code === 200) {
            console.log('地区树的接口', res.data.data);
            this.nodeName = res.data.data[0].name;
            this.unitId = res.data.data[0].id;
            this.level = res.data.data[0].level;
            this.$bus.$emit('refreshList', { unitId: this.unitId, level: this.level });
            this.currentNodekey = res.data.data[0].id;
            this.$nextTick(function () {
              this.$refs.tree.setCurrentKey(this.currentNodekey);
            });
            // this.getData();

            return resolve(res.data.data);
          }
        });
      } else {
        this.$ajax({
          method: 'get',
          url: '/area/webServiceLazyLoadRegionTree',
          data: {
            areaId: node.data.id,
          },
        }).then((res) => {
          if (res.data.code == 200) {
            return resolve(res.data.data);
          }
        });
      }
    },

    handleSearch() {
      this.$router.push({
        path: 'publicInformationView',
        query: { projectNameOrCode: this.searchKey, type: 0 },
      });
    },

    goIpConfig(item, index) {
      if (item.isRouter) {
        // let routeData = this.$router.resolve({ name: item.router });
        // window.open(routeData.href, '_blank');
        this.$router.push(item.router);
        return;
      }
      console.log('跳转', index);
      if (item.link) {
        window.open(item.link);
      } else {
        this.$message.success('敬请期待！');
      }
    },

    closeAdverTise() {
      this.showAdverTise = false;
    },

    carouselChange(e) {
      console.log(e, typeof e, '轮播图');
      this.wordImgIndex = e;
    },
    jumpLink() {
      return;
      window.open('https://njs.whnccq.com/list-b04472e8f12041828f219381c8f6d181.html');
    },
    updateHeight() {
      this.$nextTick(() => {
        this.$emit('detailTopHeight', this.$refs.mainBoxRef.offsetHeight + 'px');
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.main-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f7f7f7;

  .top-cont {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;

    .topc-top {
      height: 80px;
      // width: 100%;
      // padding: 0 310px;
      width: 1300px;

      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      position: relative;
      left: 50%;
      transform: translateX(-50%);

      .topct-right {
        display: flex;
        justify-content: flex-end;

        .topct-inner {
          display: flex;
          align-items: center;
          flex-direction: row;
          // padding: 0 50px 0 110px;

          .innner-text {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 18px;
            color: red;
          }

          .innner-search {
            height: 30px;
            display: flex;
            align-items: center;

            ::v-deep .el-input__inner {
              height: 38px;
              line-height: 38px;
              // background-color: #60c6d7;
              // border: 1px solid #60c6d7;
              color: #666;
              border-radius: 8px 0 0px 8px;
            }

            ::v-deep .el-input-group {
              border-radius: 8px;
            }

            // ::v-deep .el-input__icon {
            //   line-height: 38px;
            //   color: #fff;
            // }

            ::v-deep .el-icon-search {
              color: #fff;
              // position: relative;
              // left: 50%;
              // transform: translateX(-50%);
            }

            ::v-deep .el-input-group__append,
            .el-input-group__prepend {
              padding: 0 15.25px;
              // width: 47px;
              background: #ed911f;
              border-radius: 0px 8px 8px 0;
              border: 1px solid #ed911f;
              // cursor: pointer;
            }
          }
        }
      }

      .topct-left {
        display: flex;
        align-items: center;

        .topct-text {
          margin-right: 67px;
          cursor: pointer;
        }

        ::v-deep .el-input__inner {
          height: 32px;
          line-height: 32px;
          border-radius: 10px;
          border: 1px solid #ed911f;
          color: #666;
        }

        ::v-deep .el-input__icon {
          line-height: 32px;
          // color: #ffffff;
        }

        ::v-deep .el-input--prefix .el-input__inner {
          padding-left: 22px;
        }

        ::v-deep .el-input__prefix {
          display: flex;
          align-items: center;
        }

        ::v-deep .el-icon-location {
          color: #ed911f;
        }
      }
    }

    .topc-bot {
      height: 44px;
      background: #0098ff;
      display: flex;
      align-items: center;
      flex-direction: row;
      // padding: 0 310px;

      .topcb-right {
        flex: 1;
        height: 100%;
        display: flex;
        justify-content: center;

        .tab-cont {
          height: 100%;
          width: 1300px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;

          .tab-item-box {
            height: 100%;
            // flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            position: relative;

            .tab-active {
              height: 100%;
              width: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              cursor: pointer;
              font-family: Microsoft YaHei;
              font-weight: bold;
              font-size: 19px;
              color: #ffffff;
              line-height: 24px;
            }

            .tab-item {
              height: 100%;
              width: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 19px;
              color: #ffffff;
              line-height: 24px;
              cursor: pointer;
            }

            .active-line {
              // width: 88px;
              width: 100%;
              height: 3px;
              background: #ffffff;
            }

            .active-line-shadow {
              position: absolute;
              bottom: 0;
              left: 0;
              display: none;
            }

            &:hover .active-line-shadow {
              display: block;
            }
          }
        }
      }
    }
  }

  .big-bg {
    width: 100%;
    // position: relative;
    // margin-bottom: 16px;
    // background: #0098ff;

    ::v-deep .el-carousel__indicators--horizontal {
      z-index: 999999;
    }

    .word-img {
      position: absolute;
      top: 124px;
      z-index: 999;
      // height: 400px;
      width: 100%;
      display: flex;
      justify-content: center;

      img {
        min-width: 1300px;
        // height: 100%;
        // position: relative;
        // left: 50%;
        // transform: translateX(-50%);
      }
    }

    .top-bg {
      width: 100%;
      // height: 180px;
      position: relative;

      // min-width: 1300px;
      .top-imgbg {
        height: 100%;
        width: 100%;
      }

      // .top-imgword {
      //   z-index: 999999;
      //   position: absolute;
      //   top: 0px;
      //   left: 0px;
      //   width: 100%;
      //   height: 100%;
      //   min-width: 1300px;
      // }

      .top-imgword {
        /* z-index: 999999; */
        z-index: 99;
        position: absolute;
        width: 100%;
        top: 50%;
        left: 50%;
        min-width: 1300px;
        max-width: 1920px;
        transform: translate(-50%, -50%);
      }
    }

    .carousel-img {
      width: 100%;
      height: 100%;
    }

    ::v-deep .el-carousel__button {
      width: 10px;
      height: 10px;
      // background: rgba(255, 255, 255, 0.3);
      background: #fff;
      opacity: 1;
      border-radius: 50%;
    }

    ::v-deep .el-carousel__indicator.is-active button {
      background: #ff9c00;
    }
  }
}

.custom-placeholder ::placeholder {
  color: #999;
  /* 指定颜色 */
}

.goBox {
  padding: 16px 0px 0;
  display: flex;
  // justify-content: space-between;
  align-items: center;
  // height: 160px;
  background: #f7f7f7;
  width: 1300px;
  // overflow: hidden;
  position: relative;
  justify-content: space-between;

  .item-box:nth-child(-n + 3) {
    // margin-right: 20px;
  }

  .item {
    width: 244px;
    height: 100px;
    // overflow: hidden;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 36px;
    // padding-left: 159px;
    position: relative;

    &:hover {
      top: -5px;
      box-shadow: 0 4px 10px #ccc;
      transition: 0.3s;
    }

    .link-text {
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 24px;
      color: #333333;
      line-height: 24px;
    }

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.right-app {
  width: 68px;
  // height: 140px;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  position: fixed;
  z-index: 999;
  top: 534px;
  right: 32px;
  background: #ffffff;
  padding-top: 11px;
  padding: 11px 0;

  .common-div {
    display: flex;
    flex-direction: column;
    align-items: center;

    .img1 {
      width: 28px;
      height: 30px;
      margin-bottom: 4px;
    }

    .text1 {
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      // line-height: 30px;
      margin-bottom: 18px;
      height: 14px;
    }

    .img2 {
      width: 26px;
      height: 26px;
      margin-bottom: 6px;
    }

    .text2 {
      height: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      // line-height: 30px;
    }
  }

  .active-div {
    text-align: center;
    display: none;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #ed911f;

    .img1 {
      width: 28px;
      height: 30px;
      margin-top: 11px;
    }

    .text1 {
      margin-top: 6px;
    }

    .img2 {
      width: 26px;
      height: 26px;
      margin-top: 20px;
    }

    .text2 {
      margin-top: 8px;
    }
  }

  .common-item1 {
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover .QrCode-div1 {
      display: block;
    }
  }

  .common-item2 {
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover .QrCode-div2 {
      display: block;
    }
  }

  .QrCode-div1 {
    display: none;
    width: 140px;
    background: #ffffff;
    box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    position: absolute;
    left: -146px;
    top: 0;
    padding: 10px;
    text-align: center;
    z-index: 999;

    .qrcode {
      img {
        width: 120px;
        height: 120px;
      }
    }

    .texta {
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 16px;
      color: #424242;
      margin-top: 10px;
    }
  }

  .QrCode-div2 {
    //new
    display: none;
    width: 440px;
    height: 340px;
    background: #ffffff;
    box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    position: absolute;
    left: -448px;
    top: -140px;
    padding: 10px;
    text-align: center;
    z-index: 999;

    .cont {
      height: calc(100% / 6);
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      align-content: center;
      // flex-direction: column;
      flex-wrap: nowrap;

      .textb {
        width: 60%;
        text-align: right;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #333333;

        // margin-bottom: 10px;

      }

      .textc {
        width: 40%;
        text-align: left;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ed911f;
        white-space: nowrap;
        text-indent: 2em;
      }
    }
  }
}

.right-top {
  width: 68px;
  height: 70px;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  position: fixed;
  z-index: 999;
  top: 740px;
  right: 32px;
  background: #ffffff;
  padding-top: 11px;
  cursor: pointer;
  display: none;

  .common-div {
    display: flex;
    flex-direction: column;
    align-items: center;

    .img1 {
      width: 28px;
      height: 30px;
      margin-bottom: 4px;
    }

    .text1 {
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      // line-height: 30px;
      margin-bottom: 18px;
      height: 14px;
    }

    .img2 {
      width: 26px;
      height: 26px;
      margin-bottom: 6px;
    }

    .text2 {
      height: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      // line-height: 30px;
    }
  }

  .active-div {
    text-align: center;
    display: none;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #ed911f;

    .img1 {
      width: 28px;
      height: 30px;
      margin-top: 11px;
    }

    .text1 {
      margin-top: 6px;
    }

    .img2 {
      width: 26px;
      height: 26px;
      margin-top: 20px;
    }

    .text2 {
      margin-top: 8px;
    }
  }

  .common-item1 {
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover .QrCode-div1 {
      display: block;
    }
  }

  .common-item2 {
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover .QrCode-div2 {
      display: block;
    }
  }

  .QrCode-div1 {
    display: none;
    width: 140px;
    background: #ffffff;
    box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    position: absolute;
    left: -146px;
    top: 0;
    padding: 10px;
    text-align: center;
    z-index: 999;

    .qrcode {
      img {
        width: 120px;
        height: 120px;
      }
    }

    .texta {
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 16px;
      color: #424242;
      margin-top: 10px;
    }
  }

  .QrCode-div2 {
    display: none;
    width: 140px;
    height: 140px;
    background: #ffffff;
    box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    position: absolute;
    left: -146px;
    top: 0;
    padding: 10px;
    text-align: center;
    z-index: 999;

    .cont {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .textb {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #333333;
        margin-bottom: 10px;
      }

      .textc {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ed911f;
        white-space: nowrap;
      }
    }
  }
}

.advertise-box {
  //   position: absolute;
  //   top: 110px;
  //   left: -200px;
  z-index: 999;
  position: absolute;
  top: 200px;
  /* left: 50%; */
  z-index: 999;
  /* transform: translateX(-50%); */
  margin-left: -200px;

  .close-img {
    position: relative;
    right: -22px;
    top: 5px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>

<style>
*::-webkit-scrollbar {
  width: 10px;
  height: 10px !important;
  width: 6px;
  height: 12px !important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 12px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}

.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 230px;
  /* padding: 0; */
  padding: 0 10px;
  overflow: hidden;
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
  color: #ed911f;
}

ul li>>>.el-tree .el-tree-node__content:hover {
  background-color: #e8f7ff;
}

ul li>>>.el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
  background-color: #ffffff;
}

.el-tree-node__label {
  font-weight: normal;
  margin-right: 10px;
  font-size: 12px;
}

.el-tree .is-current .el-tree-node__label {
  color: #ed911f;
  font-weight: 700;
}

.el-tree .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}

.el-select-dropdown__item.hover {
  background: transparent;
}

.el-select-dropdown__item span {
  margin-left: 10px;
}

::v-deep .el-scrollbar__bar.is-horizontal {
  height: 0px;
}

::v-deep .el-scrollbar__bar.is-vertical {
  width: 0px;
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #c8ecff;
}

.el-select-dropdown__item span {
  margin-left: 0px;
}

.el-scrollbar .el-scrollbar__view .text-hover:hover {
  background: #e8f7ff;
}

/* ::v-deep .el-scrollbar .el-scrollbar__bar.is-vertical .el-scrollbar__thumb {
  background: #D4E6FB !important;
} */
::v-deep .is-vertical .el-scrollbar__thumb {
  background: #d4e6fb !important;
}

::v-deep .el-scrollbar__bar.is-vertical {
  background-color: #f2f7ff !important;
}

.img-box {
  width: 163px;
  height: 402px;
  /* cursor: pointer; */
}
</style>
