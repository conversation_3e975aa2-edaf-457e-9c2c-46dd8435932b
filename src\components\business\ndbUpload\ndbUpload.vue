<template>
  <div class="nd-upload-box">
    <div class="file-box">
      <div v-for="(item, index) in fileList" :key="index" class="file-item">
        <el-image v-if="item.suffix != 'pdf' && item.suffix != 'PDF'" class="file-img" :src="item.fileUrl"
          :preview-src-list="imagePreviewList" @click="clickImage(item)" />
        <div v-if="item.suffix == 'pdf' || item.suffix == 'PDF'" class="file-img" @click="openPDf(item.fileUrl)">
          <img src="./images/pdf.png" alt="" />
        </div>
        <div v-if="isSz" class="file-name" @click="downLoadOneFile(item.fileId)">
          {{ item.fileName }}
        </div>
        <i v-if="!disabled || !noPlus" class="el-icon-remove" @click="deleteImg(item)" />
      </div>
      <div v-if="fileList && fileList.length === 0 && disabled" class="no-data">
        <img src="./images/noImges.png" class="no-data-img" alt="" />
        <!-- 暂无数据（产品说不要，先预留） -->
      </div>
      <div v-if="!disabled" class="file-item file-item-add" @click="openUploadDialog()">+</div>
    </div>
    <ndb-upload-dialog ref="ndbUploadDialogRef" v-model="fileList" :is-need-p-d-f="isNeedPDF" :data-id="dataId"
      :file-type-id="fileTypeId" :server-name="serverName" :show-phone-upload="showPhoneUpload"
      :show-scanner-upload="showScannerUpload" :show-metronome-upload="showMetronomeUpload" :is-sz="isSz"
      :function-id="functionId" :is-change-name="isChangeName" :is-change-order="isChangeOrder"
      :is-need-clear-all="isNeedClearAll" :show-village-reimbursement="showVillageReimbursement"
      :is-need-describe="isNeedDescribe" :is-needtrue-click="isNeedtrueClick" :is-z-t="isZT"
      :function-name="functionName" :model-type="modelType" :menu-entry="menuEntry"
      :is-need-bill-recognition="isNeedBillRecognition" :isFileRequired="isFileRequired"
      @beforeClose="beforeDialogClose" />
  </div>
</template>

<script>
import ndbUploadDialog from '@/components/business/ndbUpload/ndbUploadDialog.vue';
export default {
  components: {
    ndbUploadDialog,
  },
  props: {
    // 是否只读
    disabled: {
      type: Boolean,
      default: false,
    },
    // 是否不要加号
    noPlus: {
      type: Boolean,
      default: false,
    },
    // 是否需要上传pdf
    isNeedPDF: {
      type: Boolean,
      default: true,
    },
    // 所属模块id
    functionId: {
      type: String,
      default: '',
    },
    // 批次id
    dataId: {
      type: String,
      default: '',
    },
    // 类型id
    fileTypeId: {
      type: String,
      default: '',
    },
    //模块名称
    functionName: {
      type: String,
      default: '',
    },
    // 服务器
    serverName: {
      type: String,
      default: 'nd-oneThree',
    },
    // 是否显示手机上传按钮
    showPhoneUpload: {
      type: Boolean,
      default: true,
    },
    // 是否显示高拍仪上传按钮
    showMetronomeUpload: {
      type: Boolean,
      default: true,
    },
    // 是否显示扫描仪上传按钮
    showScannerUpload: {
      type: Boolean,
      default: false,
    },
    // 是否是三资
    isSz: {
      type: Boolean,
      default: false,
    },
    // 是否有重命名功能
    isChangeName: {
      type: Boolean,
      default: false,
    },
    // 是否有调整顺序功能
    isChangeOrder: {
      type: Boolean,
      default: false,
    },
    // 是否显示村报账
    showVillageReimbursement: {
      type: Boolean,
      default: false,
    },
    // 是否需要全部清空
    isNeedClearAll: {
      type: Boolean,
      default: false,
    },
    // 是否需要事项描述功能
    isNeedDescribe: {
      type: Boolean,
      default: false,
    },
    // 是否需要确定按钮
    isNeedtrueClick: {
      type: Boolean,
      default: false,
    },
    // 是否需要账套
    isZT: {
      type: String,
      default: '0',
    },
    // 是否需要票据识别
    isNeedBillRecognition: {
      type: Boolean,
      default: false,
    },
    // 判断是哪个菜单进入
    menuEntry: {
      type: String,
      default: '',
    },
    // 手机二维码使用的模块类型
    modelType: {
      type: String,
      default: '',
    },
    // 附件上传是否有限制
    fileLimt: {
      type: Boolean,
      default: false,
    },
    // 附件是必填项模块才需要传过来的参数
    isFileRequired: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      key: '0', //0没有经过放大缩小的旋转，1经过放大缩小的旋转
      fileId: '', //图片id图片旋转使用
      deg: '', //图片旋转角度图片旋转用
      fileList: [],
      moduleIdentifier: '', //是否是票据管理 不是0 是的话1
      orderNumber: '', //点击每张图片的编号
      rotateImgtimer: null,
      rotateImgover: true,
    };
  },
  computed: {
    // 图片预览
    imagePreviewList() {
      // console.log(this.fileList);
      var lists = [];
      // debugger
      this.fileList.map((item, index) => {
        lists.push(item.fileUrl);
        this.$set(item, 'suffix', item.fileSuffix);
        this.$set(item, 'ORDERNUMBER', item.orderNumber);
        this.$set(item, 'changeName', false);
        this.$set(item, 'curName', item.fileName);
      });
      return lists;
    },
  },
  watch: {
    // 批次id
    dataId: {
      immediate: true,
      handler(value) {
        // this.getFiles();
      },
    },
  },
  mounted() { },
  methods: {
    // //获取服务器
    getServerPath() {
      if (typeof getContextPath === "function") {
        // console.log("获得服务器路径:" + getContextPath());
        return getContextPath();
      } else {
        return "/sz_product_new";
      }
    },
    // 实现图片旋转保存
    clickImage(item) {
      this.orderNumber = Number(item.orderNumber);
      this.fileId = item.fileId;
      this.$nextTick(() => {
        let prever = document.getElementsByClassName('el-image-viewer__prev'); //获取前一张图片的dom实例对象
        let nexter = document.getElementsByClassName('el-image-viewer__next'); //获取后一张图片的dom实例对象
        let wrapper = document.getElementsByClassName('el-image-viewer__actions__inner'); //拿到下面一排按钮的dom实例对象
        let wrapperLeft = document.getElementsByClassName('el-icon-refresh-left'); //左旋按钮dom实例对象
        let wrapperRight = document.getElementsByClassName('el-icon-refresh-right'); //右旋按钮dom实例对象
        let wrapperClose = document.querySelector('.el-image-viewer__close'); //关闭按钮dom实例对象
        wrapperClose.addEventListener('click', this.hideCusBtnClose); //给关闭按钮添加点击事件，此时要刷新附件
        let wrapperMask = document.querySelector('.el-image-viewer__mask'); //遮罩层dom实例对象
        wrapperMask.addEventListener('click', this.hideCusBtnMask); //遮罩层添加点击事件，此时要刷新附件
        // let downImg = document.createElement('i');
        // downImg.setAttribute('class', 'el-icon-check');
        // wrapper[0].appendChild(downImg);//添加最后一排√按钮
        console.log(prever, 'preverpreverprever');
        // console.log(nexter, 'nexternexternexternexter');
        // console.log(wrapper, 'wrapperwrapperwrapperwrapperwrapper');
        // console.log(wrapperLeft, 'wrapperLeftwrapperLeft');
        // console.log(wrapperRight, 'wrapperRightwrapperRight');
        // console.log(wrapperClose, 'wrapperClosewrapperClosewrapperClose');
        if (wrapper.length > 0) {
          // console.log('0000000000');
          this.wrapperElem = wrapper[0];
          // this.cusClickHandler();
        }
        // 上一张图片按钮
        if (prever.length > 0) {
          console.log('1111111111');
          this.preverElem = prever[0];
          this.cusClickHandlerLeft();
        }
        // 下一张图片按钮
        if (nexter.length > 0) {
          console.log('222222222222222');
          this.nexterElem = nexter[0];
          this.cusClickHandlerRight();
        }
        // 左旋按钮
        if (wrapperLeft.length > 0) {
          console.log('33333');
          clearInterval(this.rotateImgtimer)
          this.rotateImgtimer = null;
          this.wrapperLeftElem = wrapperLeft[0];
          this.wrapperLeftElem.addEventListener('click', this.hideCusBtnWrapperLeft);
        }
        // 右旋按钮
        if (wrapperRight.length > 0) {
          console.log('4444444444');
          clearInterval(this.rotateImgtimer)
          this.rotateImgtimer = null;
          this.wrapperRightElem = wrapperRight[0];
          this.wrapperRightElem.addEventListener('click', this.hideCusBtnWrapperRight);
        }
      });
    },
    //上一张图片按钮添加点击事件
    cusClickHandlerLeft() {
      this.preverElem.addEventListener('click', this.hideCusBtnLeft);
    },
    // 下一张图片按钮添加点击事件
    cusClickHandlerRight() {
      this.nexterElem.addEventListener('click', this.hideCusBtnRight);
    },
    // 逆时针旋转（向左旋转）-90度传给后端
    hideCusBtnWrapperLeft(e) {
      this.deg = Number(this.deg) + Number(-90);
      console.log(this.deg, 'this.degthis.degthis.degthis.deg');
      // if (this.rotateImgover) {
      //   this.rotateImgover = false
      //   this.rotateImg()
      // }
    },
    // 顺时针旋转（向右旋转）90度传给后端
    hideCusBtnWrapperRight(e) {
      this.deg = Number(this.deg) + Number(90);
      console.log(this.deg, 'this.degthis.degthis.degthis.deg');
      // if (this.rotateImgover) {
      //   this.rotateImgover = false
      //   this.rotateImg()
      // }
    },
    // 点击×按钮关闭遮罩层刷新附件
    hideCusBtnClose(e) {
      // this.rotateImgover = true
      if (this.deg % 360 === 0) {
        this.deg = Number(0);
        // setTimeout(function () {
        //   this.getFiles();
        // }, 2000);
      } else {
        this.rotateImg();
        this.deg = Number(0);
      }
    },
    // 点击其他区域关闭遮罩层刷新附件
    hideCusBtnMask(e) {
      // this.rotateImgover = true
      if (this.deg % 360 === 0) {
        this.deg = Number(0);
      } else {
        this.rotateImg();
        this.deg = Number(0);
      }
    },
    // 上一张图片按钮点击事件，拿到当前图片的fileId，并且要刷新附件
    hideCusBtnLeft(e) {
      // this.rotateImgover = true
      if (this.deg % 360 === 0) {
        this.deg = Number(0);
        if (this.orderNumber > 1) {
          this.orderNumber = this.orderNumber - 1;
        } else {
          this.orderNumber = this.fileList.length;
        }
        this.fileId = this.fileList[this.orderNumber - 1].fileId;
        // this.getFiles();
      } else {
        this.rotateImg();
        this.deg = Number(0);
        if (this.orderNumber > 1) {
          this.orderNumber = this.orderNumber - 1;
        } else {
          this.orderNumber = this.fileList.length;
        }
        this.fileId = this.fileList[this.orderNumber - 1].fileId;
        // this.getFiles();
      }
    },
    // 下一张图片按钮点击事件，拿到当前图片的fileId，并且要刷新附件
    hideCusBtnRight(e) {
      // this.rotateImgover = true
      if (this.deg % 360 === 0) {
        this.deg = Number(0);
        if (this.orderNumber > 1) {
          this.orderNumber = this.orderNumber - 1;
        } else {
          this.orderNumber = this.fileList.length;
        }
        this.fileId = this.fileList[this.orderNumber - 1].fileId;
        // this.getFiles();
      } else {
        this.rotateImg();
        this.deg = Number(0);
        if (this.orderNumber > 1) {
          this.orderNumber = this.orderNumber - 1;
        } else {
          this.orderNumber = this.fileList.length;
        }
        this.fileId = this.fileList[this.orderNumber - 1].fileId;
        // this.getFiles();
      }
    },
    // 旋转图片的方法
    rotateImg() {
      // this.rotateImgtimer = setTimeout(() => {

      // }, 2000)
      if (this.isDocumentManagement) {
        this.moduleIdentifier = 1;
      } else {
        this.moduleIdentifier = 0;
      }
      let params = {
        fileId: this.fileId,
        rotationAngle: this.deg,
        moduleIdentifier: this.moduleIdentifier,
      };
      // 请求后台旋转图片
      this.$ajax({
        url: '/szFileNew/rotatingImages.do',
        method: 'POST',
        data: params,
      }).then((res) => {
        if (res.data.code === 0) {
          console.log('图片旋转保存成功');
          this.getFiles();
          // this.$message({
          //   showClose: true,
          //   message: '图片旋转保存成功',
          //   type: 'success',
          // });
          // this.getFiles();
          // document.querySelector(".el-image-viewer__close").click()
          // this.key = '0';
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'warning',
          });
        }
      });
    },
    //给最下面一排旋转放大缩小按钮等添加点击事件
    // cusClickHandler() {
    //   this.wrapperElem.addEventListener('click', this.hideCusBtn);
    // },
    // 最下面一排旋转放大缩小等按钮点击事件
    // hideCusBtn(e) {
    //   let className = e.target.className;
    //   console.log(className, 'ssss');
    //   // if (className === "el-icon-zoom-out" || className === "el-icon-zoom-in") {
    //   //   this.key = "1";
    //   // }
    //   // 旋转照片
    //   // if (className === "el-icon-refresh-right" || className === "el-icon-refresh-left") {
    //   //   if (this.key === "0") {
    //   //     //没有经过放大缩小
    //   //     var element = document.getElementsByClassName("el-image-viewer__img");
    //   //     console.log(element,'***************');
    //   //     var degA = element[0].style.transform.substring(16);
    //   //     var deg = degA.substring(0, degA.length - 4);
    //   //     this.deg = Number(deg);
    //   //        console.log(deg, "没有经过放大缩小");
    //   //     // this.rotateImg()

    //   //   } else {
    //   //     //经过放大缩小
    //   //     var element = document.getElementsByClassName("el-image-viewer__img");
    //   //        console.log(element,'***************');
    //   //     var degA = element[0].style.transform.substring(16);
    //   //     var deg = degA.substring(0, degA.length - 4).slice(2);
    //   //     this.deg = Number(deg);
    //   //      console.log(deg, "经过放大缩小");
    //   //     //  this.rotateImg()

    //   //   }
    //   // }

    //   // 确认修改
    //   if (className === 'el-icon-check') {
    //     this.rotateImg();
    //   }
    // },
    //下载单个附件
    downLoadOneFile(id) {
      window.open(this.getServerPath() + '/apiFile/picDownloadUrl.do?id=' + id)
    },
    //下载全部附件
    downLoadAllFile() {
      if (this.fileList.length === 0) {
        this.$message.warning('暂无可下载附件')
        return;
      }
      this.getAllFileID().then((ids) => {
        this.$ajax({
          url: '/apiFile/downLoadAllFiles.do',
          method: 'post',
          data: { ids },
          responseType: 'blob'
        }).then((res) => {
          if (!res) return;
          const blob = new Blob([res.data], {
            type: "application/zip",
          });
          if (window.navigator.msSaveOrOpenBlob) {
            //兼容IE10
            navigator.msSaveBlob(blob, "附件.zip");
          } else {
            const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
            const a = document.createElement("a"); //创建a标签
            a.style.display = "none";
            a.href = href; // 指定下载链接
            a.setAttribute("download", "附件.zip");
            a.click(); //触发下载
            URL.revokeObjectURL(a.href); //释放URL对象
          }
        })
      })
    },
    //获取所有附件的id
    getAllFileID() {
      if (this.fileList.length === 1) {
        this.downLoadOneFile(this.fileList[0].fileId)
        return;
      }
      return new Promise(reslove => {
        let ids = []
        this.fileList.forEach(element => {
          ids.push(element.fileId)
        });
        reslove(ids)
      })
    },
    // 获得附件
    getFiles() {
      if (this.isSz) {
        let params = {
          dataId: this.dataId,
          functionId: this.functionId,
          fileTypeId: this.fileTypeId,
        };
        this.$ajax({
          url: '/apiFile/getFileList.do',
          method: 'post',
          data: params,
        }).then((res) => {
          if (res.data.code == 0) {
            res.data.data.forEach((element) => {
              this.$set(element, 'suffix', element.fileSuffix);
              this.$set(element, 'ORDERNUMBER', element.orderNumber);
              this.$set(element, 'changeName', false);
              this.$set(element, 'curName', element.fileName);
            });
            this.fileList = res.data.data;
            this.getFilesCount();
            // if (res.data.code == 0) {
            //   res.data.data.forEach((element) => {
            //     this.$set(element, "suffix", element.fileSuffix);
            //   });
            //   this.fileList = res.data.data;
            //   this.getFilesCount();
            // }
            this.$emit('successGetFile', true);
          }
        });
      } else {
        let params = {
          doid: this.dataId,
          fzgs: this.fileTypeId,
        };
        this.$ajax({
          url: '/resources/findAll',
          method: 'get',
          data: params,
          serverName: this.serverName,
        }).then((res) => {
          if (res.data.code == 200) {
            this.fileList = res.data.data;
            this.$emit('successGetFile', true);
          }
        });
      }
    },
    // 获得附件数量
    getFilesCount() {
      if (this.fileList) {
        return this.fileList.length;
      } else {
        return 0;
      }
    },
    // 打开附件上传对话框
    openUploadDialog() {
      if (this.fileLimt) {
        if (this.getFilesCount() == '0') {
          this.$refs.ndbUploadDialogRef.open();
        } else {
          this.$message({
            showClose: true,
            message: '票据底图最多只能有一张',
            type: 'warning',
          });
        }
      } else {
        this.$refs.ndbUploadDialogRef.open();
      }
    },
    // 预览pdf
    openPDf(url) {
      window.open(url);
    },
    // 删除图片
    deleteImg(item) {
      if (this.isFileRequired) {
        if (this.fileList.length === 1) {
          this.$message.warning('至少保留一个附件！')
          return;
        }
      }
      if (this.isSz) {
        let params = {
          id: item.fileId,
          dataId: this.dataId,
          functionId: this.functionId,
        };
        this.$ajax({
          url: '/szFileNew.do?method=webfiledelete',
          method: 'post',
          params: params,
        }).then((res) => {
          if (res.data.code === 0) {
            this.$message.success('删除成功');
            this.getFilesCount();
            this.fileList.forEach((e, index) => {
              if (item.fileId === e.fileId) {
                this.fileList.splice(index, 1);
              }
            });
          }
        });
      } else {
        let params = {
          // doid: this.dataId,
          // fzgs: this.fileTypeId,
          // sourceId: item.sourceId,
          // isDel: 1,
          busId: this.dataId,
          id: item.id,
        };
        this.$ajax({
          url: '/web/deleteFileById',
          method: 'get',
          data: params,
          // serverName: this.serverName,
        }).then((res) => {
          this.$message.success('删除成功');
          this.fileList = res.data.data;
        });
      }
    },
    //清空所有附件
    clearAllFile() {
      if (this.fileList.length === 0) {
        this.$message.warning('没有需清空的附件');
        return false;
      }
      let cleanAllFilesUrl = "/apiOcr/cleanAllFiles.do"
      if (this.isFileRequired) {
        cleanAllFilesUrl = "/apiOcr/cleanAllFilesSet.do"
      }
      let params = {
        doId: this.dataId,
        ztQy: '0',
      };
      this.$ajax({
        url: cleanAllFilesUrl,
        method: 'POST',
        data: params,
      }).then(async (res) => {
        if (res.data.code === 0) {
          if (this.isFileRequired) {
            await this.getFiles()
          } else {
            this.fileList = [];
          }
          // 组件内附件清空
          await this.getFilesCount();
        }
      });
    },
    // 附件上传对话框beforeClose
    beforeDialogClose() {
      this.$emit('beforeClose', this.fileList);
    },
  },
};
</script>
<style lang="scss" scoped>
.nd-upload-box {
  width: 100%;
  height: auto;

  .file-box {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;
    flex-wrap: wrap;

    .file-item {
      float: left;
      list-style: none;
      width: 94px;
      height: 104px;
      border: 1px solid #dce6f3;
      border-radius: 2px;
      cursor: pointer;
      position: relative;

      .file-img {
        width: 100%;
        height: 100%;

        img {
          width: 94px;
          height: 104px;
        }

        ::v-deep .el-image__inner {
          width: 94px;
          height: 104px;
        }
      }

      .file-name {
        position: absolute;
        width: 94px;
        height: 24px;
        bottom: 0;
        left: -1px;
        background-color: rgba(#686868, .6);
        border-radius: 0px 0px 0px 2px;
        line-height: 24px;
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #fff;
        font-size: 12px;
      }

      margin: 0 6px 6px 0;

      .el-icon-remove {
        color: red;
        position: absolute;
        top: 4px;
        right: 4px;
        font-size: 14px;
      }
    }

    .no-data {
      margin-right: 10px;
    }

    .no-data-img {
      width: 153.3px;
      height: 100px;
      // margin-bottom: 10px;
      margin-right: 8px;
      border: 1px solid #e2eaf5;
      text-align: center;
    }

    .file-item-add {
      width: 94px;
      height: 104px;
      background: #f6faff;
      border: 1px solid #dce6f3;
      border-radius: 2px;
      font-size: 30px;
      color: #dce6f3;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
}
</style>
