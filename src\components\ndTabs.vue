<template>
  <div class="nd-tabs-box">
    <el-tabs type="card" v-bind="$attrs" v-on="$listeners">
      <slot class="countent" />
    </el-tabs>
  </div>
</template>

<script>
export default {
};
</script>

<style lang='scss' scoped>
.nd-tabs-box {
  ::v-deep .el-tabs__header {
    height: 35px;
    background-image: linear-gradient(#fff, #e9f4fe);
  }
  ::v-deep .el-tabs__item.is-active {
    background: #fff;
    font-weight: 700;
    height: 29px;
    margin-top: 6px;
  }
  ::v-deep .el-tabs__item {
    height: 29px;
    line-height: 29px;
    color: #0098ff;
    background: #e9f4fe;
    margin-top: 6px !important;
    border: 1px solid #e2eaf5;
    margin: 0 4px 0 4px;
    padding: 0px 8px !important;
  }
  ::v-deep .el-tabs__nav {
    border: none;
  }
  ::v-deep .el-tabs__nav:first-child {
    margin-left: 4px;
  }
}
</style>