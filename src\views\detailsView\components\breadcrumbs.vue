<!--
 * @Author: smq2717480207 <EMAIL>
 * @Date: 2023-11-20 14:10:54
 * @LastEditors: smq2717480207 <EMAIL>
 * @LastEditTime: 2023-12-05 10:54:12
 * @FilePath: \gx-wz-vue\src\views\detailsView\components\breadcrumbs.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="breadcrumbs">
    <div class="icon"><i class="el-icon-location-outline" /></div>
    <div v-for="(item, index) in text" :key="index">
      <span v-if="!(index == text.length - 1) || (text.length == 1)" style="cursor: pointer" @click="toRoute(item.route,item.query)">{{ item.name }}</span>
      <span v-if="!(index == text.length - 1) || (text.length == 1)">&nbsp;>&nbsp;</span>
      <span v-if="index == text.length - 1" style="color: #ED911F">项目详情</span>
    </div>
  </div>
</template>

<script>
export default {
  mounted() {},
  watch: {
    $route: {
      immediate: true,
      deep: true,
      handler() {
        // console.log(this.$route, 66666666666);
        this.text = JSON.parse(this.$route.query.route);
        // console.log(this.text);
      },
    },
  },
  data() {
    return {
      text: ['交易公告', '项目详情'],
    };
  },
  methods:{
    toRoute(route,query){
      this.$router.push({
        path:route,
        query
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.breadcrumbs {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;

  .icon {
    color: #ED911F;
    margin-right: 10px;
  }
}
</style>