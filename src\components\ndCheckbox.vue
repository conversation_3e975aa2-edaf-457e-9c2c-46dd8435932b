<template>
  <div class="nd-checkbox-box" :style="{ width: width }">
    <el-checkbox v-bind="$attrs" v-on="$listeners">
      <slot />
    </el-checkbox>
  </div>
</template>
<script>
export default {
  props: {
    width: {
      type: String,
      default: "220px",
    },
  },
  data() {
    return {
    };
  },
  mounted() {
  },
  methods: {
  },
};
</script>
<style lang="scss" scoped>
.nd-checkbox-box {
  ::v-deep .el-checkbox__label {
    padding-left: 6px;
    font-size: 12px;
  }

  ::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #555555;
  }

  ::v-deep .el-checkbox {
    .el-checkbox__input {
      .el-checkbox__inner {
        border: 1px solid #0098ff;
      }
    }

    .is-disabled {
      .el-checkbox__inner {
        border: 1px solid #DCDFE6;
      }

    }
  }
}</style>
