<!-- 政策法规列表 -->
<template>
  <div class="wrap-container">
    <div style="position: sticky; top: 0; z-index: 9">
      <tabs v-model="currentTab" :tab-names="tabNames" />
    </div>
    <!-- 国家政策 -->
    <list v-show="currentTab == 0" requestUrl="/zcfg/gjzc/"></list>
    <!-- 地方政策  -->
    <list v-show="currentTab == 1" requestUrl="/zcfg/dfzc/"></list>
    <!-- 交易规则-->
    <!-- <list v-show="currentTab == 2" requestUrl="/zcfg/jygz/"></list> -->
    <!-- 交易指南  -->
    <!-- <list v-show="currentTab == 3" requestUrl="/zcfg/jyzn/"></list> -->
  </div>
</template>
<script>
import tabs from '@/views/newsListView/components/tabs.vue';
import list from '@/views/newsListView/components/list.vue';
export default {
  components: {
    tabs,
    list,
  },
  data() {
    return {
      currentTab: 0,
      tabNames: [
        { name: '国家政策' },
        { name: '地方政策' },
        // { name: '交易规则' },
        // { name: '交易指南' },
      ],
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.wrap-container {
  min-width: 1000px;
  width: 100%;
  overflow: auto;
  height: 100%;
  background: #fff;
  position: relative;
  padding-bottom: 20px;
}
</style>
