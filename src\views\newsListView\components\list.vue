<template>
  <div class="list-content">
    <div
      class="list-item"
      @click.stop="handleMore(item)"
      v-for="(item, index) in pageData"
      :key="index"
      :style="index == pageData.length - 1 ? 'border-bottom:none' : ''"
    >
      <div class="date">
        <span class="month"> {{ getDate(item.time) || 0 }} </span>
        <span class="year"> {{ getYear(item.time) || 0 }} </span>
      </div>
      <div class="line"></div>
      <div class="content">
        <div class="title" @click.stop="handleMore(item)" :title="item.title">{{ item.title }}</div>
        <div class="mainxz">{{ item.zy }}</div>
        <div class="more" v-if="showDownLoad">
          <a @click.stop="console.log()" download :href="item.ljxz" style="text-decoration: none; color: inherit">下载文本</a>
        </div>
        <div class="more" v-else @click.stop="handleMore(item)">
          查看详情
          <div class="arrow-right" />
          <div class="arrow-right" />
        </div>
      </div>
      <div class="img" v-if="item.img || item.imgUrl">
        <img :src="item.img || item.imgUrl" />
      </div>
    </div>
    <!-- 分页器 -->
    <div class="pagination">
      <nd-pagination
        :page-size="pager.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="pager.total"
        :total-page="totalPage"
        :current-page="pager.pageNo"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
const bannerType = {
  // newsListView: 3,
  projectRecommendations: 4,
  regulationsListView: 3,
  projectInformationView: 1,
  helpCenter: 2,
};
import ndPagination from '@/components/ndPagination.vue';
export default {
  name: 'list',
  components: {
    ndPagination,
  },
  props: {
    // showDownLoad: {
    //   type: Boolean,
    //   default: false,
    // },
    requestUrl: {
      type: String,
      default: '',
    },
    breadcrumbName: {
      type: String,
      default: '资讯详情',
    }
  },
  data() {
    return {
      showDownLoad: false,
      listData: [],
      pager: {
        pageNo: 1, // 当前页数
        pageSize: 9, // 当前页条数
        total: 0, // 总条目数
        pageSizes: [10, 30, 50, 100],
      },
      totalPage: 1,
    };
  },
  // 前端自行分页数据
  computed: {
    pageData() {
      return this.listData.slice(
        (this.pager.pageNo - 1) * this.pager.pageSize,
        this.pager.pageNo * this.pager.pageSize,
      );
    },
  },
  mounted() {
    // console.log()
    this.getData();

    if ('showDownLoad' in this.$attrs && this.$attrs.showDownLoad !== false)
      this.showDownLoad = true;
  },
  methods: {
    getData() {
      this.$ajax({
        url: this.$props.requestUrl,
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        if (res.status === 200) {
          try {
            this.listData = eval(res.data);
            this.pager.total = this.listData.length;
            this.totalPage = Math.ceil(this.listData.length / this.pager.pageSize);
          } catch (error) {
            console.error('数据格式有误');
          }
        }
      });
    },

    // 跳转详情
    handleMore(item) {
      this.$router.push({
        path: 'listDetailView',
        query: {
          path: item.path,
          // preInfo: JSON.stringify({ name: this.$route.meta.pageName, path: this.$route.name }),
          bannerType: bannerType[this.$route.name],
          breadcrumbName:this.$props.breadcrumbName
        },
      });
    },

    //  分页器函数
    handleCurrentChange(e) {
      this.pager.pageNo = e;
    },
    handleSizeChange(e) {
      this.pager.pageSize = e;
      this.pager.pageNo = 1;
    },

    // 获取年份
    getYear(time) {
      if (!time) return '';
      return new Date(time).getFullYear();
    },
    // 获取月份日期
    getDate(time) {
      if (!time) return '';
      let month = new Date(time).getMonth() + 1 + '';
      month.padStart(2, '0');
      let day = new Date(time).getDate() + '';
      day.padStart(2, '0');
      return month + '-' + day;
    },
  },
};
</script>
<style>
.list-item:last-child {
  border-bottom: 0 !important;
}
</style>
<style lang="scss" scoped>
.list-content {
  width: 1200px;
  margin: 0 auto;
  // min-height: 300px;
  .list-item {
    max-height: 220px;
    min-height: 180px;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    box-sizing: border-box;
    padding: 30px 0;
    cursor: pointer;

    .date {
      width: 100px;
      color: #999999;
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      .month {
        font-size: 26px;
      }
      .year {
        font-size: 14px;
        border: 1px solid #999999;
        width: 46px;
        text-align: center;
      }
    }
    .line {
      width: 36px;
      height: 2px;
      background: #c6c6c6;
      position: relative;
      top: 15px;
      flex-shrink: 0;
    }
    .content {
      flex: 1;
      width: 0;
      padding: 0 30px;
      padding-right: 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      //   padding-bottom: 30px;
      &:hover .title {
        color: #10AEC2 !important;
      }
      .title {
        flex-grow: 0;
        width: 100%;
        font-size: 20px;
        font-weight: bold;
        color: #333333;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .mainxz {
        line-height: 2;
        font-size: 16px;
        font-weight: 400;
        color: #666666;
        overflow: hidden;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        flex: 1;
        padding-top: 10px;
      }
      .more {
        width: 100px;
        font-size: 16px;
        font-weight: 400;
        color: #10AEC2;
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-top: 10px;
      }
    }
    .img {
      img {
        width: 242px;
        height: 126px;
        border-radius: 5px;
        object-fit: cover;
      }
    }
  }

  // .list-item
}
// 右箭头
.arrow-right {
  width: 7px;
  height: 7px;
  border-top: 1px solid #10AEC2;
  border-right: 1px solid #10AEC2;
  transform: rotate(45deg);
  position: relative;
  top: 1px;
  left: 4px;
}

.pagination {
  width: 100%;
  height: 50px;
}
</style>
