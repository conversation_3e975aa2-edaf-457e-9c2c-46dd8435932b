/**
 * 获取选中节点的level = level 的id
 * @param {array} treeData 地区树数据
 * @param {object} checked 选中的数据
 * @param {number} level 节点的level
 * @param {object} checkedKeys 选中的key
 */
function getIdByLevel(treeData, checked, level, checkedKeys) {
  // 判断是否有满足要求的level
  if (!hasLevel(treeData, level)) return;
  // 先判断选中的节点中是否有满足要求的id
  for (let i = 0; i < checkedKeys.checkedNodes.length; i++) {
    if (checkedKeys.checkedNodes[i].level === level) {
      return checkedKeys.checkedNodes[i].id;
    }
  }
  // 再查看半选节点中是否有满足的id
  for (let i = 0; i < checkedKeys.halfCheckedNodes.length; i++) {
    if (checkedKeys.halfCheckedNodes[i].level === level) {
      return checkedKeys.halfCheckedNodes[i].id;
    }
  }
  // 就是没选择
  return null;
}

/**
 * 动态禁用复选框
 * @param {array} treeData 数据节点
 * @param {string} id 禁用不包含的id
 */
function desabledById(treeData, id) {
  for (let i = 0; i < treeData.length; i++) {
    console.log(treeData[i]);
    if (treeData[i].newAllPath.indexOf(id) !== -1 || !id) {
      treeData[i].disabled = false;
    } else {
      treeData[i].disabled = true;
    }
    if (treeData[i].level === 1) {
      treeData[i].disabled = true;
    }
    if (treeData[i].children && treeData[i].children.length) {
      desabledById(treeData[i].children, id);
    }
  }
}

/**
 * 判断数据是否有满足需求的level
 * @param {object} treeData
 * @param {number} level
 * @returns {boolean}
 */
function hasLevel(treeData, level) {
  for (let i = 0; i < treeData.length; i++) {
    if (treeData[i].level <= level) {
      return true;
    }
    if (treeData[i].children && treeData[i].children.length > 0) {
      return hasLevel(treeData[i].children, level);
    }
  }
}

/**
 * 根据id 递归拼接数据
 * @param {array} treeData 地区树数据
 * @param {string} id 插入点的地区id
 * @param {array} data 插入的数据
 */
function createdTreeDataById(treeData, id, data) {
  // 判断是否有数据
  if (treeData && treeData.length === 0) {
    treeData = _addAllPath(data);
    return;
  }
  for (let i = 0; i < treeData.length; i++) {
    if (treeData[i].id === id) {
      treeData[i].children = _addAllPath(data, treeData[i]['newAllPath'], treeData[i]);
      return;
    }
    if (treeData[i].children && treeData[i].children.length > 0) {
      createdTreeDataById(treeData[i].children, id, data);
    }
  }
}

/**
 * 对数据添加allPath
 * @param {array} data 传入的数据
 * @param {string} parentAllPath |分割的父级路径
 * @return {array} 处理好的数据
 */
function _addAllPath(data, parentAllPath = '', parent) {
  return data.map((item) => {
    item['newAllPath'] = parentAllPath + '|' + item.id;
    // 继承父的disabled属性
    if (parent && parent.level > 1) {
      item['disabled'] = parent.disabled;
    } else {
      if (item.level < 1) {
        item['disabled'] = true;
      } else {
        item['disabled'] = false;
      }
    }

    return item;
  });
}

export { createdTreeDataById, getIdByLevel, desabledById };
