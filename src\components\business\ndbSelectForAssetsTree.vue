<template>
  <div class="ndb-select-assets-tree">
    <nd-select id="wbNavUsSelect" v-model="assetsValue" placeholder="全部" :width="width" @focus="stateChange">
      <el-option :value="assetsValue" :label="assetsValue">
        <el-tree id="tree-option" ref="selectAssetsTree" :data="treeData" highlight-current :props="defaultProps"
          node-key="id" show-checkbox :default-expanded-keys="treeExpandIdList" :default-checked-keys="treeCheckedIdList"
          @check="treeClickChoose" />
      </el-option>
    </nd-select>
  </div>
</template>

<script>
import ndSelect from "@/components/ndSelect.vue";
export default {
  components: {
    ndSelect,
  },
  props: {
    width: {
      type: String,
      default: "220px",
    },
    assetsNature: {
      type: String,
      default: "null",
    }
  },
  data() {
    return {
      treeData: [],
      treeExpandIdList: [],
      selectValueId: [],
      treeCheckedIdList: [],
      assetsValue: "",
      selectValue: [],
      defaultProps: {
        label: "name",
        children: "children",
      },
    };
  },
  watch: {

  },

  mounted() {
    //初始化el-select IE下光标闪现的问题
    this.stateChange();
    this.getTreeData();
    //初始化滚动条
    let scrollWrap = document.querySelectorAll(
      ".el-scrollbar .el-select-dropdown__wrap"
    )[0];
    if (scrollWrap) {
      scrollWrap.style.cssText =
        "margin: 0px; max-height: none; overflow: hidden;";
    }
    let scrollBar = document.querySelectorAll(
      ".el-scrollbar .el-scrollbar__bar"
    );
    scrollBar.forEach((ele) => (ele.style.width = 0));
  },

  methods: {
    //【jsp】获得服务器路径
    getServerPath() {
      if (typeof getContextPath === "function") {
        console.log("获得服务器路径:" + getContextPath());
        return getContextPath();
      } else {
        return "/sz_product_new";
      }
    },
    //获取树数据
    getTreeData() {
      this.$ajax({
        method: "post",
        url: "/zichancx.do?method=assetsTypeList",
        data: { assetsProperty: this.assetsNature }
      }).then((res) => {
        console.log(res);
        this.treeData = []
        this.assetsValue = "";
        console.log(this.assetsNature, "this.assetsNature");
        if (this.assetsNature === '1') {
          this.treeData.push(res.data.data.offOperationRoot);
        } else if (this.assetsNature === '0') {
          this.treeData.push(res.data.data.operationRoot);
        } else {
          this.treeData.push(res.data.data.offOperationRoot);
          this.treeData.push(res.data.data.operationRoot);
        }

        this.treeExpandIdList = res.data.data.baseCodeIdList;
        res.data.data.baseCodeIdList.forEach(el => {
          this.selectValueId.push({
            id: el
          })
        })
        this.treeCheckedIdList = res.data.data.baseCodeIdList;
        this.$nextTick(() => {
          this.$refs.selectAssetsTree.setCheckedKeys(this.treeCheckedIdList);
        });
        this.$emit("func", this.selectValueId);
      });
    },
    //获取选中的值
    treeClickChoose(nodeObj, SelectedObj) {
      console.log(SelectedObj);
      this.selectValue = [];
      this.selectValue = SelectedObj.checkedNodes;
      var assetsvalue = "";
      for (let i = 0; i < this.selectValue.length; i++) {
        if (this.selectValue[i].children == null) {
          assetsvalue = this.selectValue[i].name;
          break;
        }
      }
      if (assetsvalue != "") {
        this.assetsValue = assetsvalue + "...";
      } else {
        this.assetsValue = ""
      }
      this.$emit("func", this.selectValue);
    },

    //禁止select在IE下，光标闪现
    stateChange() {
      // 处理在ie浏览器出现光标的问题
      const elem = document.getElementById("wbNavUsSelect");
      if (elem.hasAttribute("unselectable") === false) {
        elem.setAttribute("unselectable", "on");
        elem.setAttribute("onfocus", "this.blur()");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/*滚轮样式*/
*::-webkit-scrollbar {
  width: 10px;
  height: 10px !important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}

/*滚轮样式*/
*::-webkit-scrollbar {
  width: 10px;
  height: 10px !important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}

/*滚轮样式*/

.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 230px;
  padding: 0;
  overflow: hidden;
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

ul li>>>.el-tree .el-tree-node__content:hover {
  background-color: #e8f7ff;
}

ul li>>>.el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
  background-color: #ffffff;
}

.el-tree-node__label {
  font-weight: normal;
  margin-right: 10px;
  font-size: 12px;
}

.el-tree .is-current .el-tree-node__label {
  color: #409eff;
  font-weight: 700;
}

.el-tree .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}

.el-select-dropdown__item.hover {
  background: transparent;
}

.el-select-dropdown__item span {
  margin-left: 10px;
}

::v-deep .el-scrollbar__bar.is-horizontal {
  height: 0px;
}

::v-deep .el-scrollbar__bar.is-vertical {
  width: 0px;
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #c8ecff;
}
</style>