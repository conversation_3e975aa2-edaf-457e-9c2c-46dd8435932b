<template>
  <div class="ndb-pdf-ie-box">
    <iframe :src="src" class="pdf-viewer" />
  </div>
</template>
<script>
export default {
  props: {
    url: {
      type: String,
      default: "",
    }
  },
  data() {
    return {
      src: "pdfViewer.html?url=", // iframe的地址
    };
  },
  watch: {
    url: {
      // immediate: true,
      handler(value) {
        this.src = this.src + encodeURI(value);
      },
    },
  },
  mounted() {

  },
  methods: {

  }
};
</script>
<style lang="scss" scoped>
.ndb-pdf-ie-box {
  width: 100%;
  height: 100%;
  .pdf-viewer {
    width: 100%;
    height: 100%;
  }
}
</style>