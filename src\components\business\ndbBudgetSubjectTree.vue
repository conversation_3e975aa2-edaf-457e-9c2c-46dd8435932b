<template>
  <div class="ndb-select-organize-tree-box">
    <nd-select
      id="wbJspTreeSelect"
      ref="ndSelect"
      v-bind="$attrs"
      :filterable="true"
      :remote="true"
      :remote-method="assetsTypeFilter"
      :value="nodeName"
      :placeholder="placeholder || '请选择'"
      :width="width"
      v-on="$listeners"
    >
      <el-option v-loading="loading" :value="nodeName" :label="nodeName">
        <el-tree
          id="tree-option"
          ref="tree"
          highlight-current
          :data="listArry"
          :props="defaultProps"
          node-key="id"
          :filter-node-method="filterNode"
          :default-expand-all="true"
          @node-click="handleNodeClick"
        />
      </el-option>
    </nd-select>
  </div>
</template>
<script>
import ndSelect from "@/components/ndSelect.vue";
export default {
  components: {
    ndSelect,
  },
  // model: {
  //   prop: "deptIdNum",
  //   event: "getDep",
  // },
  model: {
    prop: "budgetVal",
    event: "getDep",
  },
  props: {
    budgetVal: {
      // type: String | Array,
      type: [Number, String, Object],
      default: null,
    },
    // 宽度
    width: {
      type: String,
      default: "220px",
    },
    deptId: {
      type: [String, Array],
      default: null,
    },
    applyDate: {
      type: String,
      default: "",
    },
    nodeNameInfo: {
      type: String,
      default: "",
    },
    budgetKemuID:{
      type: String,
      default: "",
    },
  },
  data() {
    return {
      placeholder: "",
      defaultProps: {
        id: "id",
        label: "name",
        children: "children"
      },
      listArry: [],
      nodeName: "",
      loading: false,
    };
  },
  watch: {
    'budgetVal.kmName': {
      immediate: true,
      deep: true,
      handler(value) {
        if (value != '' && value != undefined) {
          this.nodeName = value;
        }
      }
    },
    'nodeNameInfo': {
      deep: true,
      handler(val) {
        this.$nextTick(() => {
          this.nodeName = val;
        })
      }
    },
  },
  mounted() {
    // this.budgeReload();
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    assetsTypeFilter(val) {
      this.$refs.tree.filter(val)
    },
    // 重新加载数据
    budgeReload() {
      this.$nextTick(() => {
        this.getList();
      });
    },
    //获取预算科目树信息
    getList() {
      this.listArry = [];
      this.loading = true;
      let isBatch = '0';
      if(this.budgetKemuID != ''){
        isBatch = '1';
      }
      let params = {
        budgetName: '',
        deptId: this.deptId,
        applyDate: this.applyDate,
        isBatch: isBatch,
      };
      this.$ajax({
        url: "" + "/useMoneyApplyNew/queryBudgetkemuInfo.do",
        method: "post",
        data: params,
      })
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              this.listArry = res.data.data;
            }
          } else {
            if (res.data.code != 9999) {
              this.$message.error(res.data.msg);
            }
          }
          this.loading = false;
        })
    },
    // 节点点击
    handleNodeClick(node) {
      if (node.haschild == 0) {
        this.nodeName = node.name;
        this.$emit('getDep', node)
        // this.$emit("nodeClick", node);
        this.$refs.ndSelect.$refs.select.blur();
      } else {
        this.$message.error("请选中末级预算项！");
        return false;
      }
    },
    //清空数据
    clearList(){
      this.listArry = [];
    },
    //清除名字
    clearName(){
      this.nodeName =  '';
    },
  },
};
</script>

<style scoped>
*::-webkit-scrollbar {
  width: 10px;
  height: 10px !important;
  width: 6px;
  height: 12px !important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}

.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 230px;
  /* padding: 0; */
  padding: 0 10px;
  overflow: hidden;
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

ul li>>>.el-tree .el-tree-node__content:hover {
  background-color: #e8f7ff;
}

ul li>>>.el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
  background-color: #ffffff;
}

.el-tree-node__label {
  font-weight: normal;
  margin-right: 10px;
  font-size: 12px;
}

.el-tree .is-current .el-tree-node__label {
  color: #409eff;
  font-weight: 700;
}

.el-tree .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}

.el-select-dropdown__item.hover {
  background: transparent;
}

.el-select-dropdown__item span {
  margin-left: 10px;
}

::v-deep .el-scrollbar__bar.is-horizontal {
  height: 0px;
}

::v-deep .el-scrollbar__bar.is-vertical {
  width: 0px;
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #c8ecff;
}

.el-select-dropdown__item span {
  margin-left: 0px;
}
</style>
