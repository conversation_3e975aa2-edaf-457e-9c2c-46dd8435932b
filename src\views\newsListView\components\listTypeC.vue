<template>
  <div class="main-cont">
    <div v-if="pageData.length !== 0" class="list-content">
      <div class="list-item" @click.stop="handleMore(item)" v-for="(item, index) in pageData" :key="index">
        <div class="item-top">
          <div class="title">
            {{ item.title }}
          </div>
          <div class="time">
            <div>{{ item.time }}</div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="list-content">
      <empty :boxHeight="300"></empty>
    </div>
    <!-- 分页器 -->
    <div class="pagination">
      <nd-pagination :page-size="pager.pageSize" layout="total, prev, pager, next, jumper" :total="pager.total"
        :total-page="totalPage" :current-page="pager.pageNo" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
      <div class="current-total">
        当前页共{{ pageData.length }}条
      </div>
    </div>
  </div>
</template>

<script>
// const bannerType = {
//   newsListView: 3,
//   projectRecommendations: 4,
//   regulationsListView: 3,
//   projectInformationView: 1,
//   helpCenter: 2,
// };
import ndPagination from '@/components/ndPagination.vue';
import empty from '@/views/empty/index.vue';

export default {
  name: 'list',
  components: {
    ndPagination,
    empty,
  },
  props: {
    showTab: {
      type: String,
      default: '1',
    },
    requestUrl: {
      type: String,
      default: '',
    },
    breadcrumbName: {
      type: String,
      default: '资讯详情',
    },
  },
  data() {
    return {
      showDownLoad: false,
      listData: [],
      pager: {
        pageNo: 1, // 当前页数
        pageSize: 20, // 当前页条数
        total: 0, // 总条目数
        pageSizes: [10, 30, 50, 100],
      },
      totalPage: 1,
    };
  },
  watch: {
    //监听info对象
    requestUrl: {
      handler(newVal, oldVal) {
        //监听info对象变化
        console.log("newVal", newVal)
        this.pager.pageNo = 1
        this.getData(newVal)
      },
      deep: true, //深度监听
    }
  },
  // 前端自行分页数据
  computed: {
    pageData() {
      return this.listData.slice(
        (this.pager.pageNo - 1) * this.pager.pageSize,
        this.pager.pageNo * this.pager.pageSize,
      );
    },
  },
  // activated() {
  //   this.pager.pageNo = 1
  //   this.getData();
  // },
  mounted() {
    // console.log()
    this.pager.pageNo = 1
    this.getData();

    if ('showDownLoad' in this.$attrs && this.$attrs.showDownLoad !== false)
      this.showDownLoad = true;
  },
  methods: {
    getData() {
      this.$ajax({
        url: this.$props.requestUrl,
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        if (res.status === 200) {
          try {
            this.listData = eval(res.data);
            this.pager.total = this.listData.length;
            this.totalPage = Math.ceil(this.listData.length / this.pager.pageSize);
          } catch (error) {
            console.error('数据格式有误');
          }
        }
      });
    },

    // 跳转详情
    handleMore(item) {
      this.$router.push({
        path: 'listDetailView',
        query: {
          path: item.path,
          // preInfo: JSON.stringify({ name: this.$route.meta.pageName, path: this.$route.name }),
          bannerType: this.$props.showTab, // bannerType[this.$route.name],
          breadcrumbName: this.$props.breadcrumbName,
        },
      });
    },

    //  分页器函数
    handleCurrentChange(e) {
      this.pager.pageNo = e;
    },
    handleSizeChange(e) {
      this.pager.pageSize = e;
      this.pager.pageNo = 1;
    },

    // 获取年份
    getYear(time) {
      if (!time) return '';
      return new Date(time).getFullYear();
    },
    // 获取月份日期
    getDate(time) {
      if (!time) return '';
      let month = new Date(time).getMonth() + 1 + '';
      month.padStart(2, '0');
      let day = new Date(time).getDate() + '';
      day.padStart(2, '0');
      return month + '-' + day;
    },
  },
};
</script>
<style lang="scss" scoped>
.main-cont {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 400px;
}

.list-content {
  width: 1300px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e5e5;
  margin-top: 21px;
  padding: 20px 0px 24px 24px;

  .list-item {
    cursor: pointer;
    padding-left: 24px;
    margin-right: 20px;
    border-bottom: 1px dashed #E5E5E5;

    // &:hover {
    //   box-shadow: 0px 0px 10px 0px rgba(32, 32, 32, 0.1);
    // }
  }

  .item-top {  
    display: flex;  
    flex-direction: row;  
    align-items: center;  
    justify-content: space-between;  
    // margin-bottom: 19px;  

    .title {  
        font-family: Microsoft YaHei;  
        font-weight: 400;  
        font-size: 14px;  
        color: #333333;  
        line-height: 50px;  
        position: relative;  
        z-index: 1;  
    }  

    .title:before {  
        content: '•';  
        color: black;  
        margin-right: 0.5em;  
        position: absolute;  
        left: -1em;  
        z-index: -1;  
        transition: color 0.3s;  
    }  

    .title:hover:before {  
        color: #ed911f;  
    }  

    .time {  
        font-family: Microsoft YaHei;  
        font-weight: 400;  
        font-size: 14px;  
        color: #333333;  
        line-height: 40px;  
        transition: color 0.3s;
    }  

    .time:hover {  
        color: #ed911f; 
    }  

    &:hover {  
        border: none;  

        .title {  
            font-size: 14px;  
            color: #ed911f;  
            text-decoration: underline;  
        }  

        .time {  
            font-size: 14px;  
            color: #ed911f;  
            text-decoration: none;  
        }  

        .title:before {  
            color: #ed911f;
        }  
    }  
}

  .item-bottom:not(:last-child) {
    margin-bottom: 20px;
  }
}

.pagination {
  width: 100%;
  margin-top: 14px;
  display: flex;
  align-items: center;
  justify-content: center;

  .current-total {
    font-weight: 400;
    color: #606266;
    font-size: 13px;
  }

  ::v-deep .nd-pagination-box {
    width: auto;
    margin-right: 10px;
  }

  :v-deep .el-pagination button:disabled {
    background: none;
  }

  ::v-deep .el-pagination .btn-next,
  .el-pagination .btn-prev {
    background: none;
  }

  ::v-deep .el-pagination .btn-prev:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pagination .btn-next:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pager li {
    background: none;
  }

  ::v-deep .el-pager li.active {
    color: #fff;
    background: #f2ae0f;
  }

  ::v-deep .el-pager li.active:hover {
    color: #fff;
  }

  ::v-deep .el-pager li:hover {
    color: #f2ae0f;
  }
}
</style>