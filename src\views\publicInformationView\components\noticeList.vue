<!-- 政策法规列表 -->
<template>
  <div class="wrap-container">
    <!-- <div style="position: sticky; top: 0; z-index: 9">
        <tabs v-model="currentTab" :tab-names="tabNames" />
      </div> -->
    <!-- 注销类 -->
    <listTypeA v-if="currentTab == '1'" requestUrl="/ForensicsNotice/getList" :breadcrumbName="'鉴证公告'" :keyWords="keyWords">
    </listTypeA>
    <!-- 遗失类  -->
    <listTypeB v-if="currentTab == '2'" requestUrl="/ForensicsNotice/getList" :breadcrumbName="'鉴证公告'" :keyWords="keyWords">
    </listTypeB>
    <!-- <list v-show="currentTab == 1" requestUrl="/zcfg/dfzc/"></list> -->
    <!-- 变更类-->
    <listTypeC v-if="currentTab == '3'" requestUrl="/ForensicsNotice/getList" :breadcrumbName="'鉴证公告'" :keyWords="keyWords">
    </listTypeC>
    <!-- <list v-show="currentTab == 2" requestUrl="/zcfg/jygz/"></list> -->
    <!-- 交易指南  -->
    <!-- <list v-show="currentTab == 3" requestUrl="/zcfg/jyzn/"></list> -->
  </div>
</template>
<script>
import tabs from '@/views/newsListView/components/tabs.vue';
import listTypeA from './listTypeA.vue';
import listTypeB from './listTypeB.vue';
import listTypeC from './listTypeC.vue';
export default {
  props: {
    currentTab: {
      type: String,
      default: '1',
    },
    keyWords:{
      type:String,
      default:""
    }
  },
  components: {
    tabs,
    listTypeA,
    listTypeB,
    listTypeC,
  },
  data() {
    return {
      tabNames: [
        // { name: '国家政策' },
        // { name: '地方政策' },
        // { name: '交易规则' },
        // { name: '交易指南' },
      ],
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.wrap-container {
  min-width: 1000px;
  width: 100%;
  overflow: auto;
  height: 100%;
  background: #fff;
  position: relative;
  padding-bottom: 20px;
}
</style>