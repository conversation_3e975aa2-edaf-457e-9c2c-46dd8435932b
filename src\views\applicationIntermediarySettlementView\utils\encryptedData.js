import CryptoJS from 'crypto-js';
import axios from 'axios';
// import { ElMessage } from "element-plus";
/**
 * AES加密 单数据加密
 * --方法需要异转同！！！！！
 * @param {String} plaintext 要加密的明文
 * @returns {obj} {encrypted:密文,redisKey:传给后端的参}
 */
export async function encryptByAES(plaintext) {
  return await axios({
    url: '/baseInfo/getFrontendKey',
    method: 'GET',
  })
    .then((r) => {
      if (r.data.code === 200) {
        //进行加密
        let key = CryptoJS.enc.Base64.parse(r.data.data.key);
        let encrypted = CryptoJS.AES.encrypt(plaintext, key, {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7,
        });
        return {
          encrypted: encodeURIComponent(encrypted.ciphertext.toString(CryptoJS.enc.Base64)),
          redisKey: r.data.data.redisKey,
        };
      } else {
        // ElMessage.error(r.data.msg)
      }
    })
    .catch(() => {
      // ElMessage.error("网络连接失败！")
    });
}

/**
 * AES加密 多数据加密
 * --方法需要异转同！！！！！
 * @param {Array} plaintexts [{name:"键名",value:"明文"}]
 * @returns {obj} {...name:密文,redisKey:传给后端的参}
 */
export async function encryptByAESarr(plaintexts) {
  return await axios({
    url: '/baseInfo/getFrontendKey',
    method: 'GET',
  })
    .then((r) => {
      if (r.data.code === 200) {
        let encryptedObj = {};
        plaintexts.forEach((item) => {
          let key = CryptoJS.enc.Base64.parse(r.data.data.key);
          let encrypted = CryptoJS.AES.encrypt(item.value, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          //   encryptedObj[item.name] = encodeURIComponent(
          //     encrypted.ciphertext.toString(CryptoJS.enc.Base64),
          //   );
          encryptedObj[item.name] = encrypted.ciphertext.toString(CryptoJS.enc.Base64);
        });
        encryptedObj.redisKey = r.data.data.redisKey;
        return encryptedObj;
      } else {
        // ElMessage.error(r.data.msg)
      }
    })
    .catch(() => {
      // ElMessage.error("网络连接失败！")
    });
}

/**
 * AES解密
 * 方法不用转同
 * @param {String} cipherText 密文
 * @return {String} 明文
 */
export async function decryptByAES(cipherText) {
  // let key = CryptoJS.enc.Base64.parse(keyInBase64Str);
  // // 返回的是一个Word Array Object，其实就是Java里的字节数组
  // let decrypted = CryptoJS.AES.decrypt(cipherText, key, {
  //     mode: CryptoJS.mode.ECB,
  //     padding: CryptoJS.pad.Pkcs7,
  // });
  return await axios({
    url: '/user/decrypt?encryptMessage=' + cipherText,
    method: 'GET',
  })
    .then((r) => {
      if (r.data.code === 0) {
        return r.data.data;
      } else {
        // ElMessage.error(r.data.msg)
      }
    })
    .catch(() => {
      // ElMessage.error("网络连接失败！")
    });
  // return decrypted.toString(CryptoJS.enc.Utf8);
}
