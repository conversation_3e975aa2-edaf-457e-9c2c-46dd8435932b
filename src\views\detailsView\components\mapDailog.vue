<template>
    <div class="dailog" id="dailog1" style="display: block" v-if="show">
      <div class="dailog-m"></div>
      <div class="dailog-box">
        <div class="haeader-dailog">
          <span>项目位置</span>
        </div>
        <div class="main-dailog">
          <div class="map" id="bdMap">
            <iframe style="width: 100%; height: 100%" :src="src" />
          </div>
        </div>
        <div class="footer-dailog">
          <div class="buttom" @click="close()">关闭</div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  import { BMapLoader } from '../utils/map';
  export default {
    data() {
      return {
        show: false,
        map: null,
        src: '',
      };
    },
    mounted() {
      // BMapLoader();
    },
    methods: {
      open({ lat, lng }) {
        this.show = true;
        //   this.initMpa({ lat, lng });
        this.src = window.ipConfig.mapUrl + '?lat=' + lat + '&lng=' + lng;
      },
      close() {
        this.show = false;
      },
      initMpa({ lat, lng }) {
        //   try {
        //     setTimeout(() => {
        //       //   this.map = new BMap.Map('bdMap');
        //       //   var point = new BMap.Point(Number(lng), Number(lat));
        //       //   this.map.centerAndZoom(point, 15);
        //       //   // var zoomCtrl = new BMap.ZoomControl();
        //       //   // this.map.addControl(zoomCtrl);
        //       //   this.map.enableScrollWheelZoom(true);
        //       //   var marker = new BMap.Marker(point);
        //       //   this.map.addOverlay(marker);
        //       //   console.log(this.map, 'this.map');
        //       const map = new BMap.Map('bdMap', { enableMapClick: false });
        //       map.disableKeyboard(); // 禁用键盘操作地图
        //       map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
        //       var zoomCtrl = new BMapGL.ZoomControl(); // 添加缩放控件
        //       map.addControl(zoomCtrl);
        //       console.log(val, 'id="bdMap"');
        //       if (val.lat != '' && val.lat != null && val.lng != '' && val.lng != null) {
        //         console.log(123456);
        //         // 添加中心点和缩放等级
        //         const point = new BMap.Point(val.lng, val.lat); //地图中心点坐标
        //         map.centerAndZoom(point, 15); //地图创建以及显示缩放等级
        //         let marker = new BMap.Marker(point);
        //         map.addOverlay(marker);
        //         var sContent = `<div style="margin:0;padding:0">
        //                                 <div style='min-width:260px;margin:0;font-size:13px'>项目编号: &nbsp;${val.proCode}</div>
        //                                 <div style='min-width:260px;margin:0;font-size:13px'>项目名称: &nbsp;${val.proName}</div>
        //                                 <div style='min-width:260px;margin:0;font-size:13px'>项目位置: &nbsp;${val.address}</div>
        //                                 <div style='min-width:260px;margin:0;font-size:13px'>发布时间: &nbsp;${val.publishTime}</div>
        //                                 <div style='min-width:260px;margin:0;font-size:13px'>报名截止时间: &nbsp;${val.signEndTime}</div>
        //                             </div>`;
        //         var infoWindow = new BMapGL.InfoWindow(sContent, {
        //           width: 'auto',
        //           height: 'auto',
        //         });
        //         // marker添加点击事件
        //         marker.addEventListener('click', function () {
        //           this.openInfoWindow(infoWindow);
        //         });
        //       } else {
        //         // 添加中心点和缩放等级
        //         const point = new BMap.Point(120.70648, 28.00109); //地图中心点坐标(温州)
        //         map.centerAndZoom(point, 12); //地图创建以及显示缩放等级
        //       }
        //     }, 1000);
        //   } catch {}
  
        BMapLoader().then((BMap) => {
          const map = new BMap.Map('bdMap', { enableMapClick: false });
          map.disableKeyboard(); // 禁用键盘操作地图
          map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
          var zoomCtrl = new BMapGL.ZoomControl(); // 添加缩放控件
          map.addControl(zoomCtrl);
          const point = new BMap.Point(lng, lat); //地图中心点坐标
          map.centerAndZoom(point, 15); //地图创建以及显示缩放等级
          let marker = new BMap.Marker(point);
          map.addOverlay(marker);
  
          // var sContent = `<div style="margin:0;padding:0">
          //                             <div style='min-width:260px;margin:0;font-size:13px'>项目编号: &nbsp;${val.proCode}</div>
          //                             <div style='min-width:260px;margin:0;font-size:13px'>项目名称: &nbsp;${val.proName}</div>
          //                             <div style='min-width:260px;margin:0;font-size:13px'>项目位置: &nbsp;${val.address}</div>
          //                             <div style='min-width:260px;margin:0;font-size:13px'>发布时间: &nbsp;${val.publishTime}</div>
          //                             <div style='min-width:260px;margin:0;font-size:13px'>报名截止时间: &nbsp;${val.signEndTime}</div>
          //                         </div>`;
          // var infoWindow = new BMapGL.InfoWindow(sContent, {
          //   width: 'auto',
          //   height: 'auto',
          // });
          // marker添加点击事件
          // marker.addEventListener('click', function () {
          //   this.openInfoWindow(infoWindow);
          // });
        });
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .dailog {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    display: none;
    z-index: 999999;
  }
  
  .dailog-m {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000000;
    background: rgba(0, 0, 0, 0.3);
    filter: alpha(opacity=30);
    /* opacity: 0.3; */
  }
  
  .dailog-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1000px;
    /* height: 642px; */
    background-color: #fff;
    border-radius: 4px;
  }
  
  .haeader-dailog {
    padding: 20px 20px 10px;
    color: #333;
    font-size: 18px;
    line-height: 24px;
  }
  
  .main-dailog {
    padding: 30px 20px;
    border-bottom: solid 0.5px #d1dbe5;
  
    .map {
      width: 100%;
      height: 400px;
    }
  }
  
  .main-dailog p {
    margin: 0;
    padding: 0;
    font-size: 14px;
    color: #606266;
  }
  
  .footer-dailog {
    padding: 10px 20px;
    overflow: hidden;
  }
  
  .footer-dailog .buttom {
    width: 60px;
    height: 34px;
    padding: 8px 15px;
    background-color: #fff;
    font-size: 14px;
    color: #606266;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    cursor: pointer;
    float: right;
  }
  
  .footer-dailog .buttom:hover {
    color: #409eff;
    border: 1px solid #c6e2ff;
    background-color: #ecf5ff;
  }
  </style>
  