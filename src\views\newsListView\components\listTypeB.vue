<template>
  <div class="main-cont">
    <div v-if="pageData.length !== 0" class="list-content">
      <div
        class="list-item"
        @click.stop="handleMore(item)"
        v-for="(item, index) in pageData"
        :key="index"
      >
        <div class="item-top">
          <img class="img1" src="@/assets/detail/dygg.png" alt="" />
          <div class="title" @click.stop="handleMore(item)" :title="item.title">
            {{ item.title }}
          </div>
        </div>
        <div class="item-bottom">
          <div class="left-item">
            <!-- <div class="item">
              <div>鉴证编号：</div>
              <div>20240006号</div>
            </div>
            <div class="item">
              <div>鉴证品种：</div>
              <div>变更类</div>
            </div> -->
            <div class="item">
              <div>发布时间：</div>
              <div>{{ item.time }}</div>
            </div>
          </div>
          <div class="right-item" @click.stop="handleMore(item)">
            <div class="text">查看详情</div>
            <i class="el-icon-right"></i>
          </div>
        </div>
        <div class="line" v-if="index !== pageData.length - 1"></div>
      </div>
    </div>
    <div v-else class="list-content">
      <empty :boxHeight="300"></empty>
    </div>
    <!-- 分页器 -->
    <div class="pagination">
      <nd-pagination
        :page-size="pager.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="pager.total"
        :total-page="totalPage"
        :current-page="pager.pageNo"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
    
<script>
import ndPagination from '@/components/ndPagination.vue';
import empty from '@/views/empty/index.vue';

export default {
  name: 'list',
  components: {
    ndPagination,
    empty,
  },
  props: {
    // showDownLoad: {
    //   type: Boolean,
    //   default: false,
    // },
    requestUrl: {
      type: String,
      default: '',
    },
    breadcrumbName: {
      type: String,
      default: '资讯详情',
    }
  },
  data() {
    return {
      showDownLoad: false,
      listData: [],
      pager: {
        pageNo: 1, // 当前页数
        pageSize: 10, // 当前页条数
        total: 0, // 总条目数
        pageSizes: [10, 30, 50, 100],
      },
      totalPage: 1,
    };
  },
  // 前端自行分页数据
  computed: {
    pageData() {
      return this.listData.slice(
        (this.pager.pageNo - 1) * this.pager.pageSize,
        this.pager.pageNo * this.pager.pageSize,
      );
    },
  },
  mounted() {
    // this.getData();

    if ('showDownLoad' in this.$attrs && this.$attrs.showDownLoad !== false)
      this.showDownLoad = true;
  },
  methods: {
    getData() {
      this.$ajax({
        url: this.$props.requestUrl,
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        if (res.status === 200) {
          try {
            this.listData = eval(res.data);
            this.pager.total = this.listData.length;
            this.totalPage = Math.ceil(this.listData.length / this.pager.pageSize);
          } catch (error) {
            console.error('数据格式有误');
          }
        }
      });
    },

    // 跳转详情
    handleMore(item) {
      this.$router.push({
        path: 'listDetailView',
        query: {
          path: item.path,
          // preInfo: JSON.stringify({ name: this.$route.meta.pageName, path: this.$route.name }),
          bannerType: '2', // bannerType[this.$route.name],
          breadcrumbName:this.$props.breadcrumbName
        },
      });
    },

    //  分页器函数
    handleCurrentChange(e) {
      this.pager.pageNo = e;
    },
    handleSizeChange(e) {
      this.pager.pageSize = e;
      this.pager.pageNo = 1;
    },

    // 获取年份
    getYear(time) {
      if (!time) return '';
      return new Date(time).getFullYear();
    },
    // 获取月份日期
    getDate(time) {
      if (!time) return '';
      let month = new Date(time).getMonth() + 1 + '';
      month.padStart(2, '0');
      let day = new Date(time).getDate() + '';
      day.padStart(2, '0');
      return month + '-' + day;
    },
  },
};
</script>
    <style lang="scss" scoped>
.main-cont {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}
.list-content {
  width: 1300px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e5e5;
  margin-top: 15px;
  padding: 26px 21px 17px 20px;

  .item-top {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 19px;

    .img1 {
      margin-right: 14px;
    }

    .title {
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      cursor: pointer;
    }
  }

  .item-bottom:not(:last-child) {
    margin-bottom: 20px;
  }

  .item-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;

    .left-item {
      display: flex;
      align-items: center;
      flex-direction: row;

      .item:nth-child(-n + 2) {
        margin-right: 70px;
      }

      .item {
        display: flex;
        align-items: center;
        flex-direction: row;
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
      }
    }

    .right-item {
      display: flex;
      align-items: center;
      flex-direction: row;
      color: #ed911f;
      cursor: pointer;

      .text {
        margin-right: 10px;
      }
    }
  }

  .line {
    width: 100%;
    height: 1px;
    background: #e5e5e5;
    margin-bottom: 21px;
  }
}

.pagination {
  width: 100%;
  margin-top: 14px;
  :v-deep .el-pagination button:disabled {
    background: none;
  }

  ::v-deep .el-pagination .btn-next,
  .el-pagination .btn-prev {
    background: none;
  }

  ::v-deep .el-pagination .btn-prev:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pagination .btn-next:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pager li {
    background: none;
  }

  ::v-deep .el-pager li.active {
    color: #fff;
    background: #f2ae0f;
  }

  ::v-deep .el-pager li.active:hover {
    color: #fff;
  }

  ::v-deep .el-pager li:hover {
    color: #f2ae0f;
  }
}
</style>
    