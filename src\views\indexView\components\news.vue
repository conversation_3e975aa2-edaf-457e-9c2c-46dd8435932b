<template>
  <div class="news">
    <div class="new-top">
      <div class="top-item">
        <div class="top-item-top bg1">
          <div class="top-item-img">
            <img src="@/assets/detail/jgzs.png" alt="" srcset="" />
          </div>
          <div class="top-item-text">查看更多>></div>
        </div>
        <div class="top-item-bot">
          <div class="list-item" v-for="(item, index) in newsList" :key="index">
            <div class="dot"></div>
            <div class="text">{{ item.content }}</div>
          </div>
        </div>
      </div>
      <div class="top-item">
        <div class="top-item-top bg2">
          <div class="top-item-img">
            <img src="@/assets/detail/jyzx.png" alt="" srcset="" />
          </div>
          <div class="top-item-text">查看更多>></div>
        </div>
        <div class="top-item-bot">
          <div class="list-item" v-for="(item, index) in newsList" :key="index">
            <div class="dot"></div>
            <div class="text">{{ item.content }}</div>
          </div>
        </div>
      </div>
      <div class="top-item">
        <div class="top-item-top bg3">
          <div class="top-item-img">
            <img src="@/assets/detail/jcpj.png" alt="" srcset="" />
          </div>
          <div class="top-item-text">查看更多>></div>
        </div>
        <div class="top-item-bot">
          <div class="list-item" v-for="(item, index) in newsList" :key="index">
            <div class="dot"></div>
            <div class="text">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="new-bot">
      <div class="bot-item">
        <div class="bot-item-top bg4">
          <div class="bot-item-img">
            <img src="@/assets/detail/gfbz.png" alt="" srcset="" />
          </div>
          <div class="bot-item-text">查看更多>></div>
        </div>
        <div class="bot-item-bot">
          <div class="list-item" v-for="(item, index) in newsArr" :key="index">
            <div class="list-item-left">
              <div class="dot"></div>
              <div class="text">{{ item.content }}</div>
            </div>
            <div class="list-item-right">
              {{ item.time }}
            </div>
          </div>
        </div>
      </div>
      <div class="bot-item">
        <div class="bot-item-top bg5">
          <div class="bot-item-img">
            <img src="@/assets/detail/zcfg.png" alt="" srcset="" />
          </div>
          <div class="bot-item-text">查看更多>></div>
        </div>
        <div class="bot-item-bot">
          <div class="list-item" v-for="(item, index) in newsArr" :key="index">
            <div class="list-item-left">
              <div class="dot"></div>
              <div class="text">{{ item.content }}</div>
            </div>
            <div class="list-item-right">
              {{ item.time }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import tab from './tabBar.vue';
export default {
  components: {
    tab,
  },
  watch: {
    tabActive: {
      handler() {},
    },
  },
  data() {
    return {
      tabActive: 0,
      newsList: [
        { content: '2023年四季度滁州市农村土地经营权流转交易价格指数' },
        { content: '2023年四季度滁州市农村土地经营权流转交易价格指数' },
        {
          content:
            '2023年四季度滁州市农村土地经营权流转交易价格指数农村产权流转交易服务通则国家标准',
        },
        { content: '2023年四季度滁州市农村土地经营权流转交易价格指数' },
        {
          content:
            '2023年四季度滁州市农村土地经营权流转交易价格指数农村产权流转交易服务通则国家标准',
        },
        { content: '2023年四季度滁州市农村土地经营权流转交易价格指数' },
      ],
      newsArr: [
        {
          content: '农村产权流转交易服务通则国家标准农村产权流转交易服务通则国家标准',
          time: '2023-07-23',
        },
        { content: '农村产权流转交易信息平台建设与维护国家标准', time: '2023-07-23' },
        {
          content:
            '农村产权流转交易服务术语和服务分类国家标准2023年四季度滁州市农村土地经营权流转交易价格指数',
          time: '2023-07-23',
        },
        { content: '滁州市农村土地经营权流转合同（样本）', time: '2023-07-23' },
        { content: '农村产权流转交易服务通则国家标准', time: '2023-07-23' },
        {
          content: '农村产权交易农村养殖水面承包经营权交易服务规范滁州市地方标准',
          time: '2023-07-23',
        },
      ],
    };
  },
  mounted() {},
  methods: {
    // 获取新闻数据
    getNewstrends() {
      let url = '';
      switch (this.tabActive) {
        case 0: {
          url = '/xwzx/xwdt/';
          break;
        }

        case 1: {
          url = '/xwzx/dfyw/';
          break;
        }

        case 2: {
          url = '/xwzx/zjgd/';
          break;
        }
      }
      this.$ajax({
        url: url,
        method: 'get',
        serverName: 'nd-ss',
      }).then((r) => {
        let newArr = eval(r.data);
        if (newArr.length > 5) {
          this.newsList = newArr.slice(0, 6);
        } else {
          this.newsList = newArr;
        }
      });
    },

    // 获取三张图的数据
    getImageData() {
      this.$ajax({
        url: '/xwzx/tpxw/',
        method: 'get',
        serverName: 'nd-ss',
      }).then((r) => {
        let newArr = eval(r.data);
        if (newArr.length > 3) {
          this.imgData = newArr.slice(0, 3);
        } else {
          this.imgData = newArr;
        }
      });
    },

    // 点击去往新闻详情
    toDetail(path) {
      this.$router.push({
        path: 'listDetailView',
        query: {
          path: path,
          preInfo: JSON.stringify({ name: this.$route.meta.pageName, path: this.$route.name }),
          bannerType: 1,
        },
      });
    },

    // 点击more
    toMore() {
      this.$router.push('newsListView');
    },
  },
};
</script>

<style lang="scss" scoped>
.news {
  width: 100%;
  background-color: #f7feff;
  padding: 40px 310px;
  margin-bottom: 11px;

  .new-top {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    // column-gap: 20px;
    margin-bottom: 30px;

    .top-item:nth-child(-n + 2) {
      margin-right: 20px;
    }

    .top-item {
      // flex: 1;
      // width: 420px;
      width: 32.3%;
      height: 310px;
      background: #ffffff;
      border-radius: 10px;
      border: 1px solid #d4f5fa;

      .bg1 {
        background-image: url('@/assets/detail/bg1.png');
      }
      .bg2 {
        background-image: url('@/assets/detail/bg2.png');
      }
      .bg3 {
        background-image: url('@/assets/detail/bg3.png');
      }

      .top-item-top {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        height: 60px;
        padding: 19px 30px 18px 29px;
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .top-item-text {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          cursor: pointer;
          white-space: nowrap;
        }
      }

      .top-item-bot {
        padding: 24px 30px 0px 30px;
        display: flex;
        flex-direction: column;
        .list-item {
          display: flex;
          align-items: center;
          flex-direction: row;
          margin-bottom: 25px;
          height: 13px;
          cursor: pointer;

          .dot {
            width: 4px;
            height: 4px;
            background: #10aec2;
            border-radius: 50%;
            margin-right: 10px;
          }

          .text {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            // line-height: 32px;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }

  .new-bot {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    // column-gap: 20px;

    .bot-item:nth-child(-n + 1) {
      margin-right: 20px;
    }

    .bot-item {
      // flex: 1;
      width: 49.23%;
      height: 310px;
      border-radius: 10px;
      background: #ffffff;
      border: 1px solid #d4f5fa;

      .bg4 {
        background-image: url('@/assets/detail/bg4.png');
      }

      .bg5 {
        background-image: url('@/assets/detail/bg5.png');
      }

      .bot-item-top {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        height: 60px;
        padding: 19px 30px 18px 29px;
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .bot-item-text {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          cursor: pointer;
        }
      }

      .bot-item-bot {
        padding: 24px 28px 0 30px;
        width: 100%;

        .list-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 25px;
          height: 13px;
          width: 100%;
          cursor: pointer;

          .list-item-left {
            width: 80%;
            display: flex;
            align-items: center;

            .dot {
              width: 4px;
              height: 4px;
              background: #10aec2;
              border-radius: 50%;
              margin-right: 10px;
            }

            .text {
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 14px;
              color: #666666;
              // line-height: 32px;
              // width: 460px;
              width: 95%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .list-item-right {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            // line-height: 32px;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>