<template>
  <div class="box">
    <div class="search-box">
      <el-input
        v-model.trim="projectName"
        placeholder="请输入项目名称/项目编号"
        style="width: 600px; margin-right: 13px"
      />
      <el-button @click="search"> 搜 索 </el-button>
    </div>
    <div class="main-box">
      <div class="list-box">
        <div
          class="list-box-item"
          v-for="(item, index) in list"
          :key="index"
          @click="showDetail(item)"
        >
          <div class="item-top">
            <div class="title-box">
              <div class="title-left">
                <div
                  class="tag-box"
                  :class="{
                    jyzx: item.type === '0',
                    zczx: item.type === '1',
                    wtfk: item.type === '3',
                    wgjb: item.type === '2',
                    other: '0,1,2,3'.indexOf(item.type) === -1,
                  }"
                >
                  <el-tooltip effect="dark" :content="getName<PERSON>y<PERSON><PERSON>(item.type)" placement="top-start">
                    <span
                      style="
                        width: 100%;
                        display: block;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      "
                      >{{ getNameBy<PERSON>ey(item.type) }}</span
                    >
                  </el-tooltip>
                </div>
                <div class="title">
                  <el-tooltip effect="dark" :content="item.projectName" placement="top-start">
                    <span
                      style="
                        width: 100%;
                        display: block;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      "
                      >{{ item.projectName }}</span
                    >
                  </el-tooltip>
                </div>
              </div>
              <div class="title-right">
                <div class="area">
                  <el-tooltip effect="dark" :content="item.unitName" placement="top-start">
                    <span
                      style="
                        width: 100%;
                        display: block;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                      "
                      >{{ item.unitName }}</span
                    >
                  </el-tooltip>
                </div>
                <div class="time">{{ item.replyTime }}</div>
              </div>
            </div>

            <div class="content-item">
              <el-tooltip
                effect="dark"
                :content="'咨询内容：' + item.questionerContent"
                placement="top-start"
              >
                <span
                  style="
                    width: 100%;
                    display: block;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  "
                  >咨询内容：{{ item.questionerContent }}</span
                >
              </el-tooltip>
            </div>
            <div class="content-item">
              <el-tooltip
                effect="dark"
                :content="'处理结果：' + item.replyContent"
                placement="top-start"
              >
                <span
                  style="
                    width: 100%;
                    display: block;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                  "
                  >处理结果：{{ item.replyContent }}</span
                >
              </el-tooltip>
            </div>
          </div>
          <div class="item-bottom">
            <div class="bottom-left">
              <div class="left-item">
                <img src="@/assets/interacteCommunication/item01.png" alt="" />
                {{ item.questionerName }}
              </div>

              <div class="left-item">
                <img src="@/assets/interacteCommunication/item02.png" alt="" />
                {{ item.questionerTelephone }}
              </div>
            </div>
            <div class="bottom-right">
              <span style="margin-right: 10px"> 查看详情 </span>
              <span> → </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="page">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        @current-change="currentChange"
      >
      </el-pagination>
    </div>
    <detailDialog ref="detailDialogRef" />
  </div>
</template>

<script>
import detailDialog from './detail.vue';

export default {
  components: {
    detailDialog,
  },
  data() {
    return {
      list: [],
      currentPage: 1,
      projectName: '',
      total: 0,
      typeList: [],
    };
  },
  mounted() {
    this.getType();
    this.getDataList();
  },
  methods: {
    /**
     * 获取当前页码
     */
    currentChange(val) {
      this.currentPage = val;
      this.getDataList();
    },
    /**
     * 通过key获取名称
     * @param {string} e key
     */
    getNameByKey(e) {
      return this.typeList.find((item) => item.dataKey === e).dataValue;
    },
    /**
     * 获取状态数据
     */
    getType() {
      this.$ajax({
        url: '/web/getType',
        method: 'GET',
        data: {
          baseType: 'SXLX',
        },
      }).then((r) => {
        if (r.data.code !== 200) return this.$message.error(r.data.msg);
        this.typeList = r.data.data;
      });
    },
    /**
     * 获取列表数据
     */
    getDataList() {
      let data = {
        pageNo: this.currentPage,
        pageSize: 10,
        projectName: this.projectName,
      };
      this.$ajax({
        url: '/web/publicList',
        method: 'GET',
        data,
      }).then((r) => {
        if (r.data.code !== 200) return this.$message.error(r.data.msg);
        this.list = r.data.data.records;
        this.total = r.data.data.total;
      });
    },
    /**
     * 查询
     */
    search() {
      this.getDataList();
    },
    /**
     * 查看详情
     */
    showDetail(e) {
      this.$refs.detailDialogRef.open(e);
    },
  },
};
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  background-color: #f7f7f7;

  .search-box {
    width: 100%;
    height: 84px;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    ::v-deep .el-input__inner {
      height: 36px;
      line-height: 36px;
    }

    ::v-deep .el-button {
      height: 34px;
      width: 100px;
      background: #ed911f;
      font-family: Microsoft YaHei;
      font-weight: bold;
      font-size: 16px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
    }
  }

  .main-box {
    width: 1300px;
    margin: auto;
    padding: 30px 0 0 0;

    .list-box {
      width: 100%;

      .list-box-item {
        width: 100%;
        padding: 0 20px;
        background: #ffffff;
        border-radius: 4px;
        margin-bottom: 20px;

        &:nth-last-of-type(1) {
          margin-bottom: 0;
        }

        .item-top {
          width: 100%;
          padding: 20px 0;
          border-bottom: 1px solid #ececec;

          .title-box {
            width: 100%;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 17px;

            .title-left {
              width: 832px;
              max-width: 832px;
              min-width: 832px;
              display: flex;
              align-items: center;

              .tag-box {
                max-width: 126px;
                min-width: 70px;
                height: 28px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                font-size: 14px;
                color: #ffffff;
                padding: 0 7px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                margin-right: 12px;
              }

              .jyzx {
                background-color: #0098ff;
              }

              .zczx {
                background: #0dc58b;
              }

              .wtfk {
                background: #ed911f;
              }

              .wgjb {
                background: #f45151;
              }

              .other {
                /* 超出10个字隐藏，之所以设置11em是因为省略号占一个位置 */
                // max-width: 9em;
                display: block;
                line-height: 28px;
                text-align: center;
                overflow: hidden;
                /* 显示省略符号来代表被修剪的文本。 */
                text-overflow: ellipsis;
                /* 文本不换行 */
                white-space: nowrap;
                background-color: #3395bc;
              }

              .title {
                width: 650px;
                max-width: 650px;
                min-width: 650px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }

            .title-right {
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 14px;
              color: #999999;
              display: flex;
              align-items: center;

              .area {
                min-width: 200px;
                max-width: 200px;
                width: 200px;
                margin-right: 30px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }

          .content-item {
            margin-bottom: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            &:nth-last-of-type(1) {
              margin-bottom: 0;
            }
          }
        }

        .item-bottom {
          height: 52px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .bottom-left {
            display: flex;
            align-items: center;

            .left-item {
              display: flex;
              align-items: center;
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 14px;
              color: #666666;
              margin-right: 30px;

              img {
                width: 13px;
                height: 14px;
                margin-right: 6px;
                user-select: none;
              }
            }
          }

          .bottom-right {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #ed911f;
            cursor: pointer;
            user-select: none;
          }
        }
      }
    }
  }

  .page {
    height: 90px;
    display: flex;
    justify-content: center;
    align-items: center;

    ::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
      background: #ed911f;
      color: #fff !important;
    }

    ::v-deep .el-pagination.is-background .el-pager li:not(.disabled):hover {
      color: #ed911f;
    }
  }
}
</style>
