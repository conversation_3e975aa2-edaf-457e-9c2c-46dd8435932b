<template>
  <div class="ndl-button-box">
    <div class="left">
      <slot name="left" />
    </div>
    <div class="right">
      <slot name="right" />
    </div>
  </div>
</template>

<script>
export default {
  // props: {
  //   title: String,
  // },
};
</script>

<style lang='scss' scoped>
.ndl-button-box {
  width: 100%;
  height: 60px;
  background-color: #ffffff;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  .left {
    width: auto;
    height: auto;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 36px;
  }
  .right {
    width: auto;
    height: auto;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 10px;
    div + div {
      margin-left: 10px;
    }
  }
}
</style>
