<template>
  <div>
    <nd-dialog ref="saveProjectRef" class="dialog" :width="'500px'" :height="'150px'" append-to-body :before-close="close" title="保存查询条件" center>
      <div class="save-project-box">
        <nd-search-more-item :width="'70px'" title="方案名称">
          <nd-input v-model.trim="projectName" class="project-item-save-input" width="400px" placeholder="请输入方案名称" maxlength="20" />
        </nd-search-more-item>
        <nd-search-more-item :width="'70px'" title="">
          <nd-checkbox v-model="isDefault" width="50px" true-label="1" false-label="0">
            设为默认方案
          </nd-checkbox>
        </nd-search-more-item>
      </div>
      <template #footer>
        <nd-button type="primary" @click="save">
          确定
        </nd-button>
        <nd-button slot="reference" @click="close">
          关闭
        </nd-button>
      </template>
    </nd-dialog>
  </div>
</template>

<script>
import ndInput from "@/components/ndInput.vue";
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndSearchMoreItem from "@/components/ndSearchMoreItem.vue";
import ndCheckbox from "@/components/ndCheckbox.vue";
export default {
  components: {
    ndInput,
    ndDialog,
    ndButton,
    ndSearchMoreItem,
    ndCheckbox
  },

  data() {
    return {
      projectName: "",
      isDefault: ""
    };
  },

  mounted() {

  },

  methods: {
    // 保存
    save() {
      if (this.projectName == "") {
        this.$message.warning('请输入方案名称')
      } else {
        this.$emit('saveSearchData', this.projectName, this.isDefault)
        this.$refs.saveProjectRef.close()
      }
    },
    // 打开弹框
    open(...val) {
      this.projectName = val[0]
      this.isDefault = val[1]
      this.$refs.saveProjectRef.open()
    },
    // 关闭弹框
    close() {
      this.$parent.open()
      this.$refs.saveProjectRef.close()
    },
  },
};
</script>

<style lang="scss" scoped>
.save-project-box {
  margin: 50px 0 0 0px;
}
</style>