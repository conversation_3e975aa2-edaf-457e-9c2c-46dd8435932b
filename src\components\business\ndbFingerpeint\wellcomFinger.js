﻿var Wellcom = window.Wellcom || {};
(function () {

	Wellcom.websocket = null;

	Wellcom.callback = null;

	var sendJSON = {
		"sign": "wellcom",
		"method": "FPIDevDetect",
		"nport": -1,
		"timeout": 10,
		"imgno": "",
		"param1": "",
		"param2": "",
		"param3": ""
	};

	var recJSON = {
		"iRet": -1,
		"msg": ""
	}

	function exeCallback(data) {
		if (Wellcom.callback) {
			Wellcom.callback(data);
		}
		setTimeout(function () {
			Wellcom.callback = null;
		}, 100)
	}

	function doSend(message) {
		if (Wellcom.websocket) {
			if (Wellcom.websocket.readyState == WebSocket.OPEN) {
				Wellcom.websocket.send(message);
			} else {
				Wellcom.close();
				Wellcom.init();
				recJSON["iRet"] = -1;
				recJSON["msg"] = "重连中，请稍后重试";
				exeCallback(recJSON);
			}
		} else {
			Wellcom.init();
			recJ<PERSON>N["iRet"] = -1;
			recJ<PERSON><PERSON>["msg"] = "设备初始化中，请稍后重试";
			exeCallback(recJSON);
		}
	}


	function onOpen(evt) {

	}

	function onClose(evt) {
		Wellcom.close();
	}

	function onMessage(evt) {
		var result = JSON.parse(evt.data);

		exeCallback(result);
	}

	function onError(evt) {
		Wellcom.close();
	}

	Wellcom.init = function () {
		if (Wellcom.websocket) {
			try {
				Wellcom.websocket.close();
				Wellcom.websocket = null;
			} catch (err) {
				Wellcom.websocket = null;
			}
		}
		try {
			Wellcom.websocket = new WebSocket("ws://127.0.0.1:32102");
		} catch (e) {
			console.log(e);
			Wellcom.websocket.close();
			Wellcom.websocket = null;
		}

		Wellcom.websocket.onopen = function (evt) {
			onOpen(evt)
		};
		Wellcom.websocket.onmessage = function (evt) {
			onMessage(evt)
		};
		Wellcom.websocket.onerror = function (evt) {
			onError(evt)
		};
		Wellcom.websocket.onclose = function (evt) {
			onClose(evt);
		};
	}

	Wellcom.close = function () {
		if (Wellcom.websocket) {
			try {
				Wellcom.websocket.close();
			} catch (err) {

			}
		}
		Wellcom.websocket = null;
	}

	Wellcom.get_device = function (callback) {
		console.log(new Date().getTime());
		if (typeof (callback) == "function") {
			Wellcom.callback = callback;
		}
		sendJSON.method = "FPIDevDetect";
		var sendMessage = JSON.stringify(sendJSON);
		doSend(sendMessage);
	}

	Wellcom.get_feature = function (callback) {
		recJSON["msg"] = "";
		if (sendJSON["nport"] < 0) {
			callback({ iRet: -1, msg: "未检测到设备" })
			return
		}
		if (typeof (callback) != "function") {
			console.log("参数类型错误");
			return
		}
		Wellcom.callback = callback;
		sendJSON.method = "FPIGetFeature";
		sendJSON.timeout = 15;
		var sendMessage = JSON.stringify(sendJSON);
		doSend(sendMessage);
	}


	Wellcom.get_template = function (callback) {
		recJSON["msg"] = "";
		if (sendJSON["nport"] < 0) {
			callback({ iRet: -1, msg: "未检测到设备" })
			return
		}
		if (typeof (callback) != "function") {
			console.log("参数类型错误");
			return
		}
		Wellcom.callback = callback;
		sendJSON.method = "FPIGetTemplate";
		sendJSON.timeout = 45;
		var sendMessage = JSON.stringify(sendJSON);
		// console.log(sendJSON,2223333);
		doSend(sendMessage);
	}


	Wellcom.Readimg = function (imgType, callback) {
		if (sendJSON["nport"] < 0) {
			callback({ iRet: -1, msg: "未检测到设备" })
			return
		}
		if (typeof (callback) != "function") {
			console.log("参数类型错误");
			return
		}
		Wellcom.callback = callback;
		sendJSON.method = "FPIGetImageData";
		sendJSON.imgno = imgType;
		var sendMessage = JSON.stringify(sendJSON);
		doSend(sendMessage);
	}

	Wellcom.match = function (template, feature, callback) {
		if (sendJSON["nport"] < 0) {
			callback({ iRet: -1, msg: "未检测到设备" })
			return
		}
		if (typeof (callback) != "function") {
			console.log("参数类型错误");
			return
		}
		Wellcom.callback = callback;
		sendJSON.method = "FPIFpMatch";
		sendJSON.param1 = template;
		sendJSON.param2 = feature;
		var sendMessage = JSON.stringify(sendJSON);
		doSend(sendMessage);
	}

	Wellcom.cancel = function () {

		sendJSON.method = "FPICancel";
		var sendMessage = JSON.stringify(sendJSON);
		doSend(sendMessage);
	}

	Wellcom.setPortAttr = function (nport) {
		sendJSON.nport = nport;
	}

	Wellcom.get_port = function () {
		return sendJSON.nport;
	}

	Wellcom.init();

}());

export {
	Wellcom
}