<template>
  <div class="main">
    <div class="main-box">
      <div class="top-box">
        <div class="left-nav">
          <div class="left">
            <div class="title-text">金融产品</div>
            <img src="@/assets/detail/title-icon.png" alt="" srcset="" />
          </div>
          <!-- <div class="nav-box">
            <div class="nav-item" :class="[activeNav === item.order ? 'active-nav' : 'deactive-nav']" v-for="(item, index) in navArr" :key="index" @mouseover="navClick(item.order)">
              <div class="nav-top">
                <div class="nav-title">{{ item.title }}</div>
              </div>
            </div>
          </div> -->
        </div>

        <div class="more-text" @click="toMore">查看更多>></div>
      </div>
      <div v-if="activeNav === '1'">
        <el-carousel trigger="click" height="257px" :interval="3000" arrow="hover" @change="carouselChange" indicator-position="none">
          <el-carousel-item v-for="(item2, index) in listSupply" :key="index">
            <div class="bot-box">
              <template v-for="(item,index) in item2">
                <div class="list-box" :key="index" :style="{
                'background-image': 'url(' + item.imgUrl + ')',
                'background-repeat': 'no-repeat',
                'background-size': '100% 100%',
              }" @click="goDetails(item.path)">
                  <div class="text-cont">
                    <div class="list-text">
                      {{ item.title }}
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div v-if="activeNav === '2'">
        <el-carousel trigger="click" height="257px" :interval="3000" arrow="hover" @change="carouselChangeTwo">
          <el-carousel-item v-for="(item2, index) in listDemand" :key="index">
            <div class="bot-box">
              <template v-for="(item,index) in item2">
                <div class="list-box" :key="index" :style="{
                'background-image': 'url(' + item.imgUrl + ')',
                'background-repeat': 'no-repeat',
                'background-size': '100% 100%',
              }" @click="goDetails(item.path)">
                  <div class="text-cont">
                    <div class="list-text">
                      {{ item.title }}
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
  </div>
</template>

<script>
import ndRadio from '@/components/ndRadio.vue';

export default {
  components: {
    ndRadio,
  },
  name: 'home',
  data() {
    return {
      // 供应大厅
      listSupply: [
        // { url: require('@/assets/detail/exBg3.png') },
        // { url: require('@/assets/detail/exBg3.png') },
        // { url: require('@/assets/detail/exBg3.png') },
        // { url: require('@/assets/detail/exBg3.png') },
      ],
      // 供需大厅
      listDemand: [],
      //初始化数据列表
      imgList: [
        1, 2, 3, 4
      ], //初始化数据列表
      activeNav: '1',
      navArr: [
        {
          title: '供应大厅',
          url: require('@/assets/detail/bmz.png'),
          noUrl: require('@/assets/detail/acbmz.png'),
          iconUrl: require('@/assets/detail/bao.png'),
          order: '1',
        },
        {
          title: '需求大厅',
          url: require('@/assets/detail/jjz.png'),
          noUrl: require('@/assets/detail/acjjz.png'),
          iconUrl: require('@/assets/detail/jing.png'),
          order: '2',
        },
      ],
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    handleActive(index) {
      this.activeArea = index;
    },
    goDetails(path) {
      // this.$router.push({
      //   path: 'details',
      // });
      // this.$message.success("敬请期待！")
      this.$router.push({
        path: 'listDetailView',
        query: {
          path: path,
          bannerType: 0, // bannerType[this.$route.name],
          breadcrumbName: this.activeNav == '1' ? '供应大厅详情' : '需求大厅详情',
        },
      });
      this.$emit('getDealInformation', '2')
    },
    toMore() {
      //更多
      this.$router.push({
        path: 'supplyHallIndex',
        query: {
          activeNav: this.activeNav
        }
      });
      this.$emit('getDealInformation', '2')
      // this.$message.success("敬请期待！")
    },
    // 跳转 详情
    goDetail(item) {
      this.$router.push({
        path: 'listDetailView',
        query: {
          path: item.path,
          preInfo: JSON.stringify({ name: this.$route.meta.pageName, path: this.$route.name }),
          bannerType: 4,
        },
      });
    },
    getData() {
      //---供应大厅
      this.$ajax({
        url: '/gxdt/gydt/',
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        if (res.status === 200) {
          try {
            // this.listSupply = eval(res.data).slice(0, 8);

            let arrAll = eval(res.data).slice(0, 8);
            let small = [];
            arrAll.forEach(item => {
              if (small.length == 4) {
                this.listSupply.push(small);
                small = [];
              }
              small.push(item);
            });
            if (small.length) this.listSupply.push(small);
            // console.log(this.listSupply, '009090990');

          } catch (error) {
            console.error('数据格式有误');
          }
        }
      });
      // 需求大厅
      this.$ajax({
        url: '/gxdt/xqdt/',
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        if (res.status === 200) {
          try {
            // this.listDemand = eval(res.data).slice(0, 8);

            let arrAll = eval(res.data).slice(0, 8);
            let small = [];
            arrAll.forEach(item => {
              if (small.length == 4) {
                this.listDemand.push(small);
                small = [];
              }
              small.push(item);
            });
            if (small.length) this.listDemand.push(small);
            // console.log(this.listDemand, '009090990');

          } catch (error) {
            console.error('数据格式有误');
          }
        }
      });
    },

    navClick(order) {
      this.activeNav = order;
      //   this.getData();
    },

    carouselChange(e) {
      console.log(e, '轮播图');
    },
    carouselChangeTwo(e) {
      console.log(e, '轮播图');
    }
  },
};
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  // padding: 30px 0 0px;
  background: #fff;
  display: flex;
  justify-content: center;

  .main-box {
    // width: 100%;
    width: 1300px;

    .top-box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      user-select: none;

      .left-nav {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      .nav-box {
        display: flex;
        align-items: center;
        flex-direction: row;
        margin-left: 39px;
        // column-gap: 39px;

        .active-nav {
          height: 30px;
          width: 105px;
          background: #ed911f;
          border-radius: 8px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 18px;
          color: #ffffff;
        }

        .deactive-nav {
          width: 105px;
          height: 30px;
          border-radius: 8px;
          border: 1px solid #e5e5e5;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 18px;
          color: #666666;
        }

        .nav-item:nth-child(-n + 1) {
          margin-right: 20px;
        }

        .nav-item {
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          .nav-top {
            display: flex;
            align-items: center;
          }
        }
      }

      .left {
        display: flex;
        flex-direction: column;
        height: 32px;

        .title-text {
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 24px;
          color: #333333;
          line-height: 24px;
        }
      }

      .more-text {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 24px;
        cursor: pointer;
      }
    }

    .bot-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;

      // row-gap: 20px;
      .list-box {
        height: 227px;
        width: 311px;
        // height: 20.68vh;
        // width: 23.84%;
        cursor: pointer;
        position: relative;
        margin-bottom: 30px;
        border-radius: 8px;

        .text-cont {
          position: absolute;
          bottom: 0px;
          left: 0px;
          height: 44px;
          width: 100%;
          background: rgba(61, 61, 61, 0.8);
          border-radius: 0px 0px 8px 8px;
          padding-left: 10px;
          display: flex;
          align-items: center;
        }

        .list-text {
          width: 98%;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    ::v-deep .el-carousel__arrow {
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.9);
      top: calc(50% - 14px);

      .el-icon-arrow-left,
      .el-icon-arrow-right {
        color: #666666;
        font-size: 18px;
        font-weight: bold;
      }
    }
  }
}
.more-text:hover{
  color: #ed911f!important;
}
</style>