<template>
  <div class="container">
    <div class="breadcrumbs-box">
      <breadcrumbs :titles="[fromPage, breadcrumbName]" />
    </div>
    <!-- <div class="title-box">
      <div class="main-text">跨省通办”提质增效惠民生</div>
      <div class="title-bot">
        <span>所属组织：所属组织</span>
        <span>更新时间： 2023-02-10 10:18:05</span>
      </div>
    </div> -->

    <div class="hiddenBox" v-if="!isHtml">
      <div id="getIframeHeightId" class="main-content" v-html="content"></div>
    </div>
    <iframe v-if="!isHtml" ref="myIframe" :src="content2" frameborder="0"
      style="width: 100%;visibility: hidden;" :key="content2"></iframe>

    <div v-else>
      <div class="concent-bk">
        <div class="list_ntitle">{{ bindTitle }}</div>
        <div class="title">发布时间：{{ $route.query.times }}</div>
        <div class="hr"></div>
        <div class="ql-snow">
          <div class="main-content ql-editor" v-html="bindContent"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import breadcrumbs from './components/breadcrumbs.vue';

// 获取url参数
const getQueryVariable = (variable) => {
  let geturl = window.location.href;
  let getqyinfo = geturl.split('?')[1];
  let getqys = new URLSearchParams('?' + getqyinfo);
  return getqys.get(variable);
};

export default {
  components: {
    breadcrumbs,
  },
  data() {
    return {
      fromPage: '',
      detailName: '',
      content: '',
      content2: '',
      breadcrumbName: '资讯详情',
      isHtml: false,
      bindTitle: '',
      bindContent: '',
      timer: null
    };
  },
  beforeRouteEnter(to, from, next) {
    console.log(localStorage.getItem('list_detail_pageName_cache'), '参数111111111111111');

    next((_vm) => {
      _vm.getData();
      _vm.fromPage = from.meta.pageName || localStorage.getItem('list_detail_pageName_cache');
      // _vm.detailName = _vm.fromPage.slice(0, 2);
      if (from.meta.pageName)
        localStorage.setItem('list_detail_pageName_cache', from.meta.pageName);
    });
  },
  mounted() {
    if (this.$route.query.breadcrumbName) {
      this.breadcrumbName = this.$route.query.breadcrumbName;
    }
  },
  activated() {
    if (this.$route.query.breadcrumbName) {
      this.breadcrumbName = this.$route.query.breadcrumbName;
    }
  },
  methods: {
    getData() {
      this.content = ""
      this.content2 = ""
      this.isHtml = this.$route.query.isHtml
      if (this.$route.query.isHtml) {
        this.getDataBnid()
      } else {
        this.getDataHtml()
      }
    },
    // 截取url中除去协议域名端口号后的内容
    getPathFromUrl(url) {
      const regex = /\/\/[^/]+(.+)/;
      const match = url.match(regex);
      return match ? match[1] : null;
    },
    getDataHtml() {
      // http://www.whnccq.com/site/xwzx/gzdt/2024/08/01152341558343.html
      var original = getQueryVariable('path');

      // /site/xwzx/gzdt/2024/08/01152341558343.html
      var url2 = this.getPathFromUrl(getQueryVariable('path').replaceAll("http://", "https://"));

      // xwzx/gzdt/2024/08/01152341558343.html
      var url1 = url2.substring(5, url2.length);

      // this.$ajax({
      //   url: url1,
      //   method: 'get',
      //   serverName: 'nd-ss',
      // }).then((res) => {
      //   let data = res.data.replaceAll(
      //     '<img src="',
      //     `<img style="max-width:1078px;display: block;" src="${window.ipConfig.imgUrl}`,
      //   );
      // });

      this.$ajax({
        url: url1,
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        let data = res.data.replaceAll(
          '<img src="',
          `<img style="max-width:1078px;display: block;" src="${window.ipConfig.imgUrl}`,
        );
        this.content = data;
        this.content2 = getQueryVariable('path').replaceAll("http://", "https://")
        setTimeout(() => {
          // 选择你想要监听的元素  
          const targetElement = document.querySelector('#getIframeHeightId');

          // 创建一个 ResizeObserver 实例  
          const resizeObserver = new ResizeObserver(entries => {
            for (let entry of entries) {
              // 获取新的高度  
              const newHeight = entry.contentRect.height;
              // console.log('Height changed to:', newHeight);
              if (newHeight > 0) {
                clearTimeout(this.timer)
                const iframe = this.$refs.myIframe;
                let ifHeight = iframe.style.height.split('px')[0] * 1
                // console.log(ifHeight, 'iiiiiiiiiiiiiiiiiiiifheight');

                iframe.style.height = `${newHeight + 100}px`;
                setTimeout(() => {
                  iframe.style.visibility = `unset`;
                  this.timer = setTimeout(() => {
                    resizeObserver.unobserve(targetElement);
                  }, 500);
                }, 0);
              }
            }
          });

          // 开始观察目标元素  
          resizeObserver.observe(targetElement);
        }, 0);
      });
    },
    getDataBnid() {
      this.bindTitle = ''
      this.bindContent = ''
      this.$ajax({
        url: "/ForensicsNotice/getAnnouncementMessage",
        method: 'get',
        data: {
          id: this.$route.query.id
        }
      }).then((res) => {
        // console.log(res.data);
        if (res.data.code === 200) {
          this.bindTitle = res.data.data.title
          this.bindContent = res.data.data.content
        }

      });
    }
  },
};
</script>
<style lang="scss" scoped>
@import url('@/assets/css/vue-quill.snow.css');

.breadcrumbs-box {
  padding-left: calc((100% - 1200px) / 2);
  background-color: #f8f8f8;
}

.container {
  border-top: 1px solid #cbcbcb;
  // width: 1300px;
  background: #fff;

  // margin: 0 auto;
  // padding: 0 calc(100% - 1600px);
  // padding: 20px 10%;
  .title-box {
    height: 155px;
    border-bottom: 1px solid #eee;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-bottom: 30px;

    .main-text {
      font-size: 28px;
      font-weight: bold;
      color: #333333;
      text-align: center;
    }

    .title-bot {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }
  }

  .main-content {
    width: 100%;
    min-height: 300px;
    background: #fff;
    margin-top: 20px;
    overflow: hidden;
    padding-top: 10px;
  }
}

.concent-bk {
  width: 1200px;
  margin: 20px auto;
  border: 1px solid #e8e8e8;
  background: #fff;
  padding: 10px;

  .list_ntitle {
    font-size: 24px;
    font-weight: bold;
    color: #151515;
    line-height: 55px;
    text-align: center;
  }

  .title {
    text-align: center;
    line-height: 25px;
    font-size: 16px;
    color: #000;
  }

  .hr {
    width: 1140px;
    margin: 0 auto;
    height: 2px;
    margin-top: 12px;
    background: #dddddd;
  }
}

.hiddenBox {
  position: fixed;
  visibility: hidden;
  opacity: 0;
}
</style>
