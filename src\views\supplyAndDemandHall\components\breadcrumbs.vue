<template>
  <div class="breadcrumbs">
    <div class="icon"><i class="el-icon-location-outline" /></div>
    <div v-for="(item, index) in titles" :key="index" @click="goback(index)">
      <span :style="index == titles.length - 1 ? 'color:#ed911f;' : 'cursor:pointer'">{{
        item
      }}</span>
      <span v-if="index != titles.length - 1">&nbsp;>&nbsp;</span>
    </div>
  </div>
</template>

<script>
// 获取url参数
const getQueryVariable = (variable) => {
  let geturl = window.location.href;
  let getqyinfo = geturl.split('?')[1];
  let getqys = new URLSearchParams('?' + getqyinfo);
  return getqys.get(variable);
};

export default {
  props: {
    titles: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    goback(index) {
      if (!index) {
        // let path = JSON.parse(getQueryVariable('preInfo')).path;
        // this.$router.push(path);
        this.$router.back();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.breadcrumbs {
  // width: 1200px;
  height: 60px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin: 0 auto;

  .icon {
    color: #ed911f;
    margin-right: 8px;
  }
}
</style>
