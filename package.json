{"name": "ndsc", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode dev", "build": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint", "docs:dev": "vuepress dev docs --temp .temp", "docs:build": "vuepress build docs"}, "dependencies": {"@vue-office/pdf": "^1.0.0", "@vue/composition-api": "^1.7.1", "async-validator": "^1.11.5", "axios": "^0.26.0", "core-js": "^3.8.3", "echarts": "^5.4.1", "echarts-gl": "^2.0.8", "echarts-liquidfill": "^3.1.0", "element-ui": "^2.15.9", "moment": "^2.29.1", "qrcodejs2-fix": "^0.0.1", "quill": "^1.3.6", "sass": "^1.26.5", "sass-loader": "^10.0.0", "vue": "2.6.14", "vue-carousel-3d": "^1.0.1", "vue-esign": "^1.1.4", "vue-router": "^3.5.1", "vue-server-renderer": "^2.6.14", "vue-virtual-scroll-list": "^2.3.4"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "crypto-js": "^4.2.0", "eslint": "^8.38.0", "eslint-plugin-vue": "^9.10.0", "postcss-css-variables": "^0.18.0", "postcss-px-to-viewport": "^1.1.1", "vue-eslint-parser": "^9.1.1", "vue-template-compiler": "2.6.14", "vuex": "^3.2.0"}}