<template>
  <div>
    <nd-dialog
      ref="bankForm"
      width="1100px"
      height="513px"
      title="银行对照表"
      append-to-body
      :before-close="closeBankTable"
      center
    >
      <div class="content">
        <!-- 查询条件 -->
        <div class="search-opea">
          <div class="item">
            <span>省份</span><ndSelect
              v-model="params.provinceId"
              placeholder=""
              width="140px"
              clearable
              @change="selectCity"
            >
              <el-option
                v-for="item in provinces"
                :key="item.unitId"
                :label="item.unitName"
                :value="item.unitId"
              />
            </ndSelect>
          </div>
          <div class="item">
            <span>市</span><ndSelect v-model="params.cityId" placeholder="" width="140px" clearable>
              <el-option
                v-for="item in citys"
                :key="item.unitId"
                :label="item.unitName"
                :value="item.unitId"
              />
            </ndSelect>
          </div>
          <div class="item">
            <span>银行选择</span><ndSelect v-model="params.bankSelect" placeholder="" width="210px" clearable>
              <el-option
                v-for="item in banks"
                :key="item.id"
                :label="item.bankName"
                :value="item.id"
              />
            </ndSelect>
          </div>
          <div class="item">
            <span>银行名称</span><nd-input v-model="params.bankName" width="210px" type="text" />
          </div>
          <div class="item">
            <nd-button type="primary" @click="search">
              查询
            </nd-button>
          </div>
        </div>
        <!-- 列表数据 -->
        <ndTable :data="tableData" height="100%">
          <el-table-column prop="bankUnit" label="城市" align="center" />
          <el-table-column prop="bankCode" label="代码" />
          <el-table-column align="center" prop="bankName" label="银行名称" />
        </ndTable>
        <!-- 分页组件 -->
        <ndPagination
          :total="total"
          :current-page="params.page"
          :total-page="totalPage"
          :page-size="params.pageSize"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <template #footer>
        <nd-button type="normal" @click="closeBankTable">
          关闭
        </nd-button>
      </template>
    </nd-dialog>
  </div>
</template>

<script>
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndTable from "@/components/ndTable.vue";
import ndInput from "@/components/ndInput.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndPagination from "@/components/ndPagination.vue";

export default {
  components: {
    ndDialog,
    ndButton,
    ndTable,
    ndInput,
    ndSelect,
    ndPagination,
  },
  data() {
    return {
      // 查询列表参数
      params: {
        page: 1, //当前在第几页
        pageSize: 10, //每页显示条数
        provinceId: "", //省选择
        cityId: "", //城市选择
        bankSelect: "", //查询选择银行的id
        bankName: "", //自己输入的银行名称
      },
      url: "", //请求接口地址
      total: 1, //总数据条数
      totalPage: 1, //总页数
      provinces: [], //各个省
      citys: [], //各个城市
      banks: [], //各个银行
      tableData: [], //列表数据,
    };
  },

  mounted() {},
  methods: {
    // 选择省
    selectProvince(id) {
      this.$ajax({
        url: this.url + "/apiPayeeManagement/getBankArea.do",
        method: "post",
        data:{
          unitId:id
        }
      })
        .then((res) => {
          // console.log(2);
          if (res.data.code == 0) {
            // console.log(res.data);
            this.provinces = res.data.data;
          } else {
            console.log(res.data.msg);
          }
        })
        .catch(function () {});
    },
    selectCity(e){
      console.log(e);
         let data = {
        unitId: e,
      };
      this.$ajax({
        url: this.url + "/apiPayeeManagement/getBankArea.do",
        method: "post",
        data,
      })
        .then((res) => {
          // console.log(2);
          if (res.data.code == 0) {
            // console.log(res.data);
            this.citys= res.data.data;
          } else {
            console.log(res.data.msg);
          }
        })
        .catch(function () {});
    },
    // 获取选择的银行
    selectBank() {
      this.$ajax({
        url: this.url + "/apiPayeeManagement/bankSelect.do",
        method: "post",
      })
        .then((res) => {
          // console.log(2);
          if (res.data.code == 0) {
            // console.log(res.data);
            this.banks = res.data.data;
          } else {
            console.log(res.data.masg);
          }
        })
        .catch(function () {});
    },
    // 一打开页面获取列表数据
    getTableData(data) {
      this.tableData=[];
      console.log(1);
      this.$ajax({
        url: this.url + "/apiPayeeManagement/openAccountList.do",
        method: "post",
        data:data,
      }).then((res) => {
        if (res.data.code == 0) {
          console.log(res.data);
          this.tableData = res.data.data.list;
          this.total = res.data.data.totalitem;
          this.params.page = res.data.data.cpage;
          this.totalPage = res.data.data.totalpage;
        }
      });
    },
    //打开弹框
    openBankTable() {
      this.$refs.bankForm.open();
      this.getTableData(this.params);
      this.selectProvince(this.params.provinceId);
      this.selectBank()
    },
    //关闭弹框
    closeBankTable() {
      // 清空数据表单
      this.$refs.bankForm.close();
      this.params.page = 1;
      this.params.pageSize = 10;
      this.params.provinceId = ""
      this.params.cityId = "";
      this.params.bankSelect='';
      this.params.bankName = '';
      this.tableData = [];
      this.provinces=[];
      this.citys=[];
      this.banks=[];
    },
    // 根据条件查询数据
    search() {
      this.params.page = 1;
      this.getTableData(this.params);
    },
    // 分页组件每页显示条数改变时
    handleSizeChange(val) {
      this.params.pageSize = val;
      this.params.page = 1;
      this.getTableData(this.params);
    },
    // 分页组件跳转其他页码
    handleCurrentChange(val) {
      this.params.page = val;
      this.getTableData(this.params);
    },
  },
};
</script>

<style lang="scss" scoped>
.search-opea {
  display: flex;
  align-items: center;
  padding-left: 9px;
  padding-top: 17px;
  margin-bottom: 10px;
  .item {
    span {
      margin-right: 6px;
      height: 12px;
      font-size: 12px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #555555;
    }
    display: flex;
    align-items: center;
    margin-right: 20px;
  }
}
.nd-table-box {
  width: 1066px;
  height: 383px;
  padding-left: 9px;
  margin-bottom: 25px;
}

.primary {
  width: 73px;
}
</style>