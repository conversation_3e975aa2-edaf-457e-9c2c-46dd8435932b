<template>
  <div class="main">
    <div class="main-box">
      <div class="top-box">
        <div class="left">
          <div class="title-text">挂牌项目</div>
          <img src="@/assets/detail/title-icon.png" alt="" srcset="" />
        </div>
        <div class="more-text" @click="toMore">查看更多>></div>
      </div>
      <div class="bot-box" v-if="list.length !== 0">
        <div class="list-box" v-for="(item, index) in list" :key="index" @click="goDetail(item)">
          <div
            class="list-top"
            :style="{
              backgroundImage: item.proPicPath
                ? `url('${item.proPicPath}')`
                : `url('${defaultImgUrl}')`,
            }"
          >
            <div
              style="background-color: #ed911f"
              v-if="item.xmStatus === '2' || item.xmStatus === '6'"
              class="list-tag"
            >
              <span>报名中</span>
            </div>
            <!-- <div
              style="background-color: #f43d3d"
              v-if="item.xmStatus === '4' || item.xmStatus === '6'"
              class="list-tag"
            >
              <span>竞价中</span>
            </div>
            <div style="background-color: #0dc58b" v-if="item.xmStatus === '5'" class="list-tag">
              <span>已成交</span>
            </div> -->

            <div class="project-code">
              <div class="project-text">项目编号：{{ item.proCode }}</div>
            </div>
          </div>
          <div class="list-bot">
            <div class="text1">
              {{ item.projectName ?  item.projectName.length > 10 ? item.projectName.slice(0,10) + '...' : item.projectName : ""}}{{ item.dataSource == 1 && item.tenderCode ? ' - 标段' + item.tenderCode : '' }}
            </div>
            <div class="text-cont text-cont1">
              <img class="gpxm-img" src="@/assets/detail/gpjg.png" alt="" />
              <div class="text2">挂牌价格：</div>
              <div class="text3">
                <span class="text-num">{{ item.upsetLower }}</span>
                <span class="text-unit">{{ item.upsetLowerDw }}</span>
              </div>
            </div>
            <div class="text-cont text-cont2">
              <img class="gpxm-img" src="@/assets/detail/cqlx.png" alt="" />
              <div class="text2">产权类型：</div>
              <div class="text3">{{ item.childTradeVarietyName }}</div>
            </div>
            <div class="text-cont text-cont2">
              <img class="gpxm-img" src="@/assets/detail/gpsj.png" alt="" />
              <div class="text2">挂牌时间：</div>
              <div class="text3">{{ item.annoTime }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bot-box" v-else>
        <empty :boxHeight="300"></empty>
      </div>
    </div>
  </div>
</template>

<script>
import ndRadio from '@/components/ndRadio.vue';
import ndSelect from '@/components/ndSelect.vue';
import empty from '@/views/empty/index.vue';

export default {
  components: {
    ndRadio,
    ndSelect,
    empty,
  },
  name: 'home',
  data() {
    return {
      defaultImgUrl: window.ipConfig.defaultImg,
      currentNodekey: '',
      nodeName: '', //地区
      jypz: '', //交易品种id
      navArr: [
        // {
        //   title: '预公告',
        //   url: require('@/assets/detail/ygg.png'),
        //   noUrl: require('@/assets/detail/acygg.png'),
        // },
        {
          title: '报名中',
          url: require('@/assets/detail/bmz.png'),
          noUrl: require('@/assets/detail/acbmz.png'),
          iconUrl: require('@/assets/detail/bao.png'),
          order: '2',
        },
        {
          title: '竞价中',
          url: require('@/assets/detail/jjz.png'),
          noUrl: require('@/assets/detail/acjjz.png'),
          iconUrl: require('@/assets/detail/jing.png'),
          order: '4',
        },
        // {
        //   title: '已成交',
        //   url: require('@/assets/detail/ycj.png'),
        //   noUrl: require('@/assets/detail/acycj.png'),
        // },
      ],
      activeNav: '',
      list: [], //初始化数据列表
      // 地区筛选
      nodeName: '',
      unitId: '',
      level:'',
      allPath: '',
      allPathCopy: '',
      defaultProps: {
        id: 'id',
        level: 'jiBie',
        label: 'name',
        children: 'children',
        isLeaf: 'isLeaf',
      },
      treeExpandIdList: [],
      // 地区筛选

      options: [], //交易品种下拉选项
      areaId: '',
      // search查询 数组
      formList: {
        // 项目地区
        projectAreaList: [],
        // 项目地区2
        projectAreaList2: [],
        // 项目地区3
        projectAreaList3: [],
      },
      form: {
        projectArea: '', // 选中 项目地区
        projectArea2: '', // 选中 项目地区2
        projectArea3: '', // 选中 项目地区3
        projectStatus: 2,
      },
      activeIndex: 1,
    };
  },
  mounted() {
    this.getProType();
    this.getProjectArea();

    // this.$bus.$on('refreshList', (data) => {
    // debugger

    //   this.unitId = data.unitId;
    //   this.level = data.level
    //   this.getData();
    // });
    this.getData();
  },
  beforeDestroy() {
    // 销毁自定义事件
    this.$bus.$off(['refreshList']);
  },
  methods: {
    changeTab(th, status) {
      this.activeIndex = th;
      this.form.projectStatus = status;
      this.getData();
    },
    radioChange(item) {
      console.log(item);
      this.areaId = this.form.projectArea3;
      this.form.level = item.level;
      this.getData();
    },
    // 项目地区切换
    changeArea(item, flag) {
      // this.areaId=item.id;
      if (flag == 1) {
        if (item.id) {
          this.areaId = item.id;
        } else {
          this.areaId = '';
        }
        this.form.projectArea2 = '';
        this.form.projectArea3 = '';
        this.form.projectArea = item.id;
        this.form.level = item.level;
        this.formList.projectAreaList2 = this.getProjectArea(item.id, 2);
      } else if (flag == 2) {
        if (item.id) {
          this.areaId = this.form.projectArea;
        } else {
          this.areaId = '';
        }
        this.form.projectArea3 = '';
        this.form.projectArea2 = item.id;
        this.form.level = item.level;
        this.formList.projectAreaList3 = this.getProjectArea(item.id, 3);
      } else if (flag == 4) {
        if (item.id) {
          this.areaId = this.form.projectArea;
        } else {
          this.areaId = '';
        }
        this.form.projectArea3 = '';
        this.form.projectArea2 = item.id;
        this.form.level = item.level;
        // this.formList.projectAreaList3 = this.getProjectArea(item.id, 3);
      } else {
        if (item.id) {
          this.areaId = this.form.projectArea2;
        } else {
          this.areaId = '';
        }
        this.form.projectArea3 = item.id;
        this.form.level = item.level;
      }
      this.getData();
    },
    // 获取项目地区的接口
    getProjectArea(areaId, flag) {
      if (areaId) {
        this.$ajax({
          method: 'get',
          url: '/area/webServiceLazyLoadRegionTree',
          data: {
            areaId: areaId,
          },
        }).then((res) => {
          if (res.data.code == 200) {
            // console.log('地区树的接口', res.data.data);
            if (flag == 2)
              this.formList.projectAreaList2 = [{ id: '', name: '全部' }, ...res.data.data];
            if (flag == 3) this.formList.projectAreaList3 = res.data.data;
          }
        });
      } else {
        this.$ajax({
          method: 'get',
          url: '/area/webServiceLazyLoadRegionTree',
        }).then((res) => {
          if (res.data.code == 200) {
            // console.log('地区树的接口', res.data.data);
            this.formList.projectAreaList = [
              { id: '', name: '全部' },
              ...res.data.data[0].children,
            ];
          }
        });
      }
    },
    toMore() {
      //更多
      // this.$router.push('projectInformationView?type=0');
      this.$router.push('publicInformationView?type=0');
    },
    // 跳转 详情
    goDetail(item) {
        if (item.dataSource == 2) {
          return window.open(item.projectLink);
        }
      let query = [];
      let type;
      query = [{ name: '首页', route: '/' }];
      if (item.xmStatus == 2) type = 1;
      if (item.xmStatus == 4) type = 2;
      // this.$router.push({ name: "details", query: { id: item.tendersId, title: item.proName, route: JSON.stringify(query), type: type } })
      if (window.location.origin == 'http://localhost:8080') {
        window.open(
          window.location.origin +
            `/#/details?id=${item.tendersId}&route=${JSON.stringify(query)}&type=0&homeType=2&noticeType=0`,
        );
      } else {
        window.open(
          window.location.origin +
            window.location.pathname +
            `#/details?id=${item.tendersId}&route=${JSON.stringify(query)}&type=0&homeType=2&noticeType=0`,
        );
      }
    },
    // 获取交易品种列表
    getData() {
      this.$ajax({
        url: '/notice/noticeInfoV2',
        method: 'post',
        data: {
          unitId: this.unitId, //地区id
          level:this.level,
          fromHome: '1',
          proTypeId: '',
          proTypeParentId: this.jypz,
          page: 1,
          size: 8,
          noticeType: '0',
          xmStatus: this.activeNav, //项目状态
        },
      }).then((res) => {
        console.log(res.data.data.records, '列表数据');
        if (res.data.code === 200) {
          this.list = res.data.data.records;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    getProType() {
      //---初始化首页交易品种
      this.$ajax({
        url: '/baseInfo/webHomeProType',
        method: 'get',
      }).then((response) => {
        if (response.data.code == 200) {
          this.options = [{ proTypeKey: '', proTypeName: '全部' }, ...response.data.data];
        }
      });
    },
    // 节点点击
    handleNodeClick(node) {
      console.log('handleNodeClick', node);
      this.nodeName = node[this.defaultProps.label];
      this.unitId = node[this.defaultProps.id];
      this.level = node.level
      this.allPath = node.allPath;
      this.getData();
      this.treeExpandIdList = [];
      this.$refs.ndSelect.blur();
    },
    handleChange(node) {
      this.jypz = node;
      this.getData();
    },

    navClick(order) {
      this.activeNav = order;
      this.getData();
    },

    // 地区清空
    handleChange2(node) {
      console.log('123131', node);
      this.nodeName = '';
      this.unitId = '';
      this.level = ''
      this.allPath = '';
      this.treeExpandIdList = [];
      this.$refs.ndSelect.blur();
      this.allPath = this.allPathCopy;

      // this.getData();
    },

    // 获取地区树
    // getAreaTree(node, resolve) {
    //   console.log('node:', node);
    //   if (node.level === 0) {
    //     this.$ajax({
    //       method: 'get',
    //       url: '/baseInfo/webHomeAreaTreeVue',
    //       data: {
    //         showChild: 0,
    //       },
    //     }).then((res) => {
    //       if (res.data.code == 200) {
    //         console.log('地区树的接口', res.data.data);
    //         this.nodeName = res.data.data[0].unitName;
    //         this.unitId = res.data.data[0].unitId;
    //         this.allPath = res.data.data[0].allPath;
    //         this.allPathCopy = res.data.data[0].allPath;
    //         // console.log('this.unitId:', this.unitId);

    //         // return resolve([{
    //         //   unitId: "",
    //         //   name: "全部",
    //         //   isLeaf: true,
    //         // }, ...res.data.data]);
    //         return resolve(res.data.data);
    //       }
    //     });
    //   } else {
    //     this.$ajax({
    //       method: 'get',
    //       url: '/baseInfo/webHomeAreaTreeVue',
    //       data: {
    //         showChild: 1,
    //         unitId: node.data.unitId,
    //       },
    //     }).then((res) => {
    //       if (res.data.code == 200) {
    //         console.log('地区树的接口', res.data.data);
    //         if (node.level === 1) {
    //           this.nodeName = res.data.data[0].unitName;
    //           this.unitId = res.data.data[0].unitId;
    //           this.allPath = res.data.data[0].allPath;
    //           this.allPathCopy = res.data.data[0].allPath;

    //           this.getData();
    //         }
    //         return resolve(res.data.data);
    //       }
    //     });
    //   }
    // },

    // 获取地区树
    getAreaTree(node, resolve) {
      console.log('node:', node);
      if (node.level === 0) {
        this.$ajax({
          method: 'get',
          url: '/area/webServiceLazyLoadRegionTree',
          data: {},
        }).then((res) => {
          if (res.data.code === 200) {
            console.log('地区树的接口', res.data.data);
            this.nodeName = res.data.data[0].name;
            this.unitId = res.data.data[0].id;
            this.level = res.data.data[0].level;
            this.allPath = res.data.data[0].allPath;
            this.allPathCopy = res.data.data[0].allPath;
            this.currentNodekey = res.data.data[0].id;
            this.$nextTick(function () {
              this.$refs.tree.setCurrentKey(this.currentNodekey);
            });
            this.getData();

            return resolve(res.data.data);
          }
        });
      } else {
        this.$ajax({
          method: 'get',
          url: '/area/webServiceLazyLoadRegionTree',
          data: {
            areaId: node.data.id,
          },
        }).then((res) => {
          if (res.data.code == 200) {
            console.log('地区树的接口', res.data.data);
            // if (node.level === 1) {
            //   this.nodeName = res.data.data[0].name;
            //   this.unitId = res.data.data[0].id;
            //   this.allPath = res.data.data[0].allPath;
            //   this.allPathCopy = res.data.data[0].allPath;
            //   this.getData();
            // }
            return resolve(res.data.data);
          }
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  padding: 30px 0 10px;
  // padding: 0px 310px;
  background: #fff;
  display: flex;
  justify-content: center;

  .main-box {
    width: 1300px;

    .top-box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      user-select: none;
      .left {
        display: flex;
        flex-direction: column;
        height: 32px;

        .title-text {
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 24px;
          color: #333333;
          line-height: 24px;
        }
      }

      .more-text {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 24px;
        cursor: pointer;
      }
    }

    // .bot-box:after {
    //   content: '';
    //   width: 310px;
    // }
    .bot-box {
      width: 1300px;
      display: flex;
      align-items: center;
      // justify-content: space-between;
      align-content: flex-start;
      flex-wrap: wrap;
      // row-gap: 30px;
      // column-gap: 20px;
      .nav-box {
        height: 383px;
        width: 310px;
        background-image: url('@/assets/detail/jyggbg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        // row-gap: 33px;
        border-radius: 8px;
        border: 1px solid #d4f5fa;
        .nav-item {
          width: 190px;
          height: 56px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          .nav-img {
            display: flex;
            align-items: center;
            margin-right: 10px;

            .img-pic {
              height: 20px;
              width: 20px;
            }
          }

          .nav-title {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 18px;
          }
        }

        .active-nav {
          color: #ffffff;
          background-image: url('@/assets/detail/acBg.png');
          background-size: 100% 100%;
          background-repeat: no-repeat;
        }

        .deactive-nav {
          color: #10aec2;
          background-image: url('@/assets/detail/noacBg.png');
          background-size: 100% 100%;
          background-repeat: no-repeat;
        }
      }

      .list-box:not(:nth-child(4n)) {
        margin-right: 20px;
      }

      // .list-box:not(:nth-last-child(1)) {
      //   margin-bottom: 30px;
      // }

      .list-box {
        // height: 337px;
        width: 310px;
        // border: 1px solid #d4f5fa;
        cursor: pointer;
        margin-bottom: 30px;

        .list-top:hover {
          background-size: 120%; /* 放大1.2倍 */
        }
        .list-top {
          // background-image: url('@/assets/detail/jyggBg2.png');
          background-size: 100%;
          height: 226px;
          width: 100%;
          position: relative;
          margin-bottom: 14px;
          display: flex;
          align-items: flex-end;
          border-radius: 8px;

          .project-code {
            width: 100%;
            height: 40px;
            background: rgba(61, 61, 61, 0.8);
            border-radius: 0px 0px 8px 8px;
            display: flex;
            align-items: center;
            padding-left: 10px;

            .project-text {
              width: 99%;
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 16px;
              color: #ffffff;
              line-height: 36px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .list-tag {
            // width: 60px;
            height: 34px;
            color: #ffffff;
            display: flex;
            // justify-content: center;
            align-items: center;
            position: absolute;
            right: 0px;
            top: 0px;
            border-radius: 0px 8px 0px 8px;
            padding: 0px 14px;

            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            line-height: 24px;
          }
        }

        .list-bot {
          width: 100%;
          padding: 0 10px;
          .text1 {
            font-family: Microsoft YaHei;
            font-weight: bold;
            font-size: 18px;
            color: #333333;
            // line-height: 36px;
            margin-bottom: 14px;
            white-space: nowrap;
            // display: -webkit-box;
            // -webkit-line-clamp: 1;
            // -webkit-box-orient: vertical;
            // overflow: hidden;
            // text-overflow: ellipsis;
          }

          .text-cont1 {
            height: 15px;
          }
          .text-cont2 {
            height: 13px;
          }

          .text-cont:nth-child(-n + 3) {
            margin-bottom: 13px;
          }
          .text-cont {
            display: flex;
            flex-direction: row;
            align-items: center;

            .gpxm-img {
              margin-right: 9px;
              width: 17px;
              height: 16px;
            }

            .text2 {
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 14px;
              color: #666666;
              // line-height: 24px;
              white-space: nowrap;
            }

            .text3 {
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 14px;
              color: #666666;
              // line-height: 24px;
              // width: 90%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;

              .text-num {
                font-family: Microsoft YaHei;
                font-weight: bold;
                font-size: 18px;
                color: #ed911f;
              }

              .text-unit {
                font-family: Microsoft YaHei;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                line-height: 24px;
              }
            }
          }
        }
      }
    }
  }
}
.more-text:hover{
  color: #ed911f!important;
}
</style>
<!-- 
<style>
/* .el-input__inner{
    border: none;
  } */
*::-webkit-scrollbar {
  width: 10px;
  height: 10px !important;
  width: 6px;
  height: 12px !important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 12px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}

.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 230px;
  /* padding: 0; */
  padding: 0 10px;
  overflow: hidden;
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
  color: #10aec2;
}

ul li >>> .el-tree .el-tree-node__content:hover {
  background-color: #e8f7ff;
}

ul li >>> .el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
  background-color: #ffffff;
}

.el-tree-node__label {
  font-weight: normal;
  margin-right: 10px;
  font-size: 12px;
}

.el-tree .is-current .el-tree-node__label {
  color: #10aec2;
  font-weight: 700;
}

.el-tree .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}

.el-select-dropdown__item.hover {
  background: transparent;
}

.el-select-dropdown__item span {
  margin-left: 10px;
}

::v-deep .el-scrollbar__bar.is-horizontal {
  height: 0px;
}

::v-deep .el-scrollbar__bar.is-vertical {
  width: 0px;
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #c8ecff;
}

.el-select-dropdown__item span {
  margin-left: 0px;
}

.el-scrollbar .el-scrollbar__view .text-hover:hover {
  background: #e8f7ff;
}

/* ::v-deep .el-scrollbar .el-scrollbar__bar.is-vertical .el-scrollbar__thumb {
  background: #D4E6FB !important;
} */
::v-deep .is-vertical .el-scrollbar__thumb {
  background: #d4e6fb !important;
}

::v-deep .el-scrollbar__bar.is-vertical {
  background-color: #f2f7ff !important;
}
</style> -->