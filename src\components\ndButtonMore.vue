<template>
  <div class="nd-button-more-box">
    <el-popover placement="bottom" trigger="click" popper-class="nd-button-more-popper">
      <slot />
      <nd-button slot="reference">
        <span class="text">更多</span><i class="el-icon-arrow-down" />
      </nd-button>
    </el-popover>
  </div>
</template>

<script>
import ndButton from "./ndButton.vue";
export default {
  components: {
    ndButton,
  },
  data() {
    return {
      // visible: false,
    };
  },
  methods: {
  }
};
</script>

<style>
.nd-button-more-popper {
  padding: 2px;
  min-width: 125px;
}
</style>
<style scoped lang="scss">
.nd-button-more-box {
  width: auto;
  height: auto;
  .text {
    margin-right: 6px;
  }
}
.nd-button-box + .nd-button-more-box {
  margin-left: 10px;
}
</style>