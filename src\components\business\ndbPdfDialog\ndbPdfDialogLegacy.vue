<template>
  <nd-dialog ref="dialog" title="PDF预览" center height="70vh" width="800px" :hidden-y-scroll="true" append-to-body>
    <div class="nd-pdf-dialog-ie-box">
      <iframe :src="src" class="pdf-viewer" />
    </div>
    <template #footer>
      <nd-button @click="exportPdf" v-show="displayExport">
        导出
      </nd-button>
      <nd-button @click="close">
        关闭
      </nd-button>
    </template>
  </nd-dialog>
</template>
<script>
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
export default {
  props: {
    displayExport: {//是否显示导出按钮
      type: Boolean,
      default: false,
    },
    documentId: {//文书id
      type: String,
      default: "",
    },
  },
  components: {
    ndDialog,
    ndButton
  },
  data() {
    return {
      src: "", // iframe的地址
      webUrl: "pdfViewer.html?url=",
    };
  },
  mounted() {

  },
  methods: {
    // 打开
    open(src) {
      this.$refs.dialog.open();
      this.$nextTick(() => {
        // this.src = "pdfViewer.html?url=http://************:9990/cqjy5q/login.do?method=reViewPdf&id=297edff88498899b018498fee6840033"
        this.src = this.webUrl + encodeURI(src);
      });
    },
    // 导出
    exportPdf() {
      let params = {
        docId: this.documentId,//文书id
      }
      this.$ajax({
        method: "GET",
        url: "" + "/resources/download",
        data: params,
        serverName: "nd-village",
        responseType: "blob",
      }).then((res) => {
        if (!res) return;
        this.getPdfName(res.data)
      }).catch((err) => {
        console.log(err);
      });
    },
    getPdfName(response) {
      let params = {
        docId: this.documentId,//文书id
      }
      this.$ajax({
        method: "GET",
        url: "" + "/resources/downloadName",
        data: params,
        serverName: "nd-village",
      }).then((res) => {
        let pdfName=""
        if (res.data.code === 200) {
          pdfName = res.data.data
        } else {
          pdfName = "获取文书名称失败.pdf"
        }
        const blob = new Blob([response], {
          type: "application/pdf;chartset=UTF-8",
        }); // 构造一个blob对象来处理数据，并设置文件类型
        if (window.navigator.msSaveOrOpenBlob) {
          //兼容IE10
          navigator.msSaveBlob(blob, pdfName);
        } else {
          const URL = window.URL || window.webkitURL
          const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
          const a = document.createElement("a"); //创建a标签
          a.style.display = "none";
          a.href = href; // 指定下载链接
          a.setAttribute("download", pdfName);
          //a.download = this.filename; //指定下载文件名
          a.click(); //触发下载
          window.URL.revokeObjectURL(a.href); //释放URL对象
        }
      })
    },
    // 关闭
    close() {
      this.$refs.dialog.close();
    },
  }
};
</script>
<style lang="scss" scoped>
.nd-pdf-dialog-ie-box {
  width: 100%;
  height: 100%;

  .pdf-viewer {
    width: 100%;
    height: 100%;
  }
}
</style>