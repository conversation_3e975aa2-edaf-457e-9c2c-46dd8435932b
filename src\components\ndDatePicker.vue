<template>
  <div class="nd-date-picker-box" :style="{ width: width }">
    <el-date-picker ref="datePicker" v-bind="$attrs" v-on="$listeners" @focus="focusHandler()" />
  </div>
</template>

<script>
export default {
  props: {
    width: {
      type: String,
      default: "220px",
    },
  },
  data() {
    return {
    };
  },
  mounted() {
  },
  methods: {
    // 关闭
    close() {
      this.$refs.datePicker.pickerVisible = false;
    },
    // 输入框获得焦点时
    focusHandler() {
      // 注册双击事件
      this.$nextTick(() => {
        let elDateTable = document.getElementsByClassName("el-date-table");
        if (elDateTable.length > 0) {
          //取最后一个日期控件的元素
          let elDateTableElement = elDateTable[elDateTable.length - 1];
          elDateTableElement.addEventListener("dblclick", this.close);
        }
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.nd-date-picker-box {
  ::v-deep .el-input__inner {
    width: 100%;
    height: 28px;
    line-height: 28px;
    font-size: 12px;
  }

  ::v-deep .el-date-editor.el-input {
    width: 100%;
  }

  ::v-deep .el-range-input {
    font-size: 12px;
  }

  ::v-deep .el-input__icon {
    line-height: 100%;
  }

  ::v-deep .el-range-separator {
    width: auto;
    height: auto;
    line-height: normal;
    font-size: 12px;
  }
}</style>