<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,user-scalable=yes,initial-scale=0.2,shrink-to-fit=no">
  <meta name="武汉产权门户" src="/leadingEnd/whcq-mh" version="2025-06-25 20:00">
  <title>武汉农村综合产权交易所</title>
  <script>document.write('<script src="./config.js?t=' + new Date().getTime() + '"><\/script>')</script>
  <style>
    html,
    body,
    div,
    span,
    applet,
    object,
    iframe,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    blockquote,
    pre,
    a,
    abbr,
    acronym,
    address,
    big,
    cite,
    code,
    del,
    dfn,
    em,
    img,
    ins,
    kbd,
    q,
    s,
    samp,
    small,
    strike,
    strong,
    sub,
    sup,
    tt,
    var,
    b,
    u,
    i,
    center,
    dl,
    dt,
    dd,
    ol,
    ul,
    li,
    fieldset,
    form,
    label,
    legend,
    table,
    caption,
    tbody,
    tfoot,
    thead,
    tr,
    th,
    td,
    article,
    aside,
    canvas,
    details,
    embed,
    figure,
    figcaption,
    footer,
    header,
    menu,
    nav,
    output,
    ruby,
    section,
    summary,
    time,
    mark,
    audio,
    video,
    input {
      margin: 0;
      padding: 0;
      border: 0;
      box-sizing: border-box;
      font-family: "微软雅黑";
    }

    html {
      width: 100%;
      height: 100%;
      scroll-padding-top: 48px;
    }

    body {
      width: 100%;
      height: 100%;
    }
  </style>
</head>

<body>
  <div id="app"></div>
</body>

</html>