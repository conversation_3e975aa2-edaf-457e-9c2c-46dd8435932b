<!-- 帮助中心列表 -->
<template>
  <div class="wrap-container">
    <div style="position: sticky; top: 0; z-index: 9">
      <tabs v-model="currentTab" :tab-names="tabNames" />
    </div>
    <!-- 交易须知 -->
    <!-- <list v-show="currentTab == 0" requestUrl="/bzzx/zlxz/"></list> -->
    <div v-show="currentTab == 0" v-html="jyxz" style="padding-top: 30px"></div>
    <!-- 交易流程  -->
    <div v-show="currentTab == 1">
      <div class="arrow-img-box">
        <div
          v-for="(item, index) in arrows"
          :key="index"
          class="arrow-item"
          @click="linkTo(item, index)"
        >
          <img style="width: 62px; height: 58px; margin-bottom: 7px" :src="item.url" />
          <span>{{ item.txt }}</span>
          <img
            v-if="index !== arrows.length - 1"
            class="arrow"
            src="@/img/list-detail/step-arrow.png"
            alt="arrow"
          />
        </div>
      </div>
      <div class="arrow-img-box" style="height: auto; border: none">
        <!-- <img src="@/img/list-detail/process-img.png" alt="" /> -->
        <!-- <img :src="stepImg" alt="交易流程图" /> -->
        <div v-html="stepImg"></div>
      </div>
    </div>
    <!-- 交易规则-->
    <list v-show="currentTab == 2" requestUrl="/zcfg/jygz/"></list>
    <!-- 交易帮助-->
    <!-- <div  v-show="currentTab == 2" v-html="lxwm"></div> -->
    <div style="padding-top: 10px" v-show="currentTab == 3">
      <div class="tab3-box">
        <div class="content-box">
          <!-- <div class="moudle-name">交易帮助</div> -->
          <div class="content-item" :id="item.anhor" v-for="(item, index) in nav" :key="index">
            <!-- <div class="item-title">{{ index + 1 + '、' + item.txt }}</div> -->
            <div v-html="item.content"></div>
          </div>
        </div>
        <div class="nav-box">
          <div
            v-for="(item, index) in nav"
            :key="index"
            class="nav-item"
            :class="index == stepIndex ? 'nav-act' : ''"
            @click="linkTo(item, index)"
          >
            <img :src="index == stepIndex ? item.actUrl : item.url" />
            <span> {{ item.txt }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 文书下载-->
    <list showDownLoad v-show="currentTab == 4" requestUrl="/bzzx/wsxz/"></list>
  </div>
</template>
<script>
// 获取url参数
const getQueryVariable = (variable) => {
  let geturl = window.location.href;
  let getqyinfo = geturl.split('?')[1];
  let getqys = new URLSearchParams('?' + getqyinfo);
  return getqys.get(variable);
};
let midTab = 0;
let item = {};
let stepMidIndex = 0;
import tabs from '@/views/newsListView/components/tabs.vue';
import list from '@/views/newsListView/components/list.vue';
import { TimeSelect } from 'element-ui';
export default {
  components: {
    tabs,
    list,
  },
  data() {
    return {
      currentTab: 0,
      tabNames: [
        { name: '交易须知' },
        { name: '交易流程' },
        { name: '交易规则' },
        { name: '交易帮助' },
        { name: '文本下载' },
      ],
      jylc: '',
      jyxz: '',
      stepImg: '',
      stepIndex: 0,
      arrows: [
        { url: require('@/img/list-detail/step1.png'), txt: '交易准备', anhor: 'step1' },
        { url: require('@/img/list-detail/step2.png'), txt: '交易申请', anhor: 'step2' },
        { url: require('@/img/list-detail/step3.png'), txt: '信息公告', anhor: 'step3' },
        { url: require('@/img/list-detail/step4.png'), txt: '交易报名', anhor: 'step4' },
        { url: require('@/img/list-detail/step5.png'), txt: '网上竞价', anhor: 'step5' },
        { url: require('@/img/list-detail/step6.png'), txt: '竞价结果公示', anhor: 'step6' },
        { url: require('@/img/list-detail/step7.png'), txt: '合同签约', anhor: 'step7' },
        { url: require('@/img/list-detail/step8.png'), txt: '缴纳价款', anhor: 'step8' },
        { url: require('@/img/list-detail/step9.png'), txt: '标的交割', anhor: 'step9' },
        { url: require('@/img/list-detail/step10.png'), txt: '价款结转', anhor: 'step10' },
        { url: require('@/img/list-detail/step11.png'), txt: '交易凭证', anhor: 'step11' },
      ],
      nav: [
        {
          url: require('@/img/list-detail/step1_1.png'),
          actUrl: require('@/img/list-detail/step1_1_act.png'),
          txt: '交易准备',
          anhor: 'step1',
          content: '',
        },
        {
          url: require('@/img/list-detail/step2_2.png'),
          actUrl: require('@/img/list-detail/step2_2_act.png'),
          txt: '交易申请',
          anhor: 'step2',
          content: '',
        },
        {
          url: require('@/img/list-detail/step3_3.png'),
          actUrl: require('@/img/list-detail/step3_3_act.png'),
          txt: '信息公告',
          anhor: 'step3',
          content: '',
        },
        {
          url: require('@/img/list-detail/step4_4.png'),
          actUrl: require('@/img/list-detail/step4_4_act.png'),
          txt: '交易报名',
          anhor: 'step4',
          content: '',
        },
        {
          url: require('@/img/list-detail/step5_5.png'),
          actUrl: require('@/img/list-detail/step5_5_act.png'),
          txt: '网上竞价',
          anhor: 'step5',
          content: '',
        },
        {
          url: require('@/img/list-detail/step6_6.png'),
          actUrl: require('@/img/list-detail/step6_6_act.png'),
          txt: '竞价结果公示',
          anhor: 'step6',
          content: '',
        },
        {
          url: require('@/img/list-detail/step7_7.png'),
          actUrl: require('@/img/list-detail/step7_7_act.png'),
          txt: '合同签约',
          anhor: 'step7',
          content: '',
        },
        {
          url: require('@/img/list-detail/step8_8.png'),
          actUrl: require('@/img/list-detail/step8_8_act.png'),
          txt: '缴纳价款',
          anhor: 'step8',
          content: '',
        },
        {
          url: require('@/img/list-detail/step9_9.png'),
          actUrl: require('@/img/list-detail/step9_9_act.png'),
          txt: '标的交割',
          anhor: 'step9',
          content: '',
        },
        {
          url: require('@/img/list-detail/step10_10.png'),
          actUrl: require('@/img/list-detail/step10_10_act.png'),
          txt: '价款结转',
          anhor: 'step10',
          content: '',
        },
        {
          url: require('@/img/list-detail/step11_11.png'),
          actUrl: require('@/img/list-detail/step11_11_act.png'),
          txt: '交易凭证',
          anhor: 'step11',
          content: '',
        },
      ],
      ifLoad: false,
    };
  },
  beforeRouteEnter(to, from, next) {
    next((_vm) => {
      _vm.currentTab = getQueryVariable('bigType') ? getQueryVariable('bigType') - 0 : 0;
      console.log(_vm.currentTab);
      _vm.stepIndex = getQueryVariable('sType') || 0;
      if (_vm.currentTab == 3) {
        midTab = _vm.currentTab;
        item = _vm.nav[_vm.stepIndex];
        stepMidIndex = _vm.stepIndex;
        _vm.linkTo(_vm.nav[_vm.stepIndex], _vm.stepIndex);
      }
    });
  },
  watch: {
    currentTab(val) {
      try {
        this.$router.replace({ path: 'helpCenter', query: { bigType: val - 0 } });
      } catch (error) {}
    },
  },
  beforeUpdate() {
    if (midTab == 3 && !this.ifLoad) {
      this.linkTo(item, stepMidIndex);
    }
  },
  mounted() {
    this.getdataByCurrentTab1();
    this.getTabItens();
    this.getStepImg();

    // this.getStep1();

    // window.onscroll = function () {
    //   let footer = document.getElementsByClassName('footer');
    //   const rect = footer[0].getBoundingClientRect();
    //   const distanceToBottom = window.innerHeight - rect.bottom;

    //   let navBox = document.getElementsByClassName('nav-box');
    //   if (distanceToBottom < 0 && navBox[0]) {
    //     let d = distanceToBottom * -1;
    //     if (d < 225) {
    //       navBox[0].style.bottom = 225 - d + 150 + 20 + 'px';
    //     } else {
    //       navBox[0].style.bottom = '150px';
    //     }
    //   }
    // };
  },
  methods: {
    getdataByCurrentTab1() {
      this.$ajax({
        url: '/bzzx/zlxz/',
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        if (res.status === 200) {
          this.getHtmlCode(res.data, 'jyxz');
        }
      });
    },
    getdataByCurrentTab3() {
      this.$ajax({
        url: '/bzzx/lxwm/',
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        if (res.status === 200) {
          this.getHtmlCode(res.data, 'lxwm');
        }
      });
    },

    getStepImg() {
      this.$ajax({
        url: '/bzzx/jylc/',
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        if (res.status === 200) {
          this.$ajax({
            url: this.getPathFromUrl(res.data),
            method: 'get',
            serverName: 'nd-ss',
          }).then((res2) => {
            let data = res2.data.replaceAll(
              '<img alt="" src="',
              `<img style="max-width:1198px;display: block;" src="${window.ipConfig.imgUrl}`,
            );
            this.stepImg = data;
          });
        }
      });
    },

    getStep(url, index = 0) {
      this.$ajax({
        url,
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        if (res.status === 200) {
          this.$ajax({
            url: this.getPathFromUrl(res.data),
            method: 'get',
            serverName: 'nd-ss',
          }).then((res) => {
            if (res.status === 200) {
              let data = res.data.replaceAll(
                '<img alt="" src="',
                `<img style="max-width:1078px;display: block;" src="${window.ipConfig.imgUrl}`,
              );
              this.nav[index].content = data;
            }
          });
        }
      });
    },

    getTabItens() {
      let urls = [
        '/bzzx/lxwm/jyzb/',
        '/bzzx/lxwm/jysq/',
        '/bzzx/lxwm/xxgg/',
        '/bzzx/lxwm/jybm/',
        '/bzzx/lxwm/dzjj/',
        '/bzzx/lxwm/jjjggs/',
        '/bzzx/lxwm/htqy/',
        '/bzzx/lxwm/jnjk/',
        '/bzzx/lxwm/bdjg/',
        '/bzzx/lxwm/jkjz/',
        '/bzzx/lxwm/jypz/',
      ];
      urls.forEach((item, index) => {
        this.getStep(item, index);
      });
    },

    getHtmlCode(html, field) {
      this.$ajax({
        url: this.getPathFromUrl(html),
        method: 'get',
        serverName: 'nd-ss',
      }).then((res) => {
        if (res.status === 200) {
          let data = res.data.replaceAll(
            '<img alt="" src="',
            `<img style="max-width:1078px;display: block;" src="${window.ipConfig.imgUrl}`,
          );
          this[field] = data;
        }
      });
    },
    // 截取url中除去协议域名端口号后的内容
    getPathFromUrl(url) {
      const regex = /\/\/[^/]+(.+)/;
      const match = url.match(regex);
      return match ? match[1] : null;
    },

    linkTo(item, index) {
      this.currentTab = 3;
      this.stepIndex = index;
      this.$nextTick(() => {
        const scrollDom = document.getElementById(item.anhor);
        scrollDom.scrollIntoView();
        if (scrollDom.firstChild.firstChild) {
          this.ifLoad = true;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.nav-act {
  background: #316c2c;
  border-radius: 10px;
  color: #fff !important;
}
.wrap-container {
  min-width: 1000px;
  width: 100%;
  overflow: auto;
  height: 100%;
  background: #fff;
  position: relative;
  padding-bottom: 20px;
}
.arrow-img-box {
  width: 1200px;
  height: 145px;
  border: 1px solid #dedede;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  margin: 0 auto;
  margin-top: 20px;
  .arrow-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: #333;
    position: relative;
    padding-right: 13px;
    cursor: pointer;
    .arrow {
      position: absolute;
      right: -13px;
      // top: 50%;
      width: 11px;
      height: 9px;
      transform: translateY(-9px);
    }
  }
}
.tab3-box {
  display: flex;
  width: 1200px;
  margin: 0 auto;
  position: relative;
  .nav-box {
    width: 220px;
    height: 480px;
    background: #f7f7f7;
    padding: 20px;
    position: fixed;
    right: 2%;
    top: 50%;
    transform: translateY(-45%);
    z-index: 99;
    border-radius: 10px;
    .nav-item {
      display: flex;
      align-items: center;
      width: 188px;
      height: 40px;
      margin: 0 auto;
      text-align: center;
      color: #666;
      font-size: 16px;
      padding-left: 30px;
      cursor: pointer;
      img {
        max-width: 20px;
        max-height: 20px;
        margin-right: 18px;
      }
    }
  }
  .content-box {
    flex: 1;
    padding-top: 10px;
    .moudle-name {
      text-align: center;
      color: #333;
      font-size: 28px;
      font-weight: bold;
    }
    .content-item {
      // border: 1px solid #eee;
      margin: 20px 20px 0;
      .item-title {
        font-size: 16px;
        color: #333333;
        padding: 0 5px;
      }
    }
  }
}
</style>
