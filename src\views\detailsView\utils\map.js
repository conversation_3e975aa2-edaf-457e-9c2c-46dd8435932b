// 百度地图
export function BMapLoader() {
  return new Promise((resolve, reject) => {
    if (window.BMapGL) {
      resolve(window.BMapGL)
    } else {
      let script = document.createElement('script')
      script.type = 'text/javascript'
      // script.src = 'https://api.map.baidu.com/api?v=1.0&type=webgl&callback=initBMap&ak=' + 'RPpiQsDLyHwN7QMwbORMT9Fy' // 南大尚诚ak   
      // script.src = 'https://api.map.baidu.com/api?v=1.0&type=lite&callback=initBMap&ak=' + 'RPpiQsDLyHwN7QMwbORMT9Fy' // 南大尚诚ak
      if (window.location.protocol === 'https:') {
        // script.src = 'https://api.map.baidu.com/api?v=2.0&callback=initBMap&ak=RPpiQsDLyHwN7QMwbORMT9Fy' // 南大尚诚ak
        script.src = 'https://api.map.baidu.com/api?v=1.0&type=webgl&callback=initBMap&ak=RPpiQsDLyHwN7QMwbORMT9Fy' // 南大尚诚ak
      } else {
        // script.src = 'http://api.map.baidu.com/api?v=2.0&callback=initBMap&ak=RPpiQsDLyHwN7QMwbORMT9Fy' // 南大尚诚ak
        script.src = 'http://api.map.baidu.com/api?v=1.0&type=webgl&callback=initBMap&ak=RPpiQsDLyHwN7QMwbORMT9Fy' // 南大尚诚ak
      }
      script.async = true
      script.onerror = reject
      document.head.appendChild(script)
    }
    window.initBMap = function () {
      resolve(window.BMapGL)
    }
  })
}