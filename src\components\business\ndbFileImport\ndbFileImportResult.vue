<template>
  <div>
    <nd-dialog
      ref="addImportResult"
      width="520px"
      height="250px"
      title="查看上次导入结果"
      append-to-body
      :before-close="closeImportResult"
      center
    >
      <div class="center">
        <div v-if="flag" class="item-one">
          <img src="@/img/sigh.png" alt=""><span>导入成功{{ successCount }}条,</span>&nbsp;&nbsp;<span class="high">失败{{ errorCount }}条!</span>
        </div>
        <div v-if="flag1" class="item-one" style="padding-left: 185px">
          <img src="@/img/sigh.png" alt=""><span>成功导入</span>
        </div>
        <div v-if="flag2" class="item-one" style="padding-left: 185px">
          <img src="@/img/sigh.png" alt=""><span>导入失败</span>
        </div>
        <div v-show="isshow" class="item-two">
          请下载失败记录信息,&nbsp;&nbsp;根据失败原因修改后继续上传
        </div>
        <div class="item-three">
          最近导入操作时间:&nbsp;{{ importTime }}
        </div>
      </div>
      <template #footer>
        <nd-button
          v-show="isshow"
          type="primary"
          @click="failureRecord"
        >
          下载失败记录
        </nd-button>
        <nd-button type="normal" @click="continueImport">
          继续导入
        </nd-button>
      </template>
    </nd-dialog>
  </div>
</template>

<script>
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndTable from "@/components/ndTable.vue";
import ndInput from "@/components/ndInput.vue";
export default {
  components: {
    ndDialog,
    ndButton,
    ndTable,
    ndInput,
  },
  props: {
    isSz: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      url: "", //当前项目后台接口请求路径,
      successCount: "", //成功导入条数
      errorCount: "", //失败导入条数
      importTime: "", //导入时间
      isshow: "", //是否需要下载失败记录
      filePathErro: "", //失败记录
      modelType: "", //三资的不同导入模块下载失败记录要用
      flag: false,
      flag1: false,
      flag2: false,
    };
  },

  mounted() {},

  methods: {
     getServerPath() {
      if (typeof getContextPath === "function") {
        console.log("vue获得jsp服务器路径:" + getContextPath());
        return getContextPath();
      } else {
        console.log("vue获得jsp服务器路径有误");
        return "/sz_product_new";
      }
    },
    //打开弹框
    openImportResult(type) {
      this.$refs.addImportResult.open();
      this.modelType = type;
      if (this.isSz) {
        if (type === "21") {
          this.$ajax({
            url: "/szUseWorkPerson/lookLast.do",
            method: "post",
          }).then((res) => {
            if (res.data.code == 0) {
              this.flag = true
              this.successCount = res.data.data.successCount
              this.importTime = res.data.data.importTime;
              this.errorCount = res.data.data.errorCount;
              this.filePathErro = res.data.data.fileUrl;
              if (this.errorCount < 1) {
                this.isshow = false;
              } else {
                this.isshow = true;
              }
              this.$refs.addImportResult.open();
            } else {
            this.$message({
              message: res.data.msg,
              type: "error",
            });
          }
          });
        } else if (type === "13") {
          this.$ajax({
            url: "/pingzgl/getImportResult.do",
            method: "post",
          }).then((res) => {
            if (res.data.code == 0) {
              if (res.data.data.successCount == "1") {
                this.flag1 = true;
              } else {
                this.flag2 = true;
              }
              this.importTime = res.data.data.importTime;
              this.errorCount = res.data.data.errorCount;
              this.filePathErro = res.data.data.fileUrl;
              if (this.errorCount < 1) {
                this.isshow = false;
              } else {
                this.isshow = true;
              }
              this.$refs.addImportResult.open();
            } else {
            this.$message({
              message: res.data.msg,
              type: "error",
            });
          }
          });
        } else if(type === "10" || type === "11" || type === "12"){
          let url = "/zlhtNew.do?method=lookLast"
          if(type == '12'){
            url = "/zyfbNew.do?method=xlsImport"
          }
          let data = type == '12'?{dataType: type}:{type: type}
            this.$ajax({
            url: url,
            method: "post",
            data:data
          }).then((res) => {
            if (res.data.code == 0) {
              this.flag = true
              this.successCount = res.data.data.successCount
              this.importTime = res.data.data.importTime;
              this.errorCount = res.data.data.errorCount;
              this.filePathErro = res.data.data.fileUrl;
              if (this.errorCount < 1) {
                this.isshow = false;
              } else {
                this.isshow = true;
              }
              this.$refs.addImportResult.open();
            } else {
            this.$message({
              message: res.data.msg,
              type: "error",
            });
          }
          });
            //  资金合同导入
        }else if(type === "19"){
               this.$ajax({
            url: "/szOtherContract.do?method=queryLastImportFile",
            method: "post",
            data:{
              dataType:'19'
            }
          }).then((res) => {
            if (res.data.code == 0) {
              this.flag = true
              this.successCount = res.data.data.successCount
              this.importTime = res.data.data.importTime;
              this.errorCount = res.data.data.errorCount;
              this.filePathErro = res.data.data.fileUrl;
              if (this.errorCount < 1) {
                this.isshow = false;
              } else {
                this.isshow = true;
              }
              this.$refs.addImportResult.open();
            } else {
            this.$message({
              message: res.data.msg,
              type: "error",
            });
          }
          });
        }
        else {
          //  资金报账
          this.$ajax({
            method: "post",
            url: this.url + "/apiPayeeManagement/getImportResult.do",
          }).then((res) => {
            if (res.data.code == 0) {
              this.flag=true,
              (this.successCount = res.data.data.successCount),
                (this.errorCount = res.data.data.errorCount),
                (this.importTime = res.data.data.importTime);
              // console.log(this.errorCount);
              if (this.errorCount < 1) {
                this.isshow = false;
              } else {
                this.isshow = true;
              }
              // console.log(this.isshow);
              this.$refs.addImportResult.open();
            } else {
              console.log(res.data.msg);
            }
            // .catch(function () {
            //     this.$message.error("查看上次导入结果失败！");
            //   });
          });
        }
      } else {
        // 贾汪
        this.$ajax({
          method: "get",
          url: "/excel/downloadFailRecord",
          data: {
            type: type,
          },
          serverName: "nd-disciplineSupervision",
        })
          .then((res) => {
            if (200 == res.data.code) {
              this.flag = true;
              this.successCount = res.data.data.numSuccess;
              this.errorCount = res.data.data.numFailure;
              this.importTime = res.data.data.updateTime;
              this.filePathErro = res.data.data.filePathFailure;
              // if (this.errorCount < 1) {
              //   this.isshow = false;
              // } else {
              //   this.isshow = true;
              // }
              this.$refs.addImportResult.open();
            }  else {
            this.$message({
              message: res.data.message,
              type: "error",
            });
          }
          })
          .catch(function () {});
      }
    },
    //关闭
    closeImportResult() {
       this.flag = false;
      this.flag1 = false;
      this.flag2 = false;
      this.successCount = "";
      this.errorCount = "";
      this.importTime = "";
      this.isshow = "";
      this.filePathErro = "";
      this.modelType = "";
      this.$refs.addImportResult.close();
    },
    // 下载失败记录
    failureRecord() {
      if (this.isSz) {
        if (this.modelType === "21"||this.modelType === "13"||this.modelType === "10"||this.modelType === "11"||this.modelType === "12"||this.modelType === "19") {
           window.open(this.filePathErro)
        }else {
          let url="/apiPayeeManagement/downLoadErrorExcel.do"
          this.$ajax({
            url: this.url + url,
            method: "post",
            responseType: "blob",
          })
            .then((res) => {
              if (!res) return;
              const blob = new Blob([res.data], {
                type: "application/vnd.ms-excel",
              }); // 构造一个blob对象来处理数据，并设置文件类型

              if (window.navigator.msSaveOrOpenBlob) {
                //兼容IE10
                navigator.msSaveBlob(blob, "失败记录.xlsx");
              } else {
                const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
                const a = document.createElement("a"); //创建a标签
                a.style.display = "none";
                a.href = href; // 指定下载链接
                a.setAttribute("download", "失败记录.xlsx");
                //a.download = this.filename; //指定下载文件名
                a.click(); //触发下载
                URL.revokeObjectURL(a.href); //释放URL对象
              }
            })
            .catch(function () {
              this.$message.error("下载失败！");
            });
        }
      } else {
        // 贾汪
        window.open(this.filePathErro);
      }
    },
    // 继续导入
    continueImport() {
      this.closeImportResult();
    },
  },
};
</script>

<style lang="scss" scoped>
.item-one {
  img {
    width: 20.5px;
    height: 20.5px;
    margin-right: 9.5px;
  }
  span {
    font-size: 16px;
    font-family: Microsoft YaHei;
    font-weight: 400;
  }

  display: flex;
  align-items: center;
  padding-left: 150px;
  padding-top: 50px;
  margin-bottom: 23px;
}
.item-two {
  padding-left: 91px;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #666666;
  margin-bottom: 11px;
}
.item-three {
  padding-left: 142px;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #999999;
}
.high {
  color: #ff3c3c;
}
</style>
