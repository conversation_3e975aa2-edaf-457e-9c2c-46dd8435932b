<template>
  <div class="nd-table-box">
    <el-table ref="table" :class="empty ? 'empty' : ''" :data="data" :border="true" v-bind="$attrs"
      :row-class-name="tableRowClassName" :span-method="objectSpanMethod" v-on="$listeners"
      @cell-mouse-leave="cellMouseLeave" @cell-mouse-enter="cellMouseEnter" @header-dragend="drag()">
      <template #empty>
        <div class="empty-image" />
        <div class="empty-data">
          {{ noValueText }}
        </div>
      </template>
      <slot></slot>
    </el-table>
  </div>
</template>

<script>
import RowSpan from "./utils/row-span";
export default {
  mixins: [RowSpan],
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    noValueText: {
      type: String,
      default: "暂无数据"
    },
    // 多选列是否合并单元格
    selectionSpan: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      empty: false,
    };
  },
  watch: {
    data: {
      // immediate: true,
      handler(value) {
        if (value.length === 0) {
          this.empty = true;
        } else {
          this.empty = false;
        }
      },
    },
  },
  methods: {
    // 展开行
    toggleRowExpansion(a, b) {
      this.$refs.table.toggleRowExpansion(a, b)
    },
    // 拖拽重新布局
    drag() {
      this.$refs.table.doLayout()
    },
  }
};
</script>

<style lang='scss' scoped>
.nd-table-box {
  width: auto;
  height: auto;

  .empty {
    ::v-deep .el-table__body-wrapper {
      z-index: 2;
    }
  }

  ::v-deep .el-table th.el-table__cell {
    background-color: #f6faff; // 头部背景色
    padding: 0px;
    height: 30px;
    font-size: 13px;
    color: #000;
    font-weight: normal;
  }

  ::v-deep .el-table {
    font-size: 13px;
    color: #000;
    font-weight: normal;
  }

  ::v-deep .el-table__empty-block {
    display: inline;
  }

  ::v-deep .el-table__empty-text {
    width: 100%;
    height: 80%;
    line-height: normal;
    margin-top: 20px;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;

    .empty-image {
      width: 153px;
      height: 61px;
      background-image: url('@/assets/nodata.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center center;
    }

    .empty-data {
      margin-top: 10px;
    }
  }

  ::v-deep .el-table thead {
    color: #3f4457;
  }

  ::v-deep .el-table .el-table__cell {
    padding: 6px 0;
  }

  ::v-deep .el-table__body tr:not(.current-row):not(.select-row):hover>td {
    background-color: #e8f7ff;
  }

  ::v-deep .el-table__body .el-table__row.hover-row:not(.select-row) td {
    background-color: #e8f7ff;
  }

  ::v-deep .el-table__body tr.current-row>td {
    background-color: #c8ecff;
  }

  ::v-deep .el-table__body tr.hover-row>td.el-table__cell {
    background-color: #c8ecff;
  }

  ::v-deep .el-table__body tr.select-row>td {
    background-color: #c8ecff;
  }

  ::v-deep .el-table--border .el-table__cell {
    border-bottom: 1px solid #DCE5F2;

  }

  ::v-deep .el-table--border .el-table__cell {
    border-right: 1px solid #DCE5F2;
  }

  ::v-deep .el-table--border {
    border: 1px solid #DCE5F2;
  }
}
</style>


