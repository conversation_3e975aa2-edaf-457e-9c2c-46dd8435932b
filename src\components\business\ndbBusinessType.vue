<template>
  <div class="ndb-bussiness-type-box">
    <nd-select
      v-bind="$attrs"
      ref="bussinessSelect"
      :key="key"
      v-loadMore="loadBusinessType"
      filterable
      clearable
      :filter-method="remoteMethod"
      v-on="$listeners"
      @clear="clearBusinessType()"
      @focus="selectFocus()"
      @blur="SelectBlur()"
    >
      <el-option v-for="(item,index) in options" :key="index" :label="item.payTypeName" :value="item.id" />
    </nd-select>
  </div>
</template>

<script>
import ndSelect from "@/components/ndSelect.vue";
export default {
    components: {
        ndSelect
    },
    directives: {
        loadMore: {
            bind(el, binding) {
                // 获取element，定义scroll
                let select_dom = el.querySelector(
                    ".el-select-dropdown .el-select-dropdown__wrap"
                );
                select_dom.addEventListener("scroll", function () {
                    let height = this.scrollHeight - this.scrollTop <= (this.clientHeight + 5);
                    if (height) {
                        binding.value();
                    }
                });
            },
        },
    },
    props: {
        deptId: { //共用接口，日记账这边不需要传
            type: String,
            default: "",
        },
        payType: { //默认为空，1收入 2支出
            type: String,
            default: "",
        },
        requestFromWhere: { //1收款管理，其他模块不传
            type: String,
            default: "",
        },
        modularType: { //1：收款管理  2：用款管理  3:大额支出
            type: String,
            default: "1",
        },
        type:{ //用款申请（专有参数）
            type: String,
            default: "",
        }
    },
    data() {
        return {
            key: 1,
            isHaveMore: true,
            options: [],
            params: {
                pageIndex: 1,
                pageSize: 10,
                payType: "",
                deptId: "",
                busInessName: "",
                requestFromWhere: ""
            },
            stay: true,
        };
    },
    watch: {
        deptId: {
            handler(newValue) {
                // console.log(newValue);
                this.params.pageIndex = 1
                // this.params.deptId = newValue
                if (newValue != undefined && newValue != null && newValue != "") {
                    this.params.deptId = newValue
                } else {
                    this.params.deptId = ""
                }
                this.options = []
                // console.log(this.params);
                this.getBusinessTypeList()
            }
        }
    },
    mounted() {
        // console.log(111111111111111111);
        // this.getBusinessTypeList()
        this.key++
    },
    methods: {
        remoteMethod(query) {
            this.options = []
            this.params.pageIndex = 1
            this.params.busInessName = query
            this.getBusinessTypeList()
        },
        selectFocus() {
            this.getBusinessTypeList()
        },
        SelectBlur() {
            this.params.busInessName = ""
        },
        clearBusinessType() {
            this.options = []
            this.params.pageIndex = 1
        },
        //获取业务类型数据
        getBusinessTypeList() {
            // if (this.deptId === "") {
            //     this.$message.warning('请选择收款单位')
            //     return;
            // }
            
            if (this.modularType == '1') { //收款管理
                let params = this.params
                params.payType = this.payType
                params.requestFromWhere = this.requestFromWhere
                this.$ajax({
                    url: "/cwPzDayNew/businessTypeList.do",
                    method: "post",
                    data: params,
                }).then((res) => {
                    // console.log(res, "getCollectionRegistrationData");
                    if (res.data.code === 0) {
                        if (this.params.pageIndex == 1) {
                            this.options = res.data.data.list
                        } else {
                            if(this.isHaveMore){
                                this.options = [...this.options, ...res.data.data.list]
                            }
                        }
                        if (res.data.data.hasnextpage) {
                            this.params.pageIndex += 1;
                            this.isHaveMore = true
                        } else {
                            this.isHaveMore = false
                        }
                    } else {
                        this.options = []
                        // this.$message.warning(res.data.msg)
                    }
                });
            }else if(this.modularType == '2'){ //用款管理
                // let params = this.params
                this.params.payType = this.payType;
                let type = this.type;
                let params = {
                    dataId: this.params.deptId,
                    pageIndex: this.params.pageIndex,
                    pageSize: this.params.pageSize,
                    payType: this.params.payType,
                    busInessName: this.params.busInessName,
                    type:type,
                };
                
                this.$ajax({
                    url: "/useMoneyApplyNew/queryUsePayTypeConfigList.do",
                    method: "post",
                    data: params,
                }).then((res) => {
                    // console.log(res, "getCollectionRegistrationData");
                    if (res.data.code === 0) {
                        if (this.params.pageIndex == 1) {
                            this.options = res.data.data.list
                        } else {
                            if(this.isHaveMore){
                                this.options = [...this.options, ...res.data.data.list]
                            }
                        }
                        if (res.data.data.hasNextPage) {
                            this.params.pageIndex += 1;
                            this.isHaveMore = true
                        } else {
                            this.isHaveMore = false
                        }
                    } else {
                        this.options = []
                        // this.$message.warning(res.data.msg)
                    }
                });
            }else if(this.modularType == '3'){ //大额支出
                // let params = this.params
                this.params.payType = this.payType;
                let type = this.type;
                let params = {
                    dataId: this.params.deptId,
                    pageIndex: this.params.pageIndex,
                    pageSize: this.params.pageSize,
                    payType: this.params.payType,
                    busInessName: this.params.busInessName,
                    type:type,
                    expansionConfig:"1"   //大额支出
                };
                
                this.$ajax({
                    url: "/bigMoneyApply/commonQueryUsePayTypeConfigList.do",
                    method: "post",
                    data: params,
                }).then((res) => {
                    // console.log(res, "getCollectionRegistrationData");
                    if (res.data.code === 0) {
                        if (this.params.pageIndex == 1) {
                            this.options = res.data.data.list
                        } else {
                            if(this.isHaveMore){
                                this.options = [...this.options, ...res.data.data.list]
                            }
                        }
                        if (res.data.data.hasNextPage) {
                            this.params.pageIndex += 1;
                            this.isHaveMore = true
                        } else {
                            this.isHaveMore = false
                        }
                    } else {
                        this.options = []
                        // this.$message.warning(res.data.msg)
                    }
                });
            }

        },
        //滚动到达底部触发加载更多事件(业务类型)
        loadBusinessType() {
            // console.log(this.isHaveMore,"====this.isHaveMore");
            if (this.isHaveMore) {
                // console.log(this.params.pageIndex);
                this.shakeSubmit(this.getBusinessTypeList, 500)();
            } else {
                this.showLoading()
                // this.$message.success("全部加载完毕");
                return false;
            }
        },
        // 滚动条触发事件防抖
        shakeSubmit(fn, delay) {
            let _this = this;
            //闭包原理，返回一个函数
            return function (e) {
                //如果定时器存在则清空定时器
                if (_this.timer) {
                    clearTimeout(_this.timer);
                }
                //设置定时器，规定时间后执行真实要执行的函数
                _this.timer = setTimeout(function () {
                    fn.apply(this, e);
                }, delay);
            };
        },
        showLoading() {
            if (this.stay) {
                this.$message.success("全部加载完毕");
                this.lock();
            }
        },
        unlock() {
            setTimeout(() => {
                this.stay = true;
                // this.lock()
            }, 1000);
        },
        lock() {
            this.stay = false;
            this.unlock()
        },
    },
};
</script>
<style lang="scss" scoped>

</style>