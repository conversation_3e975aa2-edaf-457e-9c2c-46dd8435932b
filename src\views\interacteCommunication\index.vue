<template>
  <div class="main-cont">
    <tag @tagChange="tagChange"/>
    <div v-show="activeTag === 0" class="form-cont">
      <div class="form-top">
        <img class="img1" src="@/assets/detail/message.png" alt="" />
        <div class="title">我要留言</div>
      </div>
      <div class="form">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="标题:" prop="title">
                <el-input
                  v-model="ruleForm.title"
                  maxlength="50"
                  clearable
                ></el-input> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="问题类型:" prop="type">
                <el-select v-model="ruleForm.type" placeholder="" clearable>
                  <el-option v-for="(item,i) in questionList" :key="i" :label="item.dataValue" :value="item.dataKey"></el-option>
                  <!-- <el-option label="政策咨询" value="1"></el-option>
                  <el-option label="违规举报" value="2"></el-option>
                  <el-option label="问题反馈" value="3"></el-option> -->
                </el-select> </el-form-item
            ></el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="联系人:" prop="contactUser">
                <el-input
                  v-model="ruleForm.contactUser"
                  maxlength="20"
                  clearable
                ></el-input> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="联系方式:" prop="contactInfo">
                <el-input v-model="ruleForm.contactInfo" maxlength="20" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="项目编号:" prop="proCode">
                <el-select
                  v-model="ruleForm.proCode"
                  placeholder=""
                  clearable
                  filterable
                  @query-select="handleSearchOne"
                  @change="gethandleSearchOneChange"
                >
                  <el-option
                    v-for="(item, index) in proCodeList"
                    :value="item"
                    :key="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目名称:" prop="proName">
                <el-select
                  v-model="ruleForm.proName"
                  placeholder=""
                  clearable
                  filterable
                  @query-select="handleSearchTwo"
                  @change="gethandleSearchTwoChange"
                >
                  <el-option
                    v-for="(item, index) in proNameList"
                    :value="item"
                    :key="index"
                    style="width: 400px !important"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="内容:" prop="questionerContent">
            <el-input
              type="textarea"
              v-model="ruleForm.questionerContent"
              maxlength="500"
            ></el-input>
          </el-form-item>
          <el-form-item label="附件:">
            <!-- <ndb-upload ref="ndbUpload1" server-name="nd-village" data-id="********************************" file-type-id="17d60877d5c445958c088e886ed3dc85" /> -->
            <ndb-upload ref="ndbUpload1" server-name="nd-village" :data-id="ruleForm.busId" />
          </el-form-item>
        </el-form>
      </div>
      <div class="btn">
        <el-button
          style="
            height: 40px;
            width: 193px;
            background: #ed911f;
            border: 1px solid #ed911f;
            color: #fff;
          "
          @click="submitForm('ruleForm')"
          >提交</el-button
        >
        <el-button
          style="height: 40px; width: 193px; color: #ed911f; border: 1px solid #ed911f"
          plain
          @click="resetForm('ruleForm')"
          >重置</el-button
        >
      </div>
    </div>
    <announcement v-show="activeTag === 1"/>
  </div>
</template>

<script>
import ndbUpload from '@/components/business/ndbUpload/ndbUpload.vue';

// 自定义组件
import tag from './components/tag.vue';
import announcement from './components/announcement.vue';

export default {
  components: {
    ndbUpload,
    tag,
    announcement
  },
  data() {
    return {
      ruleForm: {
        title: '',
        type: '',
        contactUser: '',
        contactInfo: '',
        proCode: '',
        proName: '',
        questionerContent: '',
        busId: '',
      },

      rules: {
        title: [{ required: true, message: '请输入', trigger: 'change' }],
        type: [{ required: true, message: '请选择', trigger: 'change' }],
        contactUser: [{ required: true, message: '请输入', trigger: 'change' }],
        contactInfo: [{ required: true, message: '请输入', trigger: 'change' }],
        proCode: [{ required: true, message: '请输入', trigger: 'change' }],
        proName: [{ required: true, message: '请输入', trigger: 'change' }],
        questionerContent: [{ required: true, message: '请输入', trigger: 'change' }],
      },
      proCodeList: [],
      proNameList: [],
      questionList:[],
      activeTag: 0,
    };
  },
  mounted() {
    this.getOptionList();
    this.getUuid();
    this.getQuestionAll()
  },
  methods: {
    getOptionList() {
      this.$ajax({
        url: '/web/selectInfo',
        method: 'get',
      }).then((res) => {
        // debugger
        console.log(res, 'this.proCodeList');
        if (res.data.code === 200) {
          if (res.data.data.proCodeList) {
            this.proCodeList = res.data.data.proCodeList;
          }
          if (res.data.data.proNameList) {
            this.proNameList = res.data.data.proNameList;
          }
        }
      });
    },
    getUuid() {
      this.$ajax({
        url: '/web/fileInfo',
        method: 'post',
      }).then((res) => {
        if (res.data.code === 200) {
          this.ruleForm.busId = res.data.data.busId;
        }
      });
    },
    handleSearchOne(query) {
      this.proCode = this.proCode.filter((item) => item.includes(query));
    },
    handleSearchTwo(query) {
      this.proName = this.proName.filter((item) => item.includes(query));
    },
    gethandleSearchOneChange(event) {
      this.$ajax({
        url: '/web/selectInfo',
        method: 'get',
        data: {
          proCode: event,
        },
      }).then((res) => {
        if (res.data.code === 200) {
          if (res.data.data.proNameList) {
            this.ruleForm.proName = res.data.data.proNameList[0];
          }
        }
      });
    },
    gethandleSearchTwoChange(event) {
      this.$ajax({
        url: '/web/selectInfo',
        method: 'get',
        data: {
          proName: event,
        },
      }).then((res) => {
        if (res.data.code === 200) {
          if (res.data.data.proCodeList) {
            this.ruleForm.proCode = res.data.data.proCodeList[0];
          }
        }
      });
    },

    getQuestionAll() {
      this.$ajax({
        url: '/web/getType',
        method: 'get',
        data: {
          baseType: 'SXLX',
        },
      }).then((res) => {
        if (res.data.code === 200) {
          this.questionList = res.data.data
          // if (res.data.data.proCodeList) {
          //   this.ruleForm.proCode = res.data.data.proCodeList[0];
          // }
        }
      });
    },
    submitForm(formName) {
      this.$confirm('是否确定提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$refs[formName].validate((valid) => {
            if (valid) {
              this.getSubitNext();
              this.resetForm('ruleForm');
              this.getUuid();
            } else {
              return false;
            }
          });
        })
        .catch(() => {});
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.$refs.ndbUpload1.fileList = [];
      this.getUuid();
    },
    getSubitNext() {
      this.$ajax({
        url: '/web/save',
        // url: "/web/save?" + `title=${this.ruleForm.title}&type=${this.ruleForm.type}&contactUser=${this.ruleForm.contactUser}&contactInfo=${this.ruleForm.contactInfo}&proCode=${this.ruleForm.proCode}&proName=${this.ruleForm.proName}&questionerContent=${this.ruleForm.questionerContent}`,
        method: 'post',
        headers: {
          'Content-Type': 'application/json',
        },
        data: this.ruleForm,
        // data: JSON.stringify(this.ruleForm),
      }).then((res) => {
        if (res.data.code === 200) {
          this.$message({
            message: res.data.msg,
            type: 'success',
          });
          // this.resetForm("formName")
        } else {
          this.$message({
            message: res.data.msg,
            type: 'warning',
          });
        }
      });
    },
    /**
     * tag切换
     */
     tagChange(e){
        console.log(e);
        this.activeTag = e;
     }
  },
};
</script>
<style lang="scss" scoped>
.main-cont {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .form-cont {
    width: 1300px;
    // height: 621px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
    margin: 32px 0 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 28px 0 30px;

    .form-top {
      display: flex;
      align-items: center;
      flex-direction: row;
      height: 27px;
      margin-bottom: 31px;

      .title {
        margin-left: 10px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 24px;
        color: #ed911f;
        line-height: 24px;
      }
    }

    .form {
      ::v-deep .el-input--suffix .el-input__inner {
        height: 30px;
        width: 400px;
      }

      ::v-deep .el-textarea__inner {
        height: 236px;
      }
    }

    .btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.wrap-cont {
  width: 81px;
  height: 91px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-item {
  // width: 100%;
  // height: 100%;
  display: flex;
  flex-direction: row;
  // justify-content: center;
  // align-items: center;
  flex-wrap: wrap;

  .file-img {
    width: 80px;
    height: 80px;
    margin-right: 10px;
    margin-top: 10px;
  }
}
</style>
