<template>
  <div class="nd-input-box" :style="{ width: width }">
    <el-input ref="input" v-bind="$attrs" v-on="$listeners" />
  </div>
</template>

<script>
export default {
  props: {
    width: {
      type: String,
      default: "220px",
    },
  },
  data() {
    return {

    };
  },
  mounted() {
    
  },
  methods: {
    setCursorPos(start, end) {
      this.$refs.input.$refs.input.focus();
      this.$refs.input.$refs.input.selectionStart = start;
      this.$refs.input.$refs.input.selectionEnd = end;
    },
  },
};
</script>

<style lang='scss' scoped>
.nd-input-box {
  ::v-deep .el-input__inner {
    width: 100%;
    height: 28px;
    line-height: 28px;
    border-radius: 0px;
    border: 1px solid #dcdfe6;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 0px;
    padding-bottom: 0px;
    font-size: 12px;
    background-color: transparent;
  }

  ::v-deep .el-input.is-disabled .el-input__inner {
    color: #606266;
  }

  ::v-deep .el-input--suffix .el-input__inner {
    padding: 0 10px;
  }

  ::v-deep .el-input__suffix {
    height: 22px;
    margin-top: -4px;
  }
}
</style>
