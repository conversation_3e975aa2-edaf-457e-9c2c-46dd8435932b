<template>
  <div class="project-info" v-loading="loading">
    <!-- tab切换 -->
    <div class="tab-box">
      <div class="w tab-type">
        <span v-for="(item, index) in tabIndexList" :key="index" :class="{ active: tabIndex == index }"
          @click="toggle(index)">{{ item }}</span>
      </div>
    </div>
    <template v-if="tabIndex !== 2">
      <!-- search查询 -->
      <div class="search-box">
        <div class="w">
          <div class="row">
            <div class="row-left">交易品种:</div>
            <div class="row-right">
              <span :class="{ active: form.tradeCategory == item.proTypeKey }"
                v-for="(item, index) in formList.tradeCategoryList" :key="index" @click="changeTrade(item, index, 1)">{{
    item.proTypeName }}</span>
            </div>
          </div>
          <div class="row" v-show="form.tradeCategory">
            <div class="row-left"></div>
            <div class="row-right child">
              <span :class="{ active: form.tradeCategory2 == item.proTypeKey }"
                v-for="(item, index) in formList.tradeCategoryList2" :key="index"
                @click="changeTrade(item, index, 2)">{{ item.proTypeName }}</span>
            </div>
          </div>
          <div class="row">
            <div class="row-left">项目地区:</div>
            <div class="row-right">
              <span :class="{ active: form.projectArea == item.id }" v-for="(item, index) in formList.projectAreaList"
                :key="index" @click="changeArea(item, 1)">{{ item.name }}</span>
            </div>
          </div>
          <div class="row" v-show="form.projectArea && formList.projectAreaList2">
            <div class="row-left"></div>
            <div class="row-right child">
              <div>
                <span :class="{ active: form.projectArea2 == item.id }"
                  v-for="(item, index) in formList.projectAreaList2" :key="index" @click="changeArea(item, 2)">{{
    item.name }}</span>
              </div>
              <div v-show="form.projectArea2 && formList.projectAreaList3">
                <span :class="{ active: form.projectArea3 == '' }" style="margin-right: 16px"
                  @click="changeArea({ id: '', name: '全部' }, 3)">全部</span>
                <ndRadio v-model="form.projectArea3" v-for="(item, index) in formList.projectAreaList3" :key="index"
                  :label="item.id">{{ item.name }}</ndRadio>
              </div>
            </div>
          </div>
          <!-- <div class="row" v-show="form.projectArea2">
          <div class="row-left"></div>
          <div class="row-right child">
            <span :class="{active:form.projectArea3 == item.dataKay}" v-for="(item,index) in formList.projectAreaList3" :key="index" @click="changeArea(item,3)">{{item.dataValue}}</span>
          </div>
        </div> -->
          <div class="row" v-show="tabIndex == 0">
            <div class="row-left">项目状态:</div>
            <div class="row-right">
              <span :class="{ active: form.projectStatus == item.dataKay }"
                v-for="(item, index) in formList.projectStatusList" :key="index"
                @click="form.projectStatus = item.dataKay">{{ item.dataValue }}</span>
            </div>
          </div>
          <template v-if="tabIndex == 0">
            <div style="display:flex;justify-content:space-between">
              <div class="row" style="width: 50%;">
                <div class="row-left">交易面积:</div>
                <div class="row-right flex">
                  <nd-input :width="'200px'" v-model="form.dealAcreage1" @input="(e) => {
    priceFormat(e, 1);
  }
    "></nd-input>
                  <div class="mar">~</div>
                  <nd-input :width="'200px'" v-model="form.dealAcreage2" @input="(e) => {
    priceFormat(e, 2);
  }
    "></nd-input>
                  <div class="mar2">亩</div>
                </div>
              </div>
              <div class="row">
                <div class="row-left">报名截止时间:</div>
                <div class="row-right">
                  <!--  start-placeholder="开始日期" end-placeholder="截止日期" -->
                  <nd-date-picker type="daterange" range-separator="~" v-model="form.bmEndFirst"
                    value-format="yyyy-MM-dd" :width="'415px'" clearable @change="changeDate"></nd-date-picker>
                </div>
              </div>
            </div>
            <div style="display:flex;justify-content:space-between">
              <div class=" row" v-show="tabIndex == 0" style="width: 50%;">
                <div class="row-left">成交起止时间:</div>
                <div class="row-right">
                  <nd-date-picker type="daterange" range-separator="~" v-model="form.cjDateStart"
                    value-format="yyyy-MM-dd" :width="'415px'" clearable @change="changeDate"></nd-date-picker>
                </div>
              </div>
              <div class="row">
                <div class="row-left">项目查询:</div>
                <div class="row-right">
                  <nd-input :width="'415px'" maxlength="200" v-model.trim="form.projectNameOrCode"
                    placeholder="请输入项目名称/项目编号"></nd-input>
                </div>
              </div>
            </div>
            <div class="btn-box">
              <nd-button type="primary" @click="search">查询</nd-button>
              <nd-button @click="reset">重置</nd-button>
            </div>
          </template>

          <template v-if="tabIndex == 1">
            <div style="display:flex;justify-content:space-between;align-items: center;">
              <div class="row" style="width: 50%;margin-top: 10px;">
                <div class="row-left">项目查询:</div>
                <div class="row-right">
                  <nd-input :width="'415px'" maxlength="200" v-model.trim="form.projectNameOrCode"
                    placeholder="请输入项目名称/项目编号"></nd-input>
                </div>
              </div>
              <div class="btn-box2">
                <nd-button type="primary" @click="search">查询</nd-button>
                <nd-button @click="reset">重置</nd-button>
              </div>
            </div>
          </template>
        </div>
      </div>
      <!-- sort 排序 -->
      <div class="sort-box w">
        <div class="w sort">
          <div class="item1" v-for="(item, index) in sortList" :key="index" @click="sort(item, index)">
            <span :class="{ active: item.dataKey == sortIndex }">{{ item.dataValue }}</span>
            <div class="iconBox" v-if="item.isJT">
              <!-- <div :class="['top', {active:item.dataKey == sortIndex && item.isFlag}]"><i class="el-icon-arrow-up"></i></div>
            <div :class="['bottom', {active:item.dataKey == sortIndex && !item.isFlag}]"><i class="el-icon-arrow-down"></i></div> -->

              <img v-if="item.dataKey == sortIndex && item.isFlag" src="@/assets/projectInformation/top.png" alt="" />
              <img v-else-if="item.dataKey == sortIndex && !item.isFlag" src="@/assets/projectInformation/bottom.png"
                alt="" />
              <!-- <img v-else src="@/assets/projectInformation/clone.png" alt=""> -->
            </div>
          </div>
        </div>
      </div>
      <!-- list 数据 -->
      <div class="list-box w">

        <template v-if="dataList.length > 0">
          <div class="item" v-for="(item, index) in dataList" :key="index" @click="goDetail(item)">
            <!-- 图片 -->
            <div class="img-box">
              <!-- <div class="img-box2"> -->
              <!-- <el-image v-if="item.proPicPath" :src="item.proPicPath"></el-image> -->
              <img v-if="item.proPicPath" :src="item.proPicPath" />
              <img v-else src="@/assets/projectInformation/default.png" />
              <!-- </div> -->
              <div class="status" v-if="tabIndex == 0">
                <img v-if="item.xmStatus == 4" src="@/assets/projectInformation/jjz.png" alt="" />
                <img v-if="item.xmStatus == 2" src="@/assets/projectInformation/bmz.png" alt="" />
                <img v-if="item.xmStatus == 6" src="@/assets/projectInformation/bmz2.png" alt="" />
                <img v-if="item.xmStatus == 6" src="@/assets/projectInformation/jjz.png" alt="" />
              </div>
              <div class="status" v-if="tabIndex == 1">
                <img v-if="item.tendersType == 0" src="@/assets/projectInformation/ylb.png" alt="" />
                <img v-else-if="item.xmStatus == 5" src="@/assets/projectInformation/ycj.png" alt="" />
              </div>
            </div>
            <!-- 内容 -->
            <div class="container">
              <div class="title" :title="item.proName">{{ item.proName }}</div>
              <div class="detail" v-show="tabIndex == 0">
                <div class="detail-item">
                  价格：<span>{{ item.upsetLower }}</span>{{ item.upsetLowerDw }}
                </div>
                <div class="detail-item">报名截止时间：{{ item.bmDateEnd }}</div>
                <!-- <div class="detail-item">交易截止时间：{{ item.biddTime }}</div> -->
                <div class="detail-item">交易开始时间：{{ item.biddTime }}</div>
              </div>
              <div class="detail" v-show="tabIndex == 1">
                <div class="detail-item"
                  v-if="item.tendersType != 1 && (item.upsetLower == '' || item.upsetLower == 0 || item.upsetLower == null)">
                  成交价格：<span>--</span>
                </div>
                <div class="detail-item" v-else>
                    成交价格：<span>{{ item.upsetLower }}</span> 元
                     <!-- {{ item.upsetLowerDw }} -->
                </div>
                <div class="detail-item" v-if="item.turnoverTime">成交时间：{{ item.turnoverTime }}</div>
                <div class="detail-item" v-else>成交时间：--</div>
              </div>
            </div>
          </div>
        </template>

        <template v-else>
          <div class="noneBox">
            <div class="noneImageBox">
              <img src="@/assets/projectInformation/noneData.png" alt="" />
            </div>
            <div class="noneText">暂无数据</div>
          </div>
        </template>
      </div>
      <!-- 分页器 -->
      <div class="w">
        <nd-pagination :page-size="pager.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pager.total"
          :total-page="totalPage" :current-page="pager.pageNo" @current-change="handleCurrentChange" />
      </div>
    </template>

    <template v-else>
      <div class="wrap-container">
        <list requestUrl="/tzgg/"></list>
      </div>
    </template>

    <!-- <ndb-upload-dialog ref="ndbUploadDialog" :show-phone-upload="false" :show-metronome-upload="false" :title="12313" /> -->
  </div>
</template>

<script>
import ndButton from '@/components/ndButton.vue';
import ndInput from '@/components/ndInput.vue';
import ndPagination from '@/components/ndPagination.vue';
import ndDatePicker from '@/components/ndDatePicker.vue';
import ndRadio from '@/components/ndRadio.vue';
import list from '@/views/newsListView/components/list.vue';
// import ndbUploadDialog from "@/components/business/ndbUpload/ndbUploadDialog.vue";

let isRouter = '';
export default {
  components: {
    ndInput,
    ndButton,
    ndDatePicker,
    ndPagination,
    ndRadio,
    list,
  },
  data() {
    return {
      loading: false,
      // tab切换
      tabIndex: 0,
      tabIndexList: ['交易公告', '成交公告', '通知公告'],
      // search查询
      form: {
        tradeCategory: '', // 选中 交易品种
        tradeCategory2: '', // 选中 交易品种2
        proTypeParentId: '', // 父级id
        projectArea: '', // 选中 项目地区
        projectArea2: '', // 选中 项目地区2
        projectArea3: '', // 选中 项目地区3
        projectStatus: '0', // 选中 项目状态
        dealAcreage1: '', // 交易面积
        dealAcreage2: '', // 交易面积
        bmEndFirst: '', // 报名截止时间
        cjDateStart: '', // 成交起止时间
        projectNameOrCode: '', // 项目名称/项目编号
        level:'',
      },
      // search查询 数组
      formList: {
        // 交易品种
        tradeCategoryList: [],
        // 交易品种2
        tradeCategoryList2: [],
        // 项目地区
        projectAreaList: [],
        // 项目地区2
        projectAreaList2: [],
        // 项目地区3
        projectAreaList3: [],
        // 项目状态
        projectStatusList: [
          { dataKay: '0', dataValue: '全部' },
          { dataKay: '2', dataValue: '报名中' },
          { dataKay: '4', dataValue: '竞价中' },
        ],
      },
      // sort 排序
      sortIndex: '0',
      sortIndexFlag: '', // 1升序 2降序
      // sort 排序 数组
      sortList: [
        { dataKey: '0', dataValue: '默认', isJT: false, isFlag: true },
        { dataKey: '1', dataValue: '按价格', isJT: true, isFlag: true },
        { dataKey: '2', dataValue: '按面积', isJT: true, isFlag: true },
        { dataKey: '3', dataValue: '按流转期限', isJT: true, isFlag: true },
        { dataKey: '4', dataValue: '按报名开始时间', isJT: true, isFlag: true },
      ],
      // list 列表
      dataList: [],
      // 分页器
      pager: {
        pageNo: 1, // 当前页数
        pageSize: 16, // 当前页条数
        total: 0, // 总条目数
      },
    };
  },
  computed: {
    totalPage() {
      return Math.ceil(this.pager.total / this.pager.pageSize);
    },
  },
  beforeRouteEnter(to, from, next) {
    console.log('前置守卫', from.fullPath);
    isRouter = from.fullPath;
    next();
  },
  activated() {
    this.fromHome(); // 处理首页调过来的情况
  },
  mounted() {
    // this.fromHome(); // 处理首页调过来的情况
    // this.getData();
  },
  methods: {
    // 首页调过来
    fromHome() {
      let type = this.$route.query.type;
      let tradeCategory = this.$route.query.tradeCategory1;
      let tradeCategory2 = this.$route.query.tradeCategory2;
      let projectNameOrCode = this.$route.query.projectNameOrCode;

      if (isRouter == '/' && (tradeCategory || projectNameOrCode || type)) this.reset();

      // 获取交易品种
      this.getTradeCategoryList();
      // 获取地区
      // this.getProjectArea();

      if (isRouter == '/' || isRouter.includes('/details')) {
        console.log('====', typeof type, type);
        if (type == '1') this.tabIndex = 1; // tab切换
        if (type === '0') this.tabIndex = 0;
        // else this.tabIndex = 0;
      }
      if (tradeCategory) this.form.tradeCategory = tradeCategory;
      if (tradeCategory) this.form.proTypeParentId = tradeCategory;
      if (tradeCategory2) this.form.tradeCategory2 = tradeCategory2;
      if (projectNameOrCode) this.form.projectNameOrCode = projectNameOrCode;

      this.getData();
    },
    //获取数据 ============================
    getData() {
      if (this.form.dealAcreage1 && this.form.dealAcreage2) {
        if (this.form.dealAcreage1 * 1 > this.form.dealAcreage2 * 1) {
          this.$message({
            message: '最小面积应小于等于最大面积，请重新填写',
            type: 'warning',
          });
          return;
        }
      }
      var params = {
        // 搜索 参数
        proTypeId: this.form.tradeCategory2 ? this.form.tradeCategory2 : this.form.tradeCategory,
        xmStatus: this.form.projectStatus,
        jymjStart: this.form.dealAcreage1,
        jymjEnd: this.form.dealAcreage2,
        bmEndFirst: this.form.bmEndFirst ? this.form.bmEndFirst[0] : '',
        bmEndSecond: this.form.bmEndFirst ? this.form.bmEndFirst[1] : '',
        cjDateStart: this.form.cjDateStart ? this.form.cjDateStart[0] : '',
        cjDateEnd: this.form.cjDateStart ? this.form.cjDateStart[1] : '',
        keyWords: this.form.projectNameOrCode,
        // 排序 参数
        order: this.sortIndex,
        orderType: this.sortIndexFlag,
        // 分页 参数
        page: this.pager.pageNo,
        size: this.pager.pageSize,
        // 是否来自首页
        fromHome: 0,
        // 公告类型，0:交易公告,1:成交公告
        noticeType: this.tabIndex,
      };
      if (
        this.form.projectArea3
          ? this.form.projectArea3
          : this.form.projectArea2
            ? this.form.projectArea2
            : this.form.projectArea
      )
        params.unitId = this.form.projectArea3
          ? this.form.projectArea3
          : this.form.projectArea2
            ? this.form.projectArea2
            : this.form.projectArea;
      params.proTypeParentId = this.form.proTypeParentId;
      params.level= this.form.level
      console.log('请求参数', params);
      this.loading = true;
      this.$ajax({
        method: 'post',
        url: '/notice/noticeInfo',
        data: params,
      }).then((res) => {
        debugger
        if (res.data.code == 200) {
          console.log('列表', res.data.data);
          this.pager.total = res.data.data.total;
          // this.dataList = res.data.data.rows;
          this.dataList = res.data.data.records;
        }
        this.loading = false;
      });

      // if (this.tabIndex === 1) { // 成交公告

      // } else { // 交易公告
      //   this.$ajax({
      //     method: 'get',
      //     url: '/notice/noticeInfo',
      //     data: {
      //       showChild: '1',
      //       areaId: '1'
      //     },
      //     serverName: 'nd-base',
      //   }).then(res => {
      //     console.log('交易公告列表', res);
      //   });
      // }
    },
    // tab切换 事件 ====================================
    toggle(index) {
      this.tabIndex = index; // 切换选中状态

      this.reset(); // 重置筛选条件
      this.getData(); // 获取数据
    },

    // 搜索条件 =================================
    // 交易品种切换
    changeTrade(item, index, flag) {
      console.log(item);
      this.form.proTypeParentId = item.proTypeParentKey;
      if (flag == 1) {
        this.form.tradeCategory2 = '';
        this.form.tradeCategory = item.proTypeKey;
        // 获取子集 发请求
        if (item.proTypeId)
          this.formList.tradeCategoryList2 = [
            { proTypeId: '', proTypeName: '全部' },
            ...this.formList.tradeCategoryList[index].children,
          ];
      }
      if (flag == 2) this.form.tradeCategory2 = item.proTypeKey;
    },
    // 获取交易品种的接口
    getTradeCategoryList() {
      new Promise((resolve, reject) => {
        this.$ajax({
          method: 'get',
          url: '/baseInfo/webProInfoProType',
          data: {},
        })
          .then((res) => {
            if (res.data.code == 200) {
              // console.log('交易品种的接口', res.data.data);
              this.formList.tradeCategoryList = [
                { proTypeId: '', proTypeName: '全部' },
                ...res.data.data,
              ];
              resolve();
            }
          })
          .then((res) => {
            let tradeCategory = this.$route.query.tradeCategory1;
            let tradeCategory2 = this.$route.query.tradeCategory2;
            if (tradeCategory) {
              this.form.tradeCategory = tradeCategory; // 选中一级分类
              this.form.tradeCategory2 = tradeCategory2; // 选中二级分类
              let findChild = this.formList.tradeCategoryList.find((item, index) => {
                return item.proTypeId == tradeCategory;
              });
              this.form.proTypeParentId = tradeCategory; // 父级id
              this.formList.tradeCategoryList2 = [
                { proTypeId: '', proTypeName: '全部' },
                ...findChild.children,
              ];
            }
          });
      });
    },
    // 项目地区切换
    changeArea(item, flag) {
      if (flag == 1) {
        this.form.projectArea2 = '';
        this.form.projectArea3 = '';
        this.form.projectArea = item.id;
        this.form.level = item.level;
        this.formList.projectAreaList2 = this.getProjectArea(item.id, 2);
      } else if (flag == 2) {
        this.form.projectArea3 = '';
        this.form.projectArea2 = item.id;
        this.form.level = item.level;
        this.formList.projectAreaList3 = this.getProjectArea(item.id, 3);
      } else {
        this.form.level = item.level;
        this.form.projectArea3 = item.id;
      }
    },
    // 获取项目地区的接口
    getProjectArea(areaId, flag) {
      return new Promise((resolve, reject) => {
        if (areaId) {
          this.$ajax({
            method: 'get',
            url: '/area/webServiceLazyLoadRegionTree',
            data: {
              areaId: areaId,
            },
          }).then((res) => {
            if (res.data.code == 200) {
              // console.log('地区树的接口', res.data.data);
              if (flag == 2)
                this.formList.projectAreaList2 = [{ id: '', name: '全部' }, ...res.data.data];
              if (flag == 3) this.formList.projectAreaList3 = res.data.data;
            }
          });
        } else {
          this.$ajax({
            method: 'get',
            url: '/area/webServiceLazyLoadRegionTree',
          }).then((res) => {
            if (res.data.code == 200) {
              // console.log('地区树的接口', res.data.data);
              this.formList.projectAreaList = [{ id: '', name: '全部' }, ...res.data.data[0].children];
            }
          });
        }
        resolve();
      });
    },
    // 亩数提示
    priceFormat(value, flag, int = 100) {
      value = value.toString();
      // debugger
      // 先把非数字的都替换掉，除了数字和小数点
      value = value.replace(/[^\d.]/g, '');
      // // 必须保证第一个为数字而不是小数点
      value = value.replace(/^\./g, '');
      // // 保证只有出现一个小数点而没有多个小数点
      value = value.replace(/\.{2,}/g, '.');
      // // 保证小数点只出现一次，而不能出现两次以上
      value = value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      // 保证只能输入2个小数
      value = value.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
      // 只能8位整数
      let index = value.indexOf('.');
      if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index);
      } else {
        value = value.slice(0, int);
      }
      if (flag == 1) this.form.dealAcreage1 = value;
      if (flag == 2) this.form.dealAcreage2 = value;
    },
    // 查询 事件
    search() {
      this.sortIndex = '0';
      this.sortIndexFlag = '';

      this.getData();
    },

    // 重置 事件
    reset() {
      // 搜索条件
      this.form.tradeCategory = '';
      this.form.tradeCategory2 = '';
      this.form.proTypeParentId=''
      this.form.projectArea = '';
      this.form.projectArea2 = '';
      this.form.projectArea3 = '';
      this.form.projectStatus = '0';
      this.form.dealAcreage1 = '';
      this.form.dealAcreage2 = '';
      this.form.regDeadline = '';
      this.form.bmEndFirst = '';
      this.form.cjDateStart = '';
      this.form.projectNameOrCode = '';
      // 排序
      this.sortIndex = '0';
      this.sortIndexFlag = '';
      // 分页器
      this.pager.pageNo = 1;
      this.pager.pageSize = 16;

      // this.getData();
    },

    // 搜索 日期 处理函数
    changeDate(e) {
      console.log(e);
    },

    // 排序 处理函数
    sort(item, index) {
      if (this.form.dealAcreage1 && this.form.dealAcreage2) {
        if (this.form.dealAcreage1 * 1 > this.form.dealAcreage2 * 1) {
          this.$message({
            message: '最小面积应小于等于最大面积，请重新填写',
            type: 'warning',
          });
          return;
        }
      }
      // 1升序 2降序
      if (!item.isJT) {
        // 默认
        this.sortIndex = item.dataKey;
        this.sortIndexFlag = '';
      } else {
        // 默认以外的
        if (item.dataKey == this.sortIndex) {
          this.sortList[index].isFlag = !this.sortList[index].isFlag;
          this.sortIndexFlag = this.sortIndexFlag == 1 ? 2 : 1;
        } else {
          this.sortList[index].isFlag = true;
          this.sortIndexFlag = 1;
        }
        this.sortIndex = item.dataKey;
      }

      this.getData();
    },

    // 跳转 详情
    goDetail(item) {
      console.log('跳转详情', item);
      let query = [];
      if (this.tabIndex == 0) {
        query = [{ name: '交易公告', route: 'projectInformationView', query: { type: 0 } }];
      }
      if (this.tabIndex == 1) {
        query = [{ name: '成交公告', route: 'projectInformationView', query: { type: 1 } }];
      }

      let query2 = {
        id: item.tendersId,
        // title: item.proName,
        route: JSON.stringify(query),
        type: "",
        homeType: 1
      };
      if (this.tabIndex === 0 && item.xmStatus == 2) query2.type = 1;
      if (this.tabIndex === 0 && item.xmStatus == 4) query2.type = 2;
      if (this.tabIndex === 1) query2.type = 3;
      // this.$router.push({
      //   name: 'details',
      //   query: query2,
      // });
      console.log(window.location);
      if (window.location.origin == "http://localhost:8080") {
        window.open(window.location.origin + `/#/details?id=${item.tendersId}&route=${JSON.stringify(query)}&type=${query2.type}&homeType=1`)
      } else {
        window.open(window.location.origin + window.location.pathname + `#/details?id=${item.tendersId}&route=${JSON.stringify(query)}&type=${query2.type}&homeType=1`)
      }
    },

    //  分页器函数
    handleCurrentChange(e) {
      console.log('当前页条数，变话', e);
      this.pager.pageNo = e;

      this.getData();
    },
  },
};
</script>

<style lang="scss" scoped>
.w {
  position: relative;
  width: 1300px;
  margin: 0 auto;
}

.project-info {
  width: 100%;
  background-color: #ffffff;
  padding-bottom: 20px;

  .tab-box {
    width: 100%;
    height: 50px;
    background-color: #f7f7f7;

    .tab-type {
      display: flex;

      span {
        width: 130px;
        line-height: 50px;
        font-weight: 400;
        color: #333333;
        font-size: 20px;
        text-align: center;
        cursor: pointer;
      }

      .active {
        color: #ffffff;
        font-weight: bold;
        background-color: #316c2c;
      }
    }
  }

  .search-box {
    padding-top: 27px;
    padding-bottom: 34px;

    // background-color: antiquewhite;
    .row {
      display: flex;
      min-height: 24px;
      font-size: 14px;

      // margin-bottom: 16px;
      // border-bottom: 1px solid #000;
      .row-left {
        width: 91px;
        color: #333333;
        font-weight: bold;
        line-height: 40px;
        // background-color: #0084ff;
        margin-right: 18px;
      }

      .row-right {
        width: calc(100% - 109px);
        line-height: 40px;
        // padding: 0 12px;
        padding-left: 12px;
        font-size: 18px;
        color: #333333;

        span {
          display: inline-block;
          padding: 0 15px;
          //   line-height: 23px;
          height: 23px;
          cursor: pointer;
          line-height: 23px;
        }

        span:hover {
          background: #316c2c;
          border-radius: 6px;
          color: white;
        }

        .active {
          background: #316c2c;
          border-radius: 6px;
          color: white;
        }
      }

      .flex {
        display: flex;
        color: #666666;
        font-size: 14px;

        .mar {
          display: inline-block;
          //   margin: 0 5px;
          width: 15px;
          text-align: center;
        }

        .mar2 {
          margin-left: 3px;
        }
      }

      .child {
        background-color: #f7f7f7;
        border-radius: 5px;
        font-size: 16px;
        // padding: 0 10px;
      }
    }

    .btn-box {
      // position: absolute;
      // right: 0;
      // bottom: -4px;
      text-align: center;
      margin-top: 10px;
    }
  }

  .sort-box {
    border-top: 1px solid #e5e5e5;
    border-bottom: 1px solid #e5e5e5;

    .sort {
      display: flex;
      align-items: center;
      height: 50px;
      font-size: 14px;
      color: #333333;

      .item1 {
        display: flex;
        align-items: center;
        height: 18px;
        margin-right: 30px;
        cursor: pointer;

        .iconBox {
          margin-left: 3px;
          height: 18px;
          // position: relative;
          display: flex;
          // align-items: center;
          // flex-direction: column;
          // height: 15px;
          // background-color: #e5e5e5;
          // // margin-left: 2px;
          // font-size: 14px;
          // font-weight: bold;
          // transform: translateY(-2px);

          // .top {
          //   position: absolute;
          //   left: 0;
          //   top: -5px;
          // }
          // .bottom {
          //   position: absolute;
          //   left: 0;
          //   bottom: -8px;
          // }
          padding-top: 4.2px;

          img {
            width: 4px;
            height: 11px;
          }
        }
      }

      .active {
        color: #316c2c;
        font-weight: 400;
      }
    }
  }

  .list-box {
    // width: 280px;
    // display: flex;
    // flex-wrap: wrap;
    // justify-content: start;
    padding-top: 29px;
    // background-color: #0084ff;

    .item {
      display: inline-block;
      width: 280px;
      // height: 370px;
      // background-color: #e5e5e5;
      margin-bottom: 26px;
      margin-right: 26px;
      cursor: pointer;
      border: 1px solid #ededed;
      border-radius: 12px;
      overflow: hidden;

      .img-box {
        position: relative;
        width: 100%;
        height: 200px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          transition: all 0.6s;
        }

        >img:hover {
          transform: scale(1.2);
        }

        .status {
          display: flex;
          position: absolute;
          right: -1px;
          top: -1px;

          img {
            width: 60px;
            height: 28px;
          }
        }
      }

      // .img-box:hover {
      //   transform: scale(1.2);
      // }

      .container {
        padding: 15px 14px 10px;

        .title {
          width: 100%;
          height: 51px;
          font-size: 14px;
          // line-height: 20px;
          // line-height: 26px;
          font-weight: bold;
          color: #333333;
          line-height: 26px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          // max-height: 51px;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
        }

        .detail {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          // height: 69px;
          font-size: 14px;
          font-weight: 400;
          color: #666666;
          background-color: white;
          margin-top: 12px;

          .detail-item {
            width: 100%;
            // height: 23px;
            // align-items: center;
            // line-height: 23px;
            overflow: hidden;
            // margin-top: 10px;
            line-height: 28px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          span {
            color: #316c2c;
            font-weight: bold;
            font-size: 18px;
            margin-right: 8px;
            line-height: 23px;
          }
        }
      }
    }

    .item:nth-child(4n) {
      margin-right: 0;
    }

    .noneBox {
      display: flex;
      flex-direction: column;
      // justify-content: center;
      align-items: center;
      width: 100%;

      .noneImageBox {
        width: 369px;
        height: 333px;
        // background-color: red;
        margin-top: 161px;
        margin-bottom: 40px;
      }

      .noneText {
        font-size: 24px;
        font-weight: 400;
        color: #6cb8ff;
        margin-bottom: 300px;
      }
    }
  }
}
</style>
