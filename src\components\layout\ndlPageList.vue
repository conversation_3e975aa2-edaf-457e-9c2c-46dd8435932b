<template>
  <ndl-page class="ndl-page-list-box">
    <div v-if="showSearch" class="ndl-page-list-search">
      <slot name="search" />
    </div>
    <div class="list" :class="showSearch ? 'list-height' : 'no-search-list-height'">
      <slot name="list" />
    </div>
  </ndl-page>
</template>

<script>
import ndlPage from "./ndlPage.vue";
export default {
  components: {
    ndlPage,
  },
  props: {
    showSearch: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang='scss' scoped>
.ndl-page-list-box {
  .ndl-page-list-search {
    width: 100%;
    height: 28px;
    display: flex;
    margin-bottom: 14px;
  }
  .list {
    width: 100%;
  }
  .list-height {
    height: calc(100% - 42px);
  }
  .no-search-list-height {
    height: 100%;
  }
}
</style>
