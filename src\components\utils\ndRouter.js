import axios from '@/http/index';
import { Message } from "element-ui";
// 获得服务器路径
function getServerPath() {
    if (typeof getContextPath === "function") {
      return getContextPath();
    } else {
      return "/sz_product_new";
    }
  }
const ndRouter = {
    config: {
        serverName: "nd-oneThree",
        openType: "newTap", // newTap newWindow
        toSz: false,
        szSystemName: ""
    },
    // 一户三权、村居这样跳
    push: function (menuCode, query, config) {
        console.log(menuCode, query, config);
        console.log(getServerPath(),"getServerPath===========");
        if(config){
            if(config.serverName){
                this.config.serverName=config.serverName
            }
            if(config.openType){
                this.config.openType=config.openType
            }
            if(config.toSz){
                this.config.toSz=config.toSz
                this.config.szSystemName=config.szSystemName
            }
        }
        if (query == undefined) {
            query = ""
        } else {
            let arr = [];
            for (let key in query) {
                let value = query[key];
                //把值写入到arr中
                arr.push(`${key}=${value}`)
            }
            //通过逗号连接， 转换成{ ， ， ， }形式
            // return `{${arr.join(',')}}`
            query = `${arr.join('&')}`
            console.log(query);
        }
        // 找不到再去调接口
        let params = {
            transferKey: menuCode
        }
        axios({
            url: "/fc/function/find",
            method: "get",
            data: params,
            serverName: this.config.serverName
        }).then((res) => {
            let targetInfo = res.data.data;
            //funUrl截取
            let funUrlLeg = targetInfo.funUrl.split("/#/");
            console.log(funUrlLeg[0], funUrlLeg[1]);
            //allPath字符串转数组
            let keyPathLeg = targetInfo.allPath.split("|");
            let keyPath = [];
            keyPathLeg.forEach((item, index) => {
               if (index == (keyPathLeg.length - 1)) {
                    //最后一个不管
                } else {
                    keyPath.push(item);
                }
            });
            console.log(keyPath,"keyPath");
            // np 的| 转 %2C
            let np=targetInfo.np.replaceAll("|","%2C")

            let tabNode = {};
            tabNode.title = targetInfo.funName;
            if (this.config.toSz) {
                if(query!=""){
                    tabNode.path = this.config.szSystemName + '/' + funUrlLeg[0] + '?funcId='+ targetInfo.id +'&np=' + np + '&ac=' + targetInfo.ac + '&'+query +'/#/' + funUrlLeg[1];
                }else{
                    tabNode.path = this.config.szSystemName + '/' + funUrlLeg[0] + '?funcId=' + targetInfo.id + '&np=' + np + '&ac=' + targetInfo.ac + '/#/' + funUrlLeg[1];
                }
            } else {
                // tabNode.path = res.data.data.url;
            }
            tabNode.menuorigin = "remote";
            tabNode.component = "";
            tabNode.key = targetInfo.functionId;
            tabNode.keyPath = keyPath;
            tabNode.name = targetInfo.funName;
            tabNode.parent = "";

            if (this.config.openType === "newTap") {
                top.postMessage({
                    data: {
                        info: "success",
                        tabNode: tabNode,
                        key: "ndsc-menu-tab-add"
                    }
                }, '*');
            } else {
                console.log(this.config.szSystemName);
                // 往三资跳
                if (this.config.toSz) {
                    // 传参是否为空
                    if(query!=""){
                        window.open(this.config.szSystemName + '/' + funUrlLeg[0] + '?funcId=' + targetInfo.id + '&np=' + np + '&ac=' + targetInfo.ac  +'&'+query +'/#/' + funUrlLeg[1], '_blank');
                    }else{
                        window.open(this.config.szSystemName + '/' + funUrlLeg[0] + '?funcId=' + targetInfo.id + '&np=' + np + '&ac=' + targetInfo.ac + '/#/' + funUrlLeg[1], '_blank');
                    }
                } else {
                    // 将来有这种情况再写
                }
            }
        });
    },

    // 三资里这样跳
    pushSz: function (menuCode, query) {
        console.log(menuCode, query); 
            this.config.serverName='nd-szProduct';
        if (query == undefined) {
            query = "";
        } else {
            var titleText=query.titleText;
            let arr = [];
            for (let key in query) {
                let value = query[key];
                //把值写入到arr中
                arr.push(`${key}=${value}`);
            }
            //通过逗号连接， 转换成{ ， ， ， }形式
            // return `{${arr.join(',')}}`
            query = `${arr.join('&')}`;
            console.log(query);
        }
        // 找不到再去调接口
        let params = {
            key: menuCode,
        }
        axios({
            url: getServerPath()+"/apiPz/getMenuByKey.do",
            method: "post",
            data: params,
            serverName: this.config.serverName
        }).then((res) => {
            let targetInfo = res.data.data;
            // 菜单权限
            if(targetInfo.ok){
                // funcId为空不跳转
                if (targetInfo.key){
                    // 获取时间戳
                    let time = parseInt(new Date().getTime() / 1000) + '';
                    let id=time;
                    console.log(id,"时间戳id");
                    console.log(res.data.data);
                    //path截取
                    let funUrlLeg = targetInfo.path.split("/#/");
                    console.log(funUrlLeg[0], funUrlLeg[1]);
                    let tabNode = {};
                    console.log(funUrlLeg[0] + '&funcId='+ targetInfo.key +'/#/' + funUrlLeg[1]);
                    // 传参是否为空
                    if(query!=""){
                        tabNode.path = funUrlLeg[0] +'&np=' + targetInfo.np + '&funcId='+ targetInfo.key +'&'+query +'/#/' + funUrlLeg[1];
                    }else{
                        tabNode.path = funUrlLeg[0] +'&np=' +targetInfo.np + '&funcId='+ targetInfo.key +'/#/' + funUrlLeg[1];
                    }
                    tabNode.path=tabNode.path.replace('.do&','.do?')
                    // console.log(tabNode.path,"tabNode.path555");
                    // titleText有值就是开一个新的tab
                    if(titleText==undefined){
                        tabNode.key = targetInfo.key;
                        tabNode.keyPath = targetInfo.allPath;
                        console.log(tabNode.keyPath,"console.log(tabNode.keyPath)")
                        tabNode.title = targetInfo.title;
                        tabNode.name=targetInfo.title;
                    }else{
                        let newAllPath=[]
                        for(let i=0;i<targetInfo.allPath.length;i++){
                            newAllPath.push(targetInfo.allPath[i])
                            if(i==targetInfo.allPath.length-1){
                                newAllPath.push(id)
                            }
                        }
                        tabNode.id=id
                        tabNode.key = id;
                        tabNode.keyPath = newAllPath;
                        console.log(tabNode.keyPath,"console.log(tabNode.keyPath)")
                        // 替换
                        tabNode.title=titleText;
                        tabNode.name=titleText;
                    }
                    tabNode.menuorigin = "remote";
                    tabNode.component = "";
                    tabNode.parent = "";      
                    parent.window.vueObject.$children[0].$children[0].$children[1].addTab(tabNode);
                    parent.window.vueObject.$children[0].$children[0].highlightMenu(tabNode);
                    parent.window.vueObject.$children[0].$children[0].reloadTab(tabNode);
                }else{
                    Message.warning("请配置对应菜单!");
                }
            }
            else{
                Message.warning("无权访问，请配置对应菜单权限!");
            }
        });
    },
};

const MyPlugin = {};
MyPlugin.install = function (Vue, options) {
    Vue.prototype.$ndRouter = ndRouter
}

export default MyPlugin