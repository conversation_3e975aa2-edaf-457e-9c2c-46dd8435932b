<template>
  <div class="main-cont">
    <div v-if="pageData.length !== 0" class="list-content">
      <div class="list-item" @click.stop="handleMore(item)" v-for="(item, index) in pageData" :key="index">
        <div class="item-top">
          <div class="title">
            {{ item.title }}
          </div>
          <div class="time">
            <div>{{ item.writeTime }}</div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="list-content">
      <empty :boxHeight="300"></empty>
    </div>
    <!-- 分页器 -->
    <div class="pagination">
      <nd-pagination :page-size="pager.pageSize" layout="total, prev, pager, next, jumper" :total="pager.total"
        :total-page="totalPage" :current-page="pager.pageNo" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
      <div class="current-total">
        当前页共{{ pageData.length }}条
      </div>
    </div>
  </div>
</template>

<script>
import ndPagination from '@/components/ndPagination.vue';
import empty from '@/views/empty/index.vue';

export default {
  name: 'ColumnListTypeC',
  components: {
    ndPagination,
    empty,
  },
  props: {
    columnId: {
      type: String,
      required: true
    },
    breadcrumbName: {
      type: String,
      default: '资讯详情',
    },
    bannerType: {
      type: String,
      default: '1'
    },
    parentBreadcrumbName: {
      type: String,
      default: ''
    },
    // 完整的菜单路径（支持多层级）
    menuPath: {
      type: Array,
      default: () => []
    },
    // 新增：简化模式，自动推断面包屑
    autoInferBreadcrumb: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      listData: [],
      pager: {
        pageNo: 1,
        pageSize: 20,
        total: 0,
        pageSizes: [10, 30, 50, 100],
      },
      totalPage: 1,
    };
  },
  computed: {
    pageData() {
      // 后端分页，直接返回接口数据
      return this.listData;
    },

    // 自动推断的面包屑信息
    inferredBreadcrumbInfo() {
      if (!this.autoInferBreadcrumb) {
        return {
          breadcrumbPath: this.getBreadcrumbPath(),
          bannerType: this.bannerType
        };
      }

      // 根据路由名称自动推断面包屑和bannerType
      const routeNameMap = {
        'newsCenter': { parent: '新闻中心', bannerType: '1' },
        'businessRule': { parent: '业务规则', bannerType: '3' },
        'lawsRegulation': { parent: '政策法规', bannerType: '4' },
        'profileDownload': { parent: '资料下载', bannerType: '5' },
        'aboutUs': { parent: '关于我们', bannerType: '6' },
        'interacteCommunication': { parent: '互动交流', bannerType: '7' },
        'index': { parent: '首页', bannerType: '1' },
        'home': { parent: '首页', bannerType: '1' },
        '': { parent: '首页', bannerType: '1' }, // 处理路由名称为空的情况
        null: { parent: '首页', bannerType: '1' }, // 处理路由名称为null的情况
        undefined: { parent: '首页', bannerType: '1' } // 处理路由名称为undefined的情况
      };

      // 获取当前路由名称，处理空值情况
      const currentRouteName = this.$route.name || '';
      const routeInfo = routeNameMap[currentRouteName] || { parent: '首页', bannerType: '1' };

      // 构建面包屑路径
      let breadcrumbPath = '';
      if (this.menuPath && this.menuPath.length > 0) {
        const pathToShow = this.menuPath.slice(0, 2);
        breadcrumbPath = pathToShow.map(menu => menu.title).join(' > ');
      } else if (this.parentBreadcrumbName) {
        breadcrumbPath = this.parentBreadcrumbName;
      } else {
        // 自动推断：最多显示两级
        const parts = [];

        // 如果面包屑名称已经包含 '>'，说明是多层级路径，直接使用
        if (this.breadcrumbName && this.breadcrumbName.includes(' > ')) {
          breadcrumbPath = this.breadcrumbName;
        } else if (this.breadcrumbName && this.breadcrumbName === routeInfo.parent) {
          // 如果当前面包屑名称与路由父级相同，只显示一级
          parts.push(this.breadcrumbName);
          breadcrumbPath = parts.join(' > ');
        } else {
          // 否则显示：父级 > 当前面包屑名称（最多两级）
          if (routeInfo.parent) {
            parts.push(routeInfo.parent);
          }
          if (this.breadcrumbName && this.breadcrumbName !== '资讯详情') {
            parts.push(this.breadcrumbName);
          }
          // 确保最多只有两级
          breadcrumbPath = parts.slice(0, 2).join(' > ');
        }
      }

      return {
        breadcrumbPath,
        bannerType: routeInfo.bannerType
      };
    }
  },
  watch: {
    columnId: {
      handler(newVal) {
        if (newVal) {
          this.pager.pageNo = 1;
          this.getData();
        }
      },
      immediate: true
    }
  },
  methods: {
    getData() {
      if (!this.columnId) return ;
      this.$ajax({
        url: '/jcms/site/manuscript/listPage',
        method: 'post',
        serverName: 'nd-ss',
        data: {
          columnId: this.columnId,
          pageNo: this.pager.pageNo,
          pageSize: this.pager.pageSize,
        }
      }).then((res) => {
        if (res.data.code === 200) {
          this.listData = res.data.data.records || [];
          this.pager.total = res.data.data.total || 0;
          this.totalPage = Math.ceil(this.pager.total / this.pager.pageSize);

          // 调试信息
          console.log('分页数据:', {
            pageNo: this.pager.pageNo,
            pageSize: this.pager.pageSize,
            total: this.pager.total,
            totalPage: this.totalPage,
            dataLength: this.listData.length
          });
        }
      }).catch(error => {
        console.error('获取列表数据失败:', error);
      });
    },

    handleMore(item) {
      // 使用计算属性获取面包屑信息
      const breadcrumbInfo = this.inferredBreadcrumbInfo;

      this.$router.push({
        path: 'columnDetailView',
        query: {
          id: item.id,
          breadcrumbName: breadcrumbInfo.breadcrumbPath,
          fromPage: this.$route.name,
          bannerType: breadcrumbInfo.bannerType
        },
      });
    },

    // 原有的面包屑构建逻辑（保持向后兼容）
    getBreadcrumbPath() {
      if (this.menuPath && this.menuPath.length > 0) {
        const pathToShow = this.menuPath.slice(0, 2);
        return pathToShow.map(menu => menu.title).join(' > ');
      } else if (this.parentBreadcrumbName) {
        return this.parentBreadcrumbName;
      } else {
        return this.breadcrumbName;
      }
    },

    handleCurrentChange(e) {
      this.pager.pageNo = e;
      this.getData();
    },

    handleSizeChange(e) {
      this.pager.pageSize = e;
      this.pager.pageNo = 1;
      this.getData();
    },
  },
};
</script>

<style lang="scss" scoped>
.main-cont {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 400px;
}

.list-content {
  width: 1300px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e5e5;
  margin-top: 21px;
  padding: 20px 0px 24px 24px;

  .list-item {
    cursor: pointer;
    padding-left: 24px;
    margin-right: 20px;
    border-bottom: 1px dashed #E5E5E5;
  }

  .item-top {  
    display: flex;  
    flex-direction: row;  
    align-items: center;  
    justify-content: space-between;  

    .title {  
        font-family: Microsoft YaHei;  
        font-weight: 400;  
        font-size: 14px;  
        color: #333333;  
        line-height: 50px;  
        position: relative;  
        z-index: 1;  
    }  

    .title:before {  
        content: '•';  
        color: black;  
        margin-right: 0.5em;  
        position: absolute;  
        left: -1em;  
        z-index: -1;  
        transition: color 0.3s;  
    }  

    .title:hover:before {  
        color: #ed911f;  
    }  

    .time {  
        font-family: Microsoft YaHei;  
        font-weight: 400;  
        font-size: 14px;  
        color: #333333;  
        line-height: 40px;  
        transition: color 0.3s;
    }  

    .time:hover {  
        color: #ed911f; 
    }  

    &:hover {  
        border: none;  

        .title {  
            font-size: 14px;  
            color: #ed911f;  
            text-decoration: underline;  
        }  

        .time {  
            font-size: 14px;  
            color: #ed911f;  
            text-decoration: none;  
        }  

        .title:before {  
            color: #ed911f;
        }  
    }  
}

  .item-bottom:not(:last-child) {
    margin-bottom: 20px;
  }
}

.pagination {
  width: 100%;
  margin-top: 14px;
  display: flex;
  align-items: center;
  justify-content: center;

  .current-total {
    font-weight: 400;
    color: #606266;
    font-size: 13px;
  }

  ::v-deep .nd-pagination-box {
    width: auto;
    margin-right: 10px;
  }

  :v-deep .el-pagination button:disabled {
    background: none;
  }

  ::v-deep .el-pagination .btn-next,
  .el-pagination .btn-prev {
    background: none;
  }

  ::v-deep .el-pagination .btn-prev:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pagination .btn-next:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pager li {
    background: none;
  }

  ::v-deep .el-pager li.active {
    color: #fff;
    background: #f2ae0f;
  }

  ::v-deep .el-pager li.active:hover {
    color: #fff;
  }

  ::v-deep .el-pager li:hover {
    color: #f2ae0f;
  }
}
</style>










