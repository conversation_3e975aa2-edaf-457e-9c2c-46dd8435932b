<template>
  <div class="phone-dialog-box">
    <nd-dialog ref="phoneUpload" :title="title" :width="width" :height="height" append-to-body :before-close="handleClose"
      center>
      <div class="content">
        <div class="sjcc_rwm">
          <div id="qrCodeDiv" ref="qrCodeDiv" class="qrcode" />
          <div v-if="isshow" class="sjcc_sx" style="cursor: pointer" @click="reQrCode">
            <p class="new" style="margin-top: 70px">二维码已过期请刷新</p>
            <p class="new">点击刷新</p>
          </div>
        </div>
        <div class="text">
          <p class="title">操作说明：</p>
          <p class="one">1.使用手机微信或支付宝扫一扫功能，扫描上方二维码，通过手机端上传图片，</p>
          <p class="two">手机端支持批量上传图片，附件与电脑端实时同步。</p>
          <p class="three">2.二维码时效性为5分钟,过期后请刷新更新</p>
        </div>
      </div>
      <template #footer>
        <div>
          <nd-button @click="close"> 关闭 </nd-button>
        </div>
      </template>
    </nd-dialog>
  </div>
</template>

<script>
import ndDialog from '@/components/ndDialog.vue';
import ndButton from '@/components/ndButton.vue';
import QRCode from 'qrcodejs2-fix';
export default {
  components: {
    ndDialog,
    ndButton,
  },
  props: {},
  data() {
    return {
      url: '',
      szUrl: '', //三资前缀
      qrUrl: '', //生成二维码的地址
      title: '手机扫码上传图片',
      width: '650px',
      height: '355px',
      isshow: false, //是否显示二维码过期的遮罩层
      params: {
        dataId: '',
        functionId: '',
        fileTypeId: '',
        isPdf: '',
        functionName: '',
        generateOrRefresh: '0',
        redisKey: '',
      }, //资产资源,村居，资金管理生成二维码要传的参数

      voucherParams: {
        activeUrl: '', //二维码url前缀
        doId: '', //即附件的dataId
        ztId: '', //账套id
        sId: '', //当前登录用户的sessionId
        key: '', //二维码对应的redis中的key
        uId: '', //凭证用户id
        fileFromWhere: '', //附件来源
        isCheckZt: '',
        isTimeout: '', //二维码是否失效 true 失效,false 有效
      }, //凭证录入修改村居报账模块生成二维码要传的参数
      flag: '',
      timer1: null, //定时器名称检验二维码是否过期
      timer2: null, //定时器名称定期调用父组件方法
      key: '', //用来判断二维码是否过期的钥匙
      modelType: '', //三资模块类型
    };
  },
  watch: {},
  mounted() { },
  created() {
    // window.addEventListener("keydown", this.handkeyCode, true); //开启监听键盘按下事件
  },
  beforeUnmount() { },

  methods: {
    // 获取二维码地址
    getORCard() {
      if (this.modelType == '1') {
        // 资产资源
        this.$ajax({
          url: this.url + '/szFileNew/qrCodeAddressAccess.do',
          method: 'post',
          data: this.params,
        }).then((res) => {
          if (res.data.code == 0) {
            console.log(this.qrUrl);
            this.qrUrl = res.data.data.url;
            this.key = res.data.data.key;
            this.creatQrCode(this.qrUrl);
          }
        });
      } else if (this.modelType == '2') {
        // 凭证录入，修改
        this.$ajax({
          url: this.url + '/apiPz/getPhoneCodeInitParam.do',
          method: 'post',
          data: {
            dataId: this.voucherParams.dataId,
          },
        }).then((res) => {
          if (res.data.code == 0) {
            this.voucherParams.activeUrl = res.data.data.activeUrl;
            this.voucherParams.fileFromWhere = res.data.data.fileFromWhere;
            this.voucherParams.ztId = res.data.data.ztId;
            this.voucherParams.sId = res.data.data.sId;
            this.voucherParams.uId = res.data.data.uId;
            this.voucherParams.doId = res.data.data.doId;
            this.voucherParams.key = res.data.data.key;
            this.voucherParams.isCheckZt = res.data.data.isCheckZt;
            let qrurl =
              this.voucherParams.activeUrl +
              '/apiPz/openScanWeb.do?ztId=' +
              this.voucherParams.ztId +
              '&sId=' +
              this.voucherParams.sId +
              '&uId=' +
              this.voucherParams.uId +
              '&doId=' +
              this.voucherParams.doId +
              '&fileFromWhere=' +
              this.voucherParams.fileFromWhere +
              '&key=' +
              this.voucherParams.key +
              '&isCheckZt=' +
              this.voucherParams.isCheckZt;
            this.creatQrCode(qrurl);
          } else {
            this.$message({
              type: 'warning',
              message: res.data.msg,
            });
          }
        });
      } else {
        // 凭证单据管理
        this.$ajax({
          url: this.url + '/apiPz/getPzdjPhoneCodeInitParam.do',
          method: 'post',
          data: {
            doId: this.params.dataId,
          },
        }).then((res) => {
          if (res.data.code == 0) {
            this.voucherParams.activeUrl = res.data.data.activeUrl;
            this.voucherParams.uId = res.data.data.userId;
            this.voucherParams.isTimeout = res.data.data.isTimeout;
            this.voucherParams.ztId = res.data.data.zhangtjzId;
            this.voucherParams.key = res.data.data.key;
            this.voucherParams.sId = res.data.data.sessionId;
            let qrurl =
              this.voucherParams.activeUrl +
              '/apiOriginalFileUpload.do?method=openScanWeb&zhangtjzId=' +
              this.voucherParams.ztId +
              '&sessionId=' +
              this.voucherParams.sId +
              '&userid=' +
              this.voucherParams.uId +
              '&key=' +
              this.voucherParams.key + '&doId=' +
              this.params.dataId;
            this.creatQrCode(qrurl);
          } else {
            this.$message({
              type: 'warning',
              message: res.data.msg,
            });
          }
        });
      }
    },
    //生成二维码
    creatQrCode(url) {
      setTimeout(() => {
        document.getElementById('qrCodeDiv').innerHTML = '';
        var that = this;
        var qrcode = new QRCode(that.$refs.qrCodeDiv, {
          text: url,
          width: 200,
          height: 200,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H,
        });
        qrcode._el.title = ''; //隐藏title
      }, 0);
      this.openPhoneUploadCodeSetIntervalTimer();
    },
    //重新刷新二维码
    reQrCode() {
      if (this.modelType == '1') {
        // 三资资产资源
        this.$refs.qrCodeDiv.innerHTML = '';
        this.params.generateOrRefresh = '1';
        this.params.redisKey = this.key;
        this.getORCard();
        this.isshow = false;
      } else if (this.modelType == '2') {
        // 三资凭证
        this.$refs.qrCodeDiv.innerHTML = '';
        var url =
          this.url +
          '/apiPz/reQrCodeBut.do?key=' +
          this.voucherParams.key +
          '&isCheckZt=' +
          '&doId=' +
          this.voucherParams.doId;
        this.voucherParams.isCheckZt;
        this.$ajax({
          url,
          method: 'post',
        }).then((res) => {
          if (res.data.code == 0) {
            this.voucherParams.key = res.data.key;
            let qrurl =
              this.voucherParams.activeUrl +
              '/apiPz/openScanWeb.do?ztId=' +
              this.voucherParams.ztId +
              '&sId=' +
              this.voucherParams.sId +
              '&uId=' +
              this.voucherParams.uId +
              '&doId=' +
              this.voucherParams.doId +
              '&fileFromWhere=' +
              this.voucherParams.fileFromWhere +
              '&key=' +
              this.voucherParams.key +
              '&isCheckZt=' +
              this.voucherParams.isCheckZt;

            this.creatQrCode(qrurl);
          } else {
            this.$message({
              type: 'warning',
              message: res.data.msg,
            });
          }
        });
        this.isshow = false;
      } else {
        // 资产凭据管理
        this.$refs.qrCodeDiv.innerHTML = '';
        var url = this.url + '/voucher_bill.do?method=reQrCodeBut&key=' + this.voucherParams.key + "&doId=" + this.params.dataId;
        this.$ajax({
          url,
          method: 'post',
        }).then((res) => {
          if (res.data.code == 0) {
            this.voucherParams.key = res.data.key;
            let qrurl =
              this.voucherParams.activeUrl +
              '/apiOriginalFileUpload.do?method=openScanWeb&zhangtjzId=' +
              this.voucherParams.ztId +
              '&sessionId=' +
              this.voucherParams.sId +
              '&userid=' +
              this.voucherParams.uId +
              '&key=' +
              this.voucherParams.key;
            this.creatQrCode(qrurl);
          } else {
            this.$message({
              type: 'warning',
              message: res.data.msg,
            });
          }
        });
        this.isshow = false;
      }
    },

    // 检测二维码是否生效
    validPhoneUploadCode() {
      if (this.modelType == '1') {
        // 三资的资产资源
        var url =
          this.url +
          '/szFileNew/validPhoneUploadCodeDialogSetIntervalTimer.do?key=' +
          this.key +
          '&functionName=' +
          this.params.functionName +
          '&flag=' +
          this.flag +
          '&dataId=' +
          this.params.dataId;
        this.$ajax({
          url: url,
          method: 'post',
        }).then((res) => {
          if (res.data.code == 100) {
            this.isshow = true;
          } else {
            this.isshow = false;
          }
        });
      } else if (this.modelType == '2') {
        // 三资的凭证录入修改
        var url =
          this.url +
          '/apiPz/validPhoneUploadCodeDialogSetIntervalTimer.do?ztId=' +
          this.voucherParams.ztId +
          '&sId=' +
          this.voucherParams.sId +
          '&uId=' +
          this.voucherParams.uId +
          '&doId=' +
          this.voucherParams.doId +
          '&fileFromWhere=' +
          this.voucherParams.fileFromWhere +
          '&key=' +
          this.voucherParams.key +
          '&isCheckZt=' +
          this.voucherParams.isCheckZt;
        this.$ajax({
          url: url,
          method: 'post',
        }).then((res) => {
          if (res.data.code == 100) {
            this.isshow = true;
          } else {
            this.isshow = false;
          }
        });
      } else {
        // 资产票据管理
        var url =
          this.url +
          '/voucher_bill.do?method=validPhoneUploadCodeDialogSetIntervalTimer&key=' +
          this.voucherParams.key + "&doId=" + this.params.dataId;
        this.$ajax({
          url: url,
          method: 'post',
        }).then((res) => {
          if (res.data.code == 100) {
            this.isshow = true;
          } else {
            this.isshow = false;
          }
        });
      }
    },

    // 开启检测二维码是否失效的定期器
    openPhoneUploadCodeSetIntervalTimer() {
      //每隔多少秒检查一下二维码是否失效
      this.timer1 = setInterval(() => {
        this.getAccessNew()
      }, 10000);
    },
    getAccessNew() {
      this.$ajax({
        url: '/szFileNew/qrCodeAddressAccessNew.do',
        method: 'post',
        data: {
          dataId: this.voucherParams.dataId,
          functionId: this.params.functionId,
          fileTypeId: this.params.fileTypeId
        },
      }).then((res) => {
        if (res.data.data) {
          if (this.modelType == '1') {
            if (this.key !== res.data.data) {
              this.key = res.data.data
              this.reQrCode1();
            }
          } else if (this.modelType == '2') {
            if (this.voucherParams.key !== res.data.data) {
              this.voucherParams.key = res.data.data
              this.reQrCode1();
            }
          } else {
            if (this.voucherParams.key !== res.data.data) {
              this.voucherParams.key = res.data.data
              this.reQrCode1();
            }
          }
        }
        this.validPhoneUploadCode();
      })
    },
    //重新刷新二维码
    reQrCode1() {
      if (this.modelType == '1') {
        // 三资资产资源
        this.$refs.qrCodeDiv.innerHTML = '';
        let urL = this.qrUrl.replace(/key=.*/, 'key=');
        let qrurl = urL + this.key
        this.creatQrCode(qrurl);
        this.isshow = false;
      } else if (this.modelType == '2') {
        // 三资凭证
        this.$refs.qrCodeDiv.innerHTML = ''
        let qrurl =
          this.voucherParams.activeUrl +
          '/apiPz/openScanWeb.do?ztId=' +
          this.voucherParams.ztId +
          '&sId=' +
          this.voucherParams.sId +
          '&uId=' +
          this.voucherParams.uId +
          '&doId=' +
          this.voucherParams.doId +
          '&fileFromWhere=' +
          this.voucherParams.fileFromWhere +
          '&key=' +
          this.voucherParams.key +
          '&isCheckZt=' +
          this.voucherParams.isCheckZt;
        this.creatQrCode(qrurl);
        this.isshow = false;
      } else {
        // 资产凭据管理
        this.$refs.qrCodeDiv.innerHTML = '';
        let qrurl =
          this.voucherParams.activeUrl +
          '/apiOriginalFileUpload.do?method=openScanWeb&zhangtjzId=' +
          this.voucherParams.ztId +
          '&sessionId=' +
          this.voucherParams.sId +
          '&userid=' +
          this.voucherParams.uId +
          '&key=' +
          this.voucherParams.key;
        this.creatQrCode(qrurl);
        this.isshow = false;
      }
    },
    //定期调用父组件方法更新附件
    updateFile() {
      this.timer2 = setInterval(() => {
        this.$emit('updateFile');
      }, 4000);
    },

    // 关闭检测二维码是否失效的定期器
    // closePhoneUploadCodeSetIntervalTimer() {

    // },
    open(dataId, functionId, functionName, fileTypeId, isPdf, flag, url, modelType) {
      console.log(modelType, "modelType");
      console.log(functionId);
      this.params.dataId = dataId;
      // console.log(functionId,"functionIdfunctionIdfunctionId");
      this.params.functionId = functionId;
      this.params.functionName = functionName;
      this.params.fileTypeId = fileTypeId;
      this.params.isPdf = Number(isPdf) + '';
      this.flag = flag;
      this.szUrl = url;
      this.voucherParams.dataId = dataId;
      this.modelType = modelType;
      console.log(this.params, 'this.params');
      console.log(this.modelType, 'this.modelType');

      this.$refs.phoneUpload.open();
      this.$nextTick(() => {
        this.getORCard();
      });
      this.updateFile();
      // this.$parent.submitTrueSave();
    },

    // 关闭手机上传弹框时手机端无法上传图片接口
    closePhoneUpload() {
      if (this.modelType == '1') {
        let key = this.key;
        this.$ajax({
          url: this.url + '/szFileNew/delRedisKey.do',
          method: 'post',
          data: {
            key,
          },
        }).then((res) => {
          if (res.data.code == 0) {
            console.log('关闭手机端成功');
          } else {
            this.$message({
              message: res.data.msg,
              type: 'warning',
            });
          }
        });
        this.$ajax({
          url: '/szFileNew/qrCodeAddressAccessByDeleteKey.do',
          method: 'post',
          data: {
            dataId: this.voucherParams.dataId,
            functionId: this.params.functionId,
            fileTypeId: this.params.fileTypeId
          },
        }).then((res) => {
        })
      } else if (this.modelType == '3') {
        this.$ajax({
          url: '/szFileNew/qrCodeAddressAccessByDeleteKey.do',
          method: 'post',
          data: {
            dataId: this.voucherParams.dataId,
            functionId: '',
            fileTypeId: this.params.fileTypeId
          },
        }).then((res) => {
        })
      } else {
        let key = this.voucherParams.key;
        this.$ajax({
          url: this.url + '/szFileNew/delRedisKey.do',
          method: 'post',
          data: {
            key,
          },
        }).then((res) => {
          if (res.data.code == 0) {
            console.log('关闭手机端成功');
          } else {
            this.$message({
              message: res.data.msg,
              type: 'warning',
            });
          }
        });
        this.$ajax({
          url: '/szFileNew/qrCodeAddressAccessByDeleteKey.do',
          method: 'post',
          data: {
            dataId: this.voucherParams.dataId,
            functionId: this.params.functionId,
            fileTypeId: this.params.fileTypeId
          },
        }).then((res) => {
        })
      }
    },
    // 点击右上角×按钮
    handleClose() {
      this.close();
    },
    // 点击取消关闭弹框
    close() {
      clearInterval(this.timer1); //清除定时器
      this.timer1 = null;
      clearInterval(this.timer2); //清除定时器
      this.timer2 = null;
      this.closePhoneUpload();
      this.$refs.phoneUpload.close();
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  position: relative;

  // .qrcode {
  //   position: absolute;
  //   top: 90px;
  //   left: 250px;
  // }
  .sjcc_rwm {
    position: absolute;
    position: relative;
    top: 20px;
    left: 217px;
    width: 200px;
    height: 200px;

    .sjcc_rwm img {
      width: 100%;
      height: 100%;
    }

    .sjcc_sx {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);

      .new {
        text-align: center;
        font-size: 12px;
        color: #ffffff;
        line-height: 24px;
      }
    }
  }

  .text {
    margin-top: 40px;
    padding-left: 112px;
    font-size: 12px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #777777;
    line-height: 24px;

    .title {
      padding-left: 0px;
    }

    .one {
      padding-left: 0px;
    }

    .two {
      padding-left: 10px;
    }

    .three {
      padding-left: 0px;
    }
  }
}
</style>
