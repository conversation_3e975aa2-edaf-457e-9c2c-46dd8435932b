<template>
  <div class="ndb-input-frequently-box" @click.stop>
    <div class="frequently-box-input">
      <nd-input
        ref="inputRef"
        v-model="commonUser"
        :disabled="disabled"
        width="82%"
        maxlength="50"
        @focus="inputFocus($event)"
        @input="inputValue($event)"
      />
      <span v-if="!disabled" class="frequently-box-input-text" @click="setToCommon()">设为常用</span>
      <span v-else class="frequently-box-input-text" style="cursor:no-drop">设为常用</span>
    </div>
    <div v-if="isShowBox" :key="refreshDrop" :class="isShowBox ? 'input-drop-down-box-show' : 'input-drop-down-box'">
      <div class="drop-header-box">
        <span class="drop-header-text">常用交款单位/人</span>
        <span class="drop-header-btn" @click="clearAll">全部清除</span>
      </div>
      <ul v-if="notFindList" class="drop-content-box" @scroll.passive="getScroll($event)">
        <li
          v-for="(item) in userList"
          :key="item.id"
          class="drop-item"
          @click="chooseCommonUser(item.personName)"
        >
          <span class="drop-item-name">{{ item.personName }}</span>
          <i class="el-icon-close" @click.stop="clearOne(item.id)" />
        </li>
        <li v-if="isLoading" class="loading-li">
          加载中。。。
        </li>
      </ul>
      <div v-else class="drop-content-box-no-List">
        无匹配数据
      </div>
    </div>
  </div>
</template>

<script>
import ndInput from '../ndInput.vue';
export default {
    components: {
        ndInput
    },
    model: {
        prop: "vmData",
        event: "selectData",
    },
    props: {
        vmData: {
            type: String,
            default() {
                return "";
            },
        },
        deptId: {
            type: String,
            default: ""
        },
        itemUnit: {
            type: String,
            default: ""
        },
        disabled: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            commonUser: "",
            commonId: "",
            isShowBox: false,
            notFindList: true,
            active: true,
            isLoading: false,
            userList: [],
            refreshDrop:0,
            params: {
                deptId: "",
                personName: "",
                page: 1,
                pageSize: 10,
            },
            haveMore:true
        };
    },
    watch: {
        itemUnit: {
            handler(value) {
                this.commonUser = value
            }
        }
    },

    mounted() {
        document.addEventListener("click", this.closeShowBox);
    },

    beforeUnmount() {
        document.removeEventListener("click", this.closeShowBox);
    },

    methods: {
        //input获取焦点
        inputFocus() {
            console.log(this.deptId);
            if (this.deptId === "") {
                this.$message.warning("请先选择收款单位")
            } else {
                this.params.deptId = this.deptId
                this.isShowBox = true
                this.getCommonPeople(this.commonUser)
            }
        },

        //输入事件
        inputValue(value) {
            this.params.page = 1
            this.getCommonPeople(value)
            this.$emit('selectData', value);
        },

        //获取常用人信息
        getCommonPeople(value) {
            var params = this.params
            params.personName = value
            this.$ajax({
                url: "/szProceedInfo/forCommonPeople.do",
                method: "post",
                data: params,
            }).then((res) => {
                console.log(res, "getCommonPeople");
                if (this.params.page > 1) {
                    if (res.data.data.list.length != 0) {
                        this.userList = [...this.userList,...res.data.data.list]
                        this.haveMore = true
                        this.notFindList = true
                    } else {
                        this.haveMore = false
                    }
                } else {
                    console.log(3);
                    this.userList = res.data.data.list
                    this.haveMore = true
                    this.notFindList = true
                }
            });
        },

        //设为常用联系人
        setToCommon() {
            if (this.commonUser == "") {
                this.$message.warning("请填写交款单位（人）")
            } else {
                var params = {
                    deptId: this.deptId,
                    personName: this.commonUser,
                };
                this.$ajax({
                    url: "/szProceedInfo/addCommonPeople.do",
                    method: "post",
                    data: params,
                }).then((res) => {
                    if (res.data.code === 0) {
                        this.$message.success('添加成功')
                        this.getCommonPeople(this.commonUser)
                    } else {
                        this.$message.warning(res.data.msg)
                    }
                });
            }
        },

        //关闭弹窗
        closeShowBox() {
            if (this.isShowBox === true) {
                this.isShowBox = false;
            }
        },

        //获取触底
        getScroll(event) {
            let scrollBottom =
                event.target.scrollHeight -
                event.target.scrollTop -
                event.target.clientHeight
            console.log('scrollBottom :>> ', scrollBottom)
            if (scrollBottom < 1) {
                if(this.haveMore){
                    this.params.page++
                    this.getCommonPeople(this.commonUser)
                }else{
                    this.$message.warning('暂无更多常用联系人')
                }
            }
        },

        //选取常用人
        chooseCommonUser(value) {
            this.commonUser = value
            this.commonId = this.userList[value]
            this.$emit('selectData', value);
            this.closeShowBox()
        },

        //清空全部
        clearAll() {
            this.$confirm('确定要清空全部的常用交款单位（人）吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                var params = {
                    deptId: this.deptId,
                    deleteFlag: "0"
                };
                this.$ajax({
                    url: "/szProceedInfo/deleteCommonPeople.do",
                    method: "post",
                    data: params,
                }).then((res) => {
                    if (res.data.code === 0) {
                        this.userList = []
                        this.$message.success('清除成功')
                        this.notFindList = false
                    } else {
                        this.$message.warning(res.data.msg)
                    }
                });
            }).catch(() => {

            });
        },

        //删除一个
        clearOne(id) {
            var params = {
                personId: id,
                deleteFlag: "1"
            };
            this.$ajax({
                url: "/szProceedInfo/deleteCommonPeople.do",
                method: "post",
                data: params,
            }).then((res) => {
                if (res.data.code === 0) {
                    this.userList.map((item, index) => {
                        if (item.id === id) {
                            this.userList.splice(index, 1)
                            this.$message.success('删除成功')
                            this.haveMore = true
                            if (this.userList.length === 0) {
                                this.notFindList = false
                            }
                        }
                    });
                } else {
                    this.$message.warning(res.data.msg)
                }
            });
        }
    },
};
</script>

<style lang="scss" scoped>
.ndb-input-frequently-box {
    position: relative;

    .input-drop-down-box {
        position: absolute;
        top: 35px;
        left: 0px;
        width: 275px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background-color: #fff;
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
        padding-bottom: 8px;
        display: none;
        overflow: hidden;
    }

    .input-drop-down-box-show {
        position: absolute;
        top: 35px;
        left: 0px;
        width: 275px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background-color: #fff;
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
        animation: showAnimate .1s linear forwards;
        padding-bottom: 6px;
        overflow: hidden;
        z-index: 1;
    }

    @keyframes showAnimate {
        from {
            max-height: 0;
        }

        to {
            display: block;
            max-height: 250px;
        }
    }

    .frequently-box-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }

    .frequently-box-input-text {
        color: #0098FF;
        font-size: 12px;
        cursor: pointer;
    }

    .drop-content-box::-webkit-scrollbar {
        width: 6px;
        height: 12px !important;
    }

    .drop-content-box::-webkit-scrollbar-thumb {
        width: 4px;
        height: 4px;
        background-color: #d4e6fb;
        border-radius: 10px;
    }

    .drop-content-box::-webkit-scrollbar-track {
        background-color: #f2f7ff;
    }

    .drop-header-box {
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        height: auto;
        padding: 6px 8px;

        .drop-header-text {
            color: #999999;
        }

        .drop-header-btn {
            color: #0098FF;
            cursor: pointer;
        }
    }

    .drop-content-box {
        max-height: 205px;
        overflow-y: auto;

        .drop-item {
            list-style: none;
            font-size: 12px;
            color: #555;
            display: flex;
            align-items: center;
            justify-content: space-between;
            line-height: 23px;
            padding: 0 8px;
            cursor: pointer;
        }

        .drop-item:hover {
            background-color: #f5f7fa;
        }

        .drop-item-name {
            width: 230px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .el-icon-close {
            color: #0098FF;
            font-weight: bold;
            cursor: pointer;
        }

    }

    .drop-content-box-no-List {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #555;
        height: 50px;
    }

    .loading-li {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #555;
    }

}
</style>