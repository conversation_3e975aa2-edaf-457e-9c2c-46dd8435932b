<template>
  <div class="nd-select-box" :style="{ width: width }">
    <el-select v-bind="$attrs" ref="select" popper-class="nd-select-popper" v-on="$listeners">
      <slot />
    </el-select>
  </div>
</template>
<script>
export default {
  props: {
    width: {
      type: String,
      default: "220px",
    },
  },
  data() {
    return {
      
    };
  },
  mounted() { 

  },
  methods: {

  },
};
</script>
<style lang="scss">
.nd-select-popper {
  .el-select-dropdown__item {
    font-size: 12px;
  }

  .el-select-dropdown__empty {
    font-size: 12px;
  }
}
</style>
<style lang="scss" scoped>
.nd-select-box {
  ::v-deep .el-select {
    width: 100%;
  }

  ::v-deep .el-input__inner {
    width: 100%;
    height: 28px;
    line-height: 28px;
    border-radius: 0px;
    font-size: 12px;
    padding: 0 10px;
  }

  ::v-deep .el-input__icon {
    line-height: 100%;
  }

}
</style>
