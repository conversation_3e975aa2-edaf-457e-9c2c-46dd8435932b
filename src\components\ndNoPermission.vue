<template>
  <div class="nd-page-box">
    <div v-if="noPermission === true" class="no-permission-box">
      <div>
        <el-image :src="img" class="image" />
        <div class="tip">
          <slot name="tip">
            很抱歉，您没有权限访问该功能!
          </slot>
        </div>
        <slot />
      </div>
    </div>
  </div>
</template>

<script>
import img from '../assets/images/noassesstips.png'
export default {
    data() {
        return {
            img: img,
            noPermission: true,
        };
    },
    mounted() {
    },
    methods: {

    },
};
</script>
<style lang='scss' scoped>
.nd-page-box {
    width: 100%;
    height: calc(100vh - 80px);


    .no-permission-box {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        div { 
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .image {
                display: block;
                height: 300px;
                // margin: 0 auto;
            }

            .tip {
                margin-top: 20px;
                font-size: 28px;
                font-weight: 500;
                color: #666666;
                text-align: center;
            }
        }
    }
}
</style>