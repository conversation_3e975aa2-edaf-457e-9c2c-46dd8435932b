<template>
  <div class="ndb-select-organize-tree-box">
    <nd-select
      id="wbJspTreeSelect"
      ref="ndSelect"
      :value="valueTitle"
      placeholder="地区"
      :width="width"
      @focus="stateChange"
    >
      <el-option :value="valueTitle" :label="valueTitle">
        <el-tree
          id="tree-option"
          highlight-current
          :load="loadNode"
          lazy
          :props="defaultProps"
          node-key="id"
          :default-expanded-keys="treeExpandIdList"
          @node-click="handleNodeClick"
        />
      </el-option>
    </nd-select>
  </div>
</template>
<script>
import ndSelect from "@/components/ndSelect.vue";
export default {
  components: {
    ndSelect,
  },
  props: {
    treeName: {
      type: String,
      default: "",
    },
    width: {
      type: String,
      default: "220px",
    },
  },

  data() {
    return {
      valueTitle: '',
      valueID: '',
      currentNode: [],
      treeExpandIdList: [],
      serverName: "nd-oneThree",
      defaultProps: {
        id: 'id',
        level: "level",
        label: 'areaName',
        children: 'children',
        isLeaf: 'leaf'
      },
    }
  },
  mounted() {
    //初始化el-select IE下光标闪现的问题
    this.stateChange();
    //初始化滚动条
    let scrollWrap = document.querySelectorAll('.el-scrollbar .el-select-dropdown__wrap')[0];
    if (scrollWrap) {
      scrollWrap.style.cssText = 'margin: 0px; max-height: none; overflow: hidden;'
    }
    let scrollBar = document.querySelectorAll('.el-scrollbar .el-scrollbar__bar');
    scrollBar.forEach(ele => ele.style.width = 0);
  },
  methods: {
    // 获得树数据
    loadNode(node, resolve) {
      if (node.level === 1) {
        return resolve(node.data.children);
      }
      var id = "";
      if (node.parent === null) {
          id = "";
      } else {
        id = node.data.id;
      }
      var params = {
        areaId: id,
      };
      if (node.level === 0) {
        this.$ajax({
          method: "get",
          url: "/area/children/findAll",
          data: params,
          serverName: this.serverName
        }).then((response) => {
          if (response.data.code == 200) {
            this.currentNode.push(response.data.data)
            this.$emit('func', this.currentNode[0]);
            this.valueTitle = this.currentNode[0].areaName
            this.currentNode.forEach(element => {
              this.treeExpandIdList.push(element.id);
              if (element.hasChild == false) {
                element.leaf = true
              }
            });
            return resolve(this.currentNode);
          } else {
            this.$message.error("获取地区信息失败！");
          }
        }).catch(() => {
          this.$message.error("获取地区信息失败！");
        });
      } else if (node.level >= 1) {
        this.$ajax({
          method: "get",
          url: "/area/next/rank/findAll",
          data: params,
          serverName: this.serverName
        }).then((response) => {
          if (response.data.code == 200) {
            console.log(node, "2");
            console.log(response);
            this.currentNode = response.data.data
            this.currentNode.forEach(element => {
              if (element.hasChild == false) {
                element.leaf = true
              }
            });
            return resolve(this.currentNode);
          } else {
            this.$message.error("获取地区信息失败！");
          }
        }).catch(() => {
          this.$message.error("获取地区信息失败！");
        });
      }

    },
    handleNodeClick(node) {
      this.valueTitle = node[this.defaultProps.label];
      this.$emit('func', node)
      this.treeExpandIdList = []
      //console.log(this.$refs.select, '333');
      // this.$refs.select.blur(); // 点击后关闭下拉框，因为点击树形控件后select不会自动折叠
      this.$refs.ndSelect.$refs.select.blur()
    },
    // 清除选中
    // clearHandle() {
    //   this.valueTitle = ''
    //   this.valueId = null
    //   this.treeExpandIdList = []
    //   this.clearSelected()
    //   this.$emit('func', null)
    // },
    // // 清空选中样式
    // clearSelected() {
    //   let allNode = document.querySelectorAll('#tree-option .el-tree-node')
    //   allNode.forEach((element) => element.classList.remove('is-current'))
    // },
    //禁止select在IE下，光标闪现
    stateChange() {
      // 处理在ie浏览器出现光标的问题
      const elem = document.getElementById('wbJspTreeSelect');

      if (elem.hasAttribute('unselectable') === false) {
        elem.setAttribute("unselectable", "on");
        elem.setAttribute('onfocus', 'this.blur()');
      }
    },
    //【jsp】获得服务器路径
    getServerPath() {
      if (typeof getContextPath === "function") {
        console.log("获得服务器路径:" + getContextPath());
        return getContextPath();
      } else {
        return "/sz_product_new";
      }
    },
  },
}
</script>
<style scoped>
*::-webkit-scrollbar {
  width: 10px;
  height: 10px !important;
  width: 6px;
  height: 12px !important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}

.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 230px;
  padding: 0;
  overflow: hidden;
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

ul li>>>.el-tree .el-tree-node__content:hover {
  background-color: #e8f7ff;
}

ul li>>>.el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
  background-color: #ffffff;
}

.el-tree-node__label {
  font-weight: normal;
  margin-right: 10px;
  font-size: 12px;
}

.el-tree .is-current .el-tree-node__label {
  color: #409eff;
  font-weight: 700;
}

.el-tree .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}

.el-select-dropdown__item.hover {
  background: transparent;
}

.el-select-dropdown__item span {
  margin-left: 10px;
}

::v-deep .el-scrollbar__bar.is-horizontal {
  height: 0px;
}

::v-deep .el-scrollbar__bar.is-vertical {
  width: 0px;
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #c8ecff;
}
</style>