<template>
  <div class="tab-name-box">
    <span
      v-for="(item, index) in tabNames"
      :key="index"
      :class="{ active: currentAct === index }"
      class="sapnTabNames"
      @click="
        currentAct = index;
        $emit('input', index);
      "
    >
      {{ item.name }}
    </span>
  </div>
</template>

<script>
export default {
  name: 'tabs',
  props: {
    tabNames: { type: Array, default: () => [{ name: 'tab1' }] },
    value: { type: Number, default: 0 },
  },
  data() {
    return { currentAct: this.value };
  },
  watch: {
    value(val) {
      this.currentAct = val;
    },
    currentAct(val) {
      this.$emit('input', val);
    },
  },
};
</script>

<style lang="scss" scoped>
.tab-name-box {
  width: 100%;
  height: 50px;
  font-size: 20px;
  font-weight: 400;
  color: #333333;
  line-height: 50px;
  padding-left: calc((100% - 1200px) / 2);
  background: #f7f7f7;
  span {
    margin-right: 10px;
    cursor: pointer;
    display: inline-block;
    height: 100%;
    width: 130px;
    text-align: center;
    color: #333;
  }
  .active {
    font-weight: bold;
    color: #fff;
    position: relative;
    background: #10AEC2;

    // &::before {
    //   position: absolute;
    //   content: '';
    //   width: 110px;
    //   bottom: -16px;
    //   left: -28px;
    //   height: 2px;
    //   background: #0084ff;
    // }
  }
}

.sapnTabNames :hover{
  background: red;
}
</style>
