<template>
  <div class="panel">
    <div class="panel-cont">
      <div class="panel-top">
        <div class="top-left">
          [ <span class="top-tab" @mouseover="changeTab(1)">新闻动态 ·&nbsp;</span> 
          <span class="top-tab" @mouseover="changeTab(2)">地方要闻 ·&nbsp;</span> 
          <span class="top-tab" @mouseover="changeTab(3)">专家观点</span> ]
        </div>
        <div class="top-right">查看更多>></div>
      </div>
      <div class="panel-bot">
        <div class="bot-pic">
          <div class="bot-time">
            <span class="time-day">12</span>
            <span class="time-year">/04/2024</span>
          </div>
          <div class="bot-text1">增强软实力 提升吸引力-安徽滁州积极融入长三 角一体化发展</div>
          <div class="bot-gap"></div>
          <div class="bot-text2">
            安徽滁州凤阳县小岗村，18户村民40多年前摁下的红手印，拉开了中国农村改革的序幕。敢闯敢干、敢为人先为滁州注入改革的天然基因，在长三角一体化上升为国家战略后...
          </div>
          <div class="bot-btn">
            <span class="btn-text">查看详情</span>
            <i class="el-icon-right" style="color: #fff"></i>
          </div>
        </div>
        <div class="bot-list">
          <div
            @click="newsClick(index)"
            class="list-item"
            v-for="(item, index) in listArr"
            :key="index"
            :class="[activeNew === index ? 'news-active' : '']"
          >
            <div class="left-cont">
              <div class="item-top">
                <div class="item-sort">{{ index + 1 }}</div>
                <div class="item-title">{{ item.title }}</div>
              </div>
              <div class="item-center">{{ item.content }}</div>
              <div class="item-bot">
                <span class="item-bot-text">{{ item.day }}</span>
                <img src="@/assets/detail/rightArrow.png" alt="" srcset="" />
              </div>
            </div>
            <div class="right-cont"></div>
            <div v-if="activeNew === index" class="fill-cont"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      listArr: [
        {
          title: '敢闯敢干、敢为人先为滁州注入改革的天然基因...',
          content:
            '近年来，全椒县教体系统积极创新，采用“做、用、讲、践”四维一体的教育模式，有效地将红色基因深深植入...',
          day: '2024/04/13',
        },
        {
          title: '敢闯敢干、敢为人先为滁州注入改革的天然基因...',
          content:
            '近年来，全椒县教体系统积极创新，采用“做、用、讲、践”四维一体的教育模式，有效地将红色基因深深植入...',
          day: '2024/04/13',
        },
        {
          title: '敢闯敢干、敢为人先为滁州注入改革的天然基因...',
          content:
            '近年来，全椒县教体系统积极创新，采用“做、用、讲、践”四维一体的教育模式，有效地将红色基因深深植入...',
          day: '2024/04/13',
        },
      ],

      activeNew: '', //选中的新闻
    };
  },
  mounted() {
    // this.getData();
  },
  methods: {
    getData() {
      this.$ajax({
        url: '/notice/count',
        method: 'post',
        data: {
          allpath: '',
        },
      }).then((r) => {
        if (r.data.code === 200) {
          this.datas = r.data.data;
          console.log(this.datas);
        }
      });
    },

    newsClick(index) {
      this.activeNew = index;
    },

    changeTab(index) {
      this.activeNew = '';
      console.log('鼠标移入事件');
    },
  },
};
</script>

<style lang="scss" scoped>
.panel {
  width: 100%;
  // padding: 50px 310px 0;
  padding: 50px 0 0 0;
  background: #F7FEFF;
  display: flex;
  justify-content: center;

  .panel-cont {
    // width: 100%;
    width: 1300px;

    .panel-top {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 30px;

      .top-left {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 28px;
        color: #333333;
        span {
          cursor: pointer;
        }

        span:hover {
          font-weight: bold;
        }
      }

      .top-right {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 14px;
        color: #10aec2;
        cursor: pointer;
      }
    }

    .panel-bot {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      height: 498px;

      .bot-pic {
        height: 100%;
        // width: 811px;
        width: 62.38%;
        background-image: url('@/assets/detail/leftimg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        padding: 45px 79px 0px 58px;

        .bot-time {
          font-family: Microsoft YaHei;
          color: #ffffff;
          margin-bottom: 40px;
          height: 44px;

          .time-day {
            font-weight: bold;
            font-size: 56px;
            margin-right: 8px;
          }

          .time-year {
            font-weight: 400;
            font-size: 20px;
          }
        }

        .bot-text1 {
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 32px;
          color: #fffefe;
          line-height: 50px;
          margin-bottom: 50px;
          height: 82px;
        }

        .bot-gap {
          width: 29px;
          height: 6px;
          background: #ffffff;
          margin-bottom: 49px;
        }

        .bot-text2 {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          line-height: 26px;
          margin-bottom: 63px;
          height: 42px;
        }

        .bot-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: row;
          width: 110px;
          height: 32px;
          border-radius: 4px;
          border: 1px solid #ffffff;
          cursor: pointer;

          .btn-text {
            width: 56px;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            margin-right: 10px;
          }
        }
      }

      .bot-list {
        flex: 1;
        // width: 489px;
        height: 100%;
        padding: 20px 0px 38px 20px;
        background: #ffffff;
        border-radius: 10px;
        border: 1px solid #d4f5fa;
        // .list-item:nth-child(-n+2) {
        //   margin-bottom: 18px;
        // }
        position: relative;

        .news-active {
          background: #f3fbfc;
        }
        .list-item {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 18px 0 18px 15px;
          cursor: pointer;
          .left-cont {
            .item-top {
              display: flex;
              flex-direction: row;
              align-items: center;
              margin-bottom: 15px;
              height: 20px;
              .item-sort {
                width: 20px;
                height: 20px;
                background: #10aec2;
                border-radius: 2px;
                opacity: 0.5;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
                margin-right: 10px;
              }

              .item-title {
                font-family: Microsoft YaHei;
                font-weight: bold;
                font-size: 18px;
                color: #333333;
                line-height: 26px;
              }
            }

            .item-center {
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 16px;
              color: #666666;
              line-height: 26px;
              padding-left: 30px;
              margin-bottom: 21px;
              height: 42px;
            }

            .item-bot {
              padding-left: 30px;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: space-between;
              height: 14px;
              cursor: pointer;
              .item-bot-text {
                font-family: Microsoft YaHei;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                line-height: 26px;
              }
            }
          }

          .right-cont {
            width: 14px;
            height: 100%;
          }

          .fill-cont {
            position: absolute;
            right: 0px;
            width: 4px;
            height: 146px;
            background: #10aec2;
          }
        }
      }
    }
  }
}
</style>