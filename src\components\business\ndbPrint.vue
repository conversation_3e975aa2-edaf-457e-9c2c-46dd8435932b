<template>
  <div class="print-dialog-box">
    <nd-dialog ref="printTreeDialog" :title="title" :width="width" :height="height" append-to-body
      :before-close="handleClose" center>
      <!-- 左侧内容 -->
      <!-- 辅助核算明细账树结构 -->
      <div v-if="isShowTree1" class="modal-left">
        <div class="tree">
          <nd-tree ref="treeNode" :key="key" v-loading="loading" :data="treeData" show-checkbox node-key="id"
            :props="defaultProps" :expand-on-click-node="false" :check-strictly="!checkStrict"
            :default-expanded-keys="defaultShowNodes" :check-on-click-node="true" @check="treeClickChoosefzhs"
            @node-dblclick="handleNodeDoubleClick" />
        </div>
        <div class="checkbox">
          <div>
            <nd-checkbox v-model="printParams.endKm" :checked="printParams.endKm == 1 ? true : false" true-label="1"
              false-label="0" @change="onlyDetailChange" style="margin-bottom: 10px" width="170px">
              仅显示最明细科目
            </nd-checkbox>
            <nd-checkbox v-model="checkStrict" width="110px"> 子级联动选择 </nd-checkbox>
          </div>
          <nd-checkbox v-model="printParams.isAll" width="50px" style="margin-right: 17px"
            :checked="printParams.isAll == 1 ? true : false" true-label="1" false-label="0" @change="checkAllChange">
            全选
          </nd-checkbox>
        </div>
      </div>
      <!-- 明细账树结构 -->
      <div v-if="isShowTree2" class="modal-left">
        <div class="tree">
          <nd-tree id="tree-option" ref="selectAssetsTree" :key="key" v-loading="loading1" class="tree-option"
            :data="treeDataLedger" highlight-current :props="defaultPropsLedger" node-key="id" show-checkbox
            :expand-on-click-node="false" :check-strictly="!checkStrict" :default-expanded-keys="defaultShowNodes"
            :check-on-click-node="true" @check="treeClickChooseMx" @node-dblclick="handleNodeDoubleClick"
            :render-content="renderContent" />
        </div>
        <div class="checkbox">
          <div>
            <nd-checkbox v-model="endKm" @change="endKmChange" style="margin-bottom: 10px" width="170px">
              仅显示最明细科目
            </nd-checkbox>
            <nd-checkbox v-model="checkStrict" width="110px"> 子级联动选择 </nd-checkbox>
          </div>
          <nd-checkbox ref="isAll" v-model="isAll" width="50px" @change="isAllChange">
            全选
          </nd-checkbox>
        </div>
      </div>
      <!-- 右侧内容 -->
      <div class="modal-right">
        <!-- 1.显示纸张类型 -->
        <div v-if="isshow1" class="row">
          <div class="label">纸张类型</div>
          <nd-radio-group v-model="printParams.pageType" @change="pageType">
            <nd-radio label="1"> A4 </nd-radio>
            <nd-radio v-if="isShowLabel2" label="2"> 自定义纸张 </nd-radio>
          </nd-radio-group>
        </div>
        <!-- 自定义纸张显示宽高 -->
        <div v-if="printParams.pageType == '2' ? true : false" style="padding-left: 47px" class="row">
          <div class="label" />
          <div class="flex-center mr10">
            宽度
            <nd-input v-model="printParams.pageLength" size="mini" class="mr5 ml5" width="50px"
              @input="changeLengthValue" />
            毫米
          </div>
          <div class="flex-center">
            高度
            <nd-input v-model="printParams.pageWidth" size="mini" class="mr5 ml5" width="50px"
              @input="changeWidthValue" />

            毫米
          </div>
        </div>
        <!-- 2.显示打印方向 -->
        <div v-if="isshow2" class="row">
          <div class="label">打印方向</div>
          <nd-radio-group v-model="printParams.printDirection">
            <nd-radio label="1"> 横向 </nd-radio>
            <nd-radio label="2"> 纵向 </nd-radio>
          </nd-radio-group>
        </div>
        <!-- 3.显示边距调整（上，左） -->
        <div v-if="isshow3" class="row">
          <div class="label">边距调整</div>
          <div class="flex-center mr10">
            左
            <nd-input v-model="printParams.backGaugeLeft" size="mini" class="mr5 ml5" width="50px"
              @input="changeLeftValue" />
            毫米
          </div>
          <div class="flex-center">
            上
            <nd-input v-model="printParams.backGaugeUp" size="mini" class="mr5 ml5" width="50px"
              @input="changeTopValue" />
            毫米
          </div>
        </div>
        <!--4.每页最多打印凭证数  -->
        <div v-if="isshow4" class="row">
          <div class="label">每页最多打印凭证数</div>
          <nd-select v-model="printParams.everyPrintSize" size="mini" width="75px">
            <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value" />
          </nd-select>
        </div>
        <!-- 5.字体大小 -->
        <div v-if="isshow5" class="row">
          <div class="label">字体大小</div>
          <nd-input v-model="printParams.fontSize" size="mini" width="57px" @input="changeSizeValue" />
        </div>
        <!-- 23.每页科目列数 -->
        <div v-if="isshow23" class="row">
          <div class="label">每页科目列数</div>
          <nd-select v-model="printParams.subjectNumberCol" size="mini" width="75px">
            <el-option v-for="item in SubjectNumList" :key="item.value" :label="item.label" :value="item.value" />
          </nd-select>
        </div>
        <!-- 22.报表名称 -->
        <div v-if="isshow22" class="row">
          <div class="label">报表名称</div>
          <nd-input maxlength="10" v-model.trim="printParams.titName"
            @input="printParams.titName = inputCheck(printParams.titName)" size="mini" width="187px" />
        </div>
        <div class="pingz-collect" style="margin-bottom: 10px">
          <!-- 6.凭证分录汇总打印 -->
          <div v-if="isshow6" class="row" style="margin-bottom: -10px">
            <nd-checkbox v-model="printParams.pingzCollect" :checked="printParams.pingzCollect == 1 ? true : false"
              true-label="1" false-label="0">
              凭证分录汇总打印
            </nd-checkbox>
          </div>
          <!-- 6-1.科目汇总到~级 -->
          <div v-if="printParams.pingzCollect == 1" class="row" style="padding-left: 23px; margin-bottom: -10px">
            <nd-checkbox @change="isSelectCollectKmLevel" v-model="printParams.isSelectCollectKmLevel"
              :checked="printParams.isSelectCollectKmLevel == 1 ? true : false" true-label="1" false-label="0"
              width="98px">
              将科目汇总到
            </nd-checkbox>
            <nd-select v-model="printParams.putKmLevel" width="75px">
              <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </nd-select>
            级
          </div>
          <!-- 6-1-1.按借贷方向分别汇总-科目 -->
          <div v-if="printParams.pingzCollect == 1 && printParams.isSelectCollectKmLevel == 1" class="row"
            style="padding-left: 46px; margin-bottom: -10px">
            <nd-checkbox width="134px" v-model="printParams.isJieDaiCollect"
              :checked="printParams.isJieDaiCollect == 1 ? true : false" true-label="1" false-label="0">
              按借贷方向分别汇总
            </nd-checkbox>
            <el-tooltip effect="dark" placement="top-start">
              <div slot="content">
                当勾选该选项时表示同一凭证中的分录按相应层级汇总后的相同科目进行<br />同方向金额汇总,即借方汇总借方,贷方汇总贷方,借贷不相互冲抵;当不勾选<br />时表示按相应层级汇总后的相同科目贷借方金额互抵;如果是"将辅助核算<br />汇总至上X级"下不勾选"借贷方向分别汇总"时表示含有辅助核算的科目按<br />相应层级汇总后的相同科目贷借方金额互抵;
              </div>
              <img class="img" src="@/img/explain.png" alt="" />
            </el-tooltip>
          </div>
          <!-- 6-2.将辅助核算汇总至上~级 -->
          <div v-if="printParams.pingzCollect == 1" class="row" style="padding-left: 23px; margin-bottom: -10px">
            <nd-checkbox @change="isSelectCollectFzhs" v-model="printParams.isSelectCollectFzhs"
              :checked="printParams.isSelectCollectFzhs == 1 ? true : false" true-label="1" false-label="0" width="134px">
              将辅助核算汇总至上
            </nd-checkbox>
            <nd-select v-model="printParams.putFzhsKmLevel" width="75px">
              <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </nd-select>
            级
          </div>
          <!-- 6-2-1.按借贷方向分别汇总-辅助核算 -->
          <div v-if="printParams.pingzCollect == 1 && printParams.isSelectCollectFzhs == 1" class="row"
            style="padding-left: 46px">
            <nd-checkbox width="134px" v-model="printParams.isJieDaiCollectFzhs"
              :checked="printParams.isJieDaiCollectFzhs == 1 ? true : false" true-label="1" false-label="0">
              按借贷方向分别汇总
            </nd-checkbox>
            <el-tooltip effect="dark" placement="top-start">
              <div slot="content">
                当勾选该选项时表示同一凭证中的分录按相应层级汇总后的相同科目进行<br />同方向金额汇总,即借方汇总借方,贷方汇总贷方,借贷不相互冲抵;当不勾选<br />时表示按相应层级汇总后的相同科目贷借方金额互抵;如果是"将辅助核算<br />汇总至上X级"下不勾选"借贷方向分别汇总"时表示含有辅助核算的科目按<br />相应层级汇总后的相同科目贷借方金额互抵;
              </div>
              <img class="img" src="@/img/explain.png" alt="" />
            </el-tooltip>
          </div>
        </div>
        <!-- 7.不显示制单人姓名 -->
        <div v-if="isshow7" class="row">
          <nd-checkbox v-model="printParams.pzPersonShow" :checked="printParams.pzPersonShow == 1 ? true : false"
            true-label="1" false-label="0">
            不显示制单人姓名（便于线下签字）
          </nd-checkbox>
        </div>
        <!-- 8.不显示附件张数 -->
        <div v-if="isshow8" class="row">
          <nd-checkbox v-model="printParams.doNotShowFileLength"
            :checked="printParams.doNotShowFileLength == 1 ? true : false" true-label="1" false-label="0">
            不显示附件张数（便于线下核对填写）
          </nd-checkbox>
        </div>
        <!-- 10.显示编制日期 -->
        <div v-if="isshow10" class="row">
          <nd-checkbox v-model="printParams.isShowPrintDate" :checked="printParams.isShowPrintDate == 1 ? true : false"
            true-label="1" false-label="0" width="100px">
            显示编制日期
          </nd-checkbox>
          <nd-date-picker v-model="printParams.printDate" type="date" value-format="yyyy-MM-dd" />
        </div>
        <!-- 11.显示打印人 -->
        <div v-if="isshow11" class="row">
          <nd-checkbox v-model="printParams.isShowPrinter" :checked="printParams.isShowPrinter == 1 ? true : false"
            true-label="1" false-label="0">
            显示打印人
          </nd-checkbox>
        </div>
        <!-- 19.显示辅助核算分组 -->
        <!-- <div v-if="isshow19" class="row">
          <nd-checkbox
            v-model="printParams.isShowFzhsGroup"
            :checked="printParams.isShowFzhsGroup == 1 ? true : false"
            true-label="1"
            false-label="0"
          >
            显示辅助核算分组
          </nd-checkbox>
        </div> -->
        <!-- 9.显示页码 -->
        <div v-if="isshow9" class="row">
          <nd-checkbox v-model="printParams.isShowPage" :checked="printParams.isShowPage == 1 ? true : false"
            true-label="1" false-label="0" @change="showPage">
            显示页码
          </nd-checkbox>
        </div>
        <!-- 14.显示页码(按归档目录中生成的页码显示) -->
        <div v-if="isshow14" class="row">
          <nd-checkbox v-model="printParams.isShowPageMx" :checked="printParams.isShowPageMx == 1 ? true : false"
            true-label="1" false-label="0" @change="showPageMx">
            显示页码(按归档目录中生成的页码显示)
          </nd-checkbox>
        </div>
        <!-- 15.打印单位负责人、财务主管、制表人 -->
        <div v-if="isshow15" class="row">
          <nd-checkbox v-model="printParams.doNotShowBuilder"
            :checked="printParams.doNotShowBuilder == '1' ? true : false" true-label="1" false-label="0"
            @change="doNotShowBuilder">
            打印单位负责人、财务主管、制表人
          </nd-checkbox>
        </div>
        <!-- 16.显示单位负责人 -->
        <div v-if="isshow16" class="row">
          <nd-checkbox v-model="printParams.isShowDeptPerson"
            :checked="printParams.isShowDeptPerson == '1' ? true : false" true-label="1" false-label="0" width="110px">
            显示单位负责人
          </nd-checkbox>
          <nd-input v-model.trim="printParams.deptPerson"
            @input="printParams.deptPerson = inputCheck(printParams.deptPerson)" size="mini" width="187px">
          </nd-input>
        </div>
        <!-- 17.显示财务主管 -->
        <div v-if="isshow17" class="row">
          <nd-checkbox v-model="printParams.isShowAccountant"
            :checked="printParams.isShowAccountant == '1' ? true : false" true-label="1" false-label="0" width="110px">
            显示财务主管
          </nd-checkbox>
          <nd-input v-model.trim="printParams.accountant"
            @input="printParams.accountant = inputCheck(printParams.accountant)" size="mini" width="187px">
          </nd-input>
        </div>
        <!-- 18.显示制表人 -->
        <div v-if="isshow18" class="row">
          <nd-checkbox v-model="printParams.isShowLister" :checked="printParams.isShowLister == '1' ? true : false"
            true-label="1" false-label="0" width="110px">
            显示制表人
          </nd-checkbox>
          <nd-input v-model.trim="printParams.lister" @input="printParams.lister = inputCheck(printParams.lister)"
            size="mini" width="187px">
          </nd-input>
        </div>
        <!-- 20.显示村监委（监事会）、镇（街道）农村集体资产监管部门：盖章” -->
        <div v-if="isshow20" class="row">
          <nd-checkbox v-model="printParams.isShowSeal" :checked="printParams.isShowSeal == 1 ? true : false"
            true-label="1" false-label="0">
            显示村监委(监事会)、镇(街道)农村集体资产监管部门:盖章
          </nd-checkbox>
        </div>
        <!-- 21.显示财务公开 “扫一扫”二维码 -->
        <div v-if="isshow21" class="row">
          <nd-checkbox v-model="printParams.isShowQrCode" :checked="printParams.isShowQrCode == 1 ? true : false"
            true-label="1" false-label="0">
            显示财务公开“扫一扫”二维码
          </nd-checkbox>
        </div>
        <!--12 打印封面 -->
        <div v-if="isShowButton" class="row row-btn" style="margin-top: 10px; font-size: 12px">
          <nd-dropdown v-if="isshow12" :name-data="nameData" @click="printCover()">
            <el-dropdown-item command="a" @click.native="typeOne"> 横版封面 </el-dropdown-item>
            <el-dropdown-item command="b" @click.native="typeZero"> 竖版封面 </el-dropdown-item>
          </nd-dropdown>
          <!-- 13  打印目录-->
          <nd-button v-if="isshow13" style="width: 91px; height: 30px;display: flex;
            align-items: center;justify-content:center" @click="printDirectory">
            打印目录
          </nd-button>
        </div>
      </div>
      <template #footer>
        <div>
          <nd-button type="primary" @click="print('1')"> 保存并打印 </nd-button>
          <nd-button @click="print('0')"> 打印 </nd-button>
          <nd-button @click="close"> 取消 </nd-button>
        </div>
      </template>
    </nd-dialog>
  </div>
</template>

<script>
import ndDialog from '../ndDialog.vue';
import ndRadio from '../ndRadio.vue';
import ndRadioGroup from '../ndRadioGroup.vue';
import ndButton from '../ndButton.vue';
import ndCheckbox from '../ndCheckbox.vue';
import ndDropdown from '../ndDropdown.vue';
import ndInput from '../ndInput.vue';
import ndSelect from '../ndSelect.vue';
import ndDatePicker from '../ndDatePicker.vue';
import ndTree from '../ndTree.vue';

export default {
  components: {
    ndDialog,
    ndRadio,
    ndRadioGroup,
    ndButton,
    ndCheckbox,
    ndDropdown,
    ndInput,
    ndSelect,
    ndDatePicker,
    ndTree,
  },
  props: {
    printurl: {
      type: String,
      default: '',
    },
    modelType: {
      type: String,
      default: '',
    },
    form: {
      type: Object,
      default: null,
    },
    id: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      nameData: '竖版封面',
      defaultShowNodes: [],
      // defaultClickNodes: [],
      key: 0,
      Ids: [],
      width: '670px',
      height: '450px',
      timer1: null,
      timer2: null,
      loading: false,
      kmName: '',
      kmCode: '',
      isAll: false, //是否全选
      loading1: false, //加载动画
      kmJb: '',
      endKm: '', //是否仅显示末级
      isShowTree1: false, //辅助核算明细账树结构
      isShowTree2: false, //<!-- 明细账树结构 -->
      isshow1: true, //1.显示纸张类型
      isshow2: true, //2.显示打印方向
      isshow3: true, //3.显示边距调整（上，左）
      isshow4: true, //4.每页最多打印凭证数
      isshow5: true, //5.字体大小
      isshow6: true, //6.凭证分录汇总打印
      isshow7: true, //7.不显示制单人姓名
      isshow8: true, //8.不显示附件张数
      isshow9: true, //9.显示页码
      isshow10: true, //10.显示编制日期
      isshow11: true, //11.显示打印人
      isShowButton: true,
      isshow12: true, //12 打印封面
      isshow13: true, //13  打印目录
      isshow14: false, //14.显示页码(按归档目录中生成的页码显示)先判断有无目录
      isshow15: false, //报表管理--打印单位负责人、会计主管、制表人
      isshow16: false, //报表管理--打印单位负责人
      isshow17: false, //报表管理--打印财务主管
      isshow18: false, //报表管理--打印制表人
      // isshow19: false, //显示辅助核算分组
      isshow20: false, //显示村监委（监事会）、镇（街道）农村集体资产监管部门：盖章
      isshow21: false, //显示财务公开 “扫一扫”二维码
      isshow22: false, //显示报表名称
      isshow23: false, //每页科目列数
      isShowLabel2: true,
      treeData: [], //左侧树结构
      treeData1: [],
      treeData2: [],
      treeDataLedger: [], //左侧树结构(明细账树)
      defaultProps: {
        children: 'sub',
        label: 'name',
      },
      defaultPropsLedger: {
        label: 'fullKmName',
        children: 'children',
      },
      nodeKey: [],
      allCheck: false,
      onlyDetail: false,
      title: '打印设置',
      option: [
        {
          value: '1',
          label: '1',
        },
        {
          value: '2',
          label: '2',
        },
      ], //每页最多打印凭证数选项
      options1: [
        {
          value: '1',
          label: '1',
        },
        {
          value: '2',
          label: '2',
        },
        {
          value: '3',
          label: '3',
        },
        {
          value: '4',
          label: '4',
        },
        {
          value: '5',
          label: '5',
        },
        {
          value: '6',
          label: '6',
        },
        {
          value: '7',
          label: '7',
        },
        {
          value: '8',
          label: '8',
        },
        {
          value: '9',
          label: '9',
        },
        {
          value: '10',
          label: '10',
        },
      ], //科目汇总到~级
      SubjectNumList: [
        {
          value: '1',
          label: '1',
        },
        {
          value: '2',
          label: '2',
        },
      ],
      options2: [
        {
          value: '1',
          label: '1',
        },
        {
          value: '2',
          label: '2',
        },
        {
          value: '3',
          label: '3',
        },
        {
          value: '4',
          label: '4',
        },
        {
          value: '5',
          label: '5',
        },
        {
          value: '6',
          label: '6',
        },
        {
          value: '7',
          label: '7',
        },
        {
          value: '8',
          label: '8',
        },
        {
          value: '9',
          label: '9',
        },
        {
          value: '10',
          label: '10',
        },
      ], //辅助核算汇总到~级
      options1Str: '', //科目汇总到~级 根据数字确定数组
      options2Str: '', //辅助核算汇总到~级 根据数字确定数组
      // 打印的时候要传的参数
      printParams: {
        pageType: '1', //1.纸张类型 1A4 2自定义 默认A4
        pageWidth: '124', //纸张类型自定义时宽度单位mm
        pageLength: '210', //纸张类型自定义时高度单位mm
        printDirection: '2', //2.打印方向 1横2纵 默认纵
        backGaugeLeft: '15', //3.左边距
        backGaugeUp: '10', //3.上边距
        everyPrintSize: '2', //4.每页最多打印凭证数
        subjectNumberCol: "1",//每页科目列数
        fontSize: '15', //5.字体大小
        titName: '', //22.报表名称
        pingzCollect: '0', //6.凭证分录汇总打印 0否1是
        isSelectCollectKmLevel: '1', //6-1.科目汇总到~级 0否1是
        isSelectCollectFzhs: '0', // 6-2.将辅助核算汇总至上~级 0否1是
        isJieDaiCollect: '0', // 6-1-1.按借贷方向分别汇总-科目  0否1是
        isJieDaiCollectFzhs: '0', // 6-2-1.按借贷方向分别汇总-辅助核算 0否1是
        putKmLevel: '1', // 科目汇总级数
        putFzhsKmLevel: '1', // 将辅助核算汇总至上级数
        pzPersonShow: '0', //7.是否显示制单人姓名
        doNotShowFileLength: '0', //8.不显示附件张数
        isShowPage: '1', //9.显示页码1
        isShowPageMx: '0', //14.显示页码(按归档目录中生成的页码显示)
        isShowPrintDate: '1', //10.是否显示打印日期
        printDate: '', //打印日期（yyyy-MM-dd 默认系统当前日期 日期插件）
        isShowPrinter: '1', //11.显示打印人（0否1是）
        doNotShowBuilder: '1', //15.打印单位负责人、会计主管、制表人（0否1是）
        deptPerson: '', //16.单位负责人内容
        accountant: '', //17.财务主管内容
        lister: '', //18.制表人内容
        isShowDeptPerson: '0', //16.是否显示单位负责人
        isShowAccountant: '0', //17.是否显示财务主管
        isShowLister: '0', //18.是否显示制表人
        // isShowFzhsGroup: '0', //19.显示辅助核算分组 默认不选中
        isShowSeal: '0', //20.显示村监委（监事会）、镇（街道）农村集体资产监管部门：盖章”
        isShowQrCode: '0', //21.显示财务公开 “扫一扫”二维码
        start_mon: '', //会计期间起
        end_mon: '', //会计期间止
        isAssist: '', //是否显示辅助核算
        showYe: '', //显示明细栏余额
        kmStatus: '', //专栏科目
        fzhsType: '', //辅助核算类型
        fzhsName: '', //辅助核算名称
        noZero: '', //余额为0不显示
        kmIds: '',
        subjectCode: '',
        noFse: '', //无发生额且余额为0不显示
        isNoLj: '', // 无发生额不显示本期合计、本年累计
        pz_state: '', //包含未记账凭证
        kmId: '', //左侧传过来的kmid
        printType: '0', //0是打印1是打印并保存
        fzhsIds: [], //左侧可能会传的树状节点
        isAll: '0', //0否1是（当是1时，fzhsids不需传值）
        endKm: '0', //仅显示末级科目0否1是
        kmJb: '', //总账打印的科目级别
        ztId: '',
        fzId: '',
        id: '',
        //报表管理模块参数
        reportType: '', //报表类型(1资产负债2收支明细3收益分配)
        startMonth: '', //开始月份
        endMonth: '', //结束月份
        pzStatus: '', //开始月份
        reportId: '', //报表ID
        unitId: '', //地区ID
        unitType: '', //地区类型
        villageType: '', //村居类型
        yearAndMonth: '', //年月
        month: '', //月
        orgType: '', //组织类型
        moneyUnit: '', //金额单位
        moneyDecimalNum: '', //保留位数
        exclusionsUnitAllpath: '', //排除项
        startYearAndMonth: '', //开始年月
        endYearAndMonth: '', //结束年月
        villageDelete: '', //村社往来类型
        //凭证录入模块的参数
        jsonArr: '',
        // 科目余额表
        //  startMonth: '',//会计期间起
        //endMonth: '',// 会计期间止
        kmLevel: '', // 科目级次
        pzStatus: '', // 未记账凭证
        isShowFzhs: '', // 显示辅助核算
        showFx: '', // 是否借贷两列显示
        allPath: '', //科目
        urlflag: '', //1 科目余额表 凭证汇总
        startDate: '', // 开始时间
        endDate: '',
        pzhEnd: '',
        pzhStart: '',
        moneyEnd: '',
        moneyStart: ''
      },
      kmStatusList: {
        dataId: "",
        dataType: "",
        dataAllPath: "",
        statisticalContent: "",
        startMonth: "",
        endMonth: "",
        moneyUnitSet: "",
        moneySet: "",
        ruleOutARegionAllPath: "",
        subjectCode: "",
        isContainKeepAccounts: "",
        kmDeptType: "",
      },
      // 用来获取勾选的打印条件
      arr: [],
      checkStrict: false, //是否关联子集
    };
  },

  watch: {
    treeData() {
      // console.log(this.tre,'-----------------------');
    },
  },

  mounted() { },
  //  首先判断模块类型 1、凭证查询打印 2、总账 3、明细帐 4、科目余额表 5、资产负债表 6、收支明细表 7、收益分配表
  // 8、辅助核算余额表 9、辅助核算明细表 11、综合查询-资产负债表纵向表 12、综合查询-收支明细表纵向表 13、综合查询-收益及收益分配表纵向表
  // 15.凭证汇总表 16.多栏明细账 17.资产负债表（旧） 19.收益分配表（旧）

  methods: {
    inputCheck(input) {
      console.log(input, 'inputinputinputinput');
      input = input + "";
      input = input.replace(/\s*/g, ""); // 去除所有的空格
      // input = input.replace(/[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g, ""); // 只能输入汉字英文数字
      return input;
    },
    // 自定义树显示内容
    renderContent(h, { node, data, store }) {
      // console.log(node.isFzhs,data.isFzhs,"48546");
      if (Number(data.isFzhs)) {
        return (
          <div style="display:flex;align-items:center;">
            <div style="margin-right:10px">{node.label}</div>
            <el-tooltip content="辅助核算类科目" placement="right">
              <img src={require('@/assets/fu_icon.png')} style="width:14px;height:14px" />
            </el-tooltip>
          </div>
        );
      } else {
        return (
          <span>
            <span> {node.label}</span>
          </span>
        );
      }
    },
    // 双击展开当前项
    handleNodeDoubleClick(object) {
      console.log(object, '++++++++++++');
      var id = object.id;
      if (this.defaultShowNodes.includes(id)) {
        var a = [];
        this.defaultShowNodes.map((item) => {
          if (item !== id) {
            a.push(item);
          }
        });
        this.defaultShowNodes = a;
        this.key = this.key + 1;
      } else {
        this.defaultShowNodes.push(id);
      }
    },
    // tree转table
    treeToTable(datas) {
      datas.map((item, index) => {
        this.treeData2.push(item);
        if (item.children) {
          this.treeToTable(item.children);
        }
        // console.log(this.treeData2,'11111111111111111111111');
      });
    },
    treeToTable1(datas) {
      console.log(datas, '222222');
      datas.map((item, index) => {
        this.treeData1.push(item);
        if (item.sub) {
          this.treeToTable1(item.sub);
        }
        // console.log(this.treeData1,'11111111111111111111111');
      });
    },
    showPage(val) {
      if (val) {
        this.printParams.isShowPageMx = '0';
      } else {
        this.printParams.isShowPageMx = '1';
      }
    },
    showPageMx(val) {
      if (val) {
        this.printParams.isShowPage = '0';
      } else {
        this.printParams.isShowPage = '1';
      }
    },
    doNotShowBuilder(val) {
      if (val == '0') {
        this.printParams.doNotShowBuilder = '0';
      } else {
        this.printParams.doNotShowBuilder = '1';
      }
    },
    isSelectCollectKmLevel(val) {
      if (val) {
        this.printParams.isSelectCollectFzhs = '0';
      } else {
        this.printParams.isSelectCollectFzhs = '1';
      }
    },
    isSelectCollectFzhs(val) {
      if (val) {
        this.printParams.isSelectCollectKmLevel = '0';
      } else {
        this.printParams.isSelectCollectKmLevel = '1';
      }
    },
    typeOne() {
      if (this.modelType == '2') {
        this.printOpen(this.getServerPath() + '/tatolExcel/fmPageShow.do?flag=1&type=1');
      } else if (this.modelType == '3') {
        this.printOpen(this.getServerPath() + '/tatolExcel/fmPageShow.do?flag=0&type=1');
      }
    },
    typeZero() {
      if (this.modelType == '2') {
        this.printOpen(this.getServerPath() + '/tatolExcel/fmPageShow.do?flag=1&type=0');
      } else if (this.modelType == '3') {
        this.printOpen(this.getServerPath() + '/tatolExcel/fmPageShow.do?flag=0&type=0');
      }
    },
    // 左边距只能输入正整数
    changeLeftValue(value) {
      this.printParams.backGaugeLeft = /^[0-9]*$/.test(parseInt(value))
        ? String(parseInt(value)).replace('.', '')
        : '';
      if (this.printParams.backGaugeLeft == '0') {
        this.printParams.backGaugeLeft = '';
      }
      // console.log(typeof this.printParams.backGaugeLeft);
    },
    // 上边距只能输入正整数
    changeTopValue(value) {
      this.printParams.backGaugeUp = /^[0-9]*$/.test(parseInt(value))
        ? String(parseInt(value)).replace('.', '')
        : '';
      if (this.printParams.backGaugeUp == '0') {
        this.printParams.backGaugeUp = '';
      }
    },
    //宽度50-1500mm
    changeWidthValue(value) {
      this.printParams.pageWidth = /^[0-9]*$/.test(parseInt(value))
        ? String(parseInt(value)).replace('.', '')
        : '';
      if (this.timer1 !== null) clearTimeout(this.timer1);
      this.timer1 = setTimeout(() => {
        if (this.printParams.pageWidth < 50 || this.printParams.pageWidth > 1500) {
          this.printParams.pageWidth = '';
          this.$message({
            type: 'warning',
            message: '打印高度度请输入50~1500mm!',
          });
        }
      }, 1000);
    },
    //高度50-1500mm
    changeLengthValue(value) {
      this.printParams.pageLength = /^[0-9]*$/.test(parseInt(value))
        ? String(parseInt(value)).replace('.', '')
        : '';
      if (this.timer2 !== null) clearTimeout(this.timer2);
      this.timer2 = setTimeout(() => {
        if (this.printParams.pageLength < 50 || this.printParams.pageLength > 1500) {
          this.printParams.pageLength = '';
          this.$message({
            type: 'warning',
            message: '打印宽度请输入50~1500mm!',
          });
        }
      }, 1000);
    },
    //字体大小为正整数
    changeSizeValue(value) {
      this.printParams.fontSize = /^[0-9]*$/.test(parseInt(value))
        ? String(parseInt(value)).replace('.', '')
        : '';
      if (this.printParams.fontSize == '0') {
        this.printParams.fontSize = '';
      }
    },
    //【jsp】获得服务器路径
    getServerPath() {
      if (typeof getContextPath === 'function') {
        console.log('获得服务器路径:' + getContextPath());
        return getContextPath();
      } else {
        return '/sz_product_new';
      }
    },
    // 打印封面默认竖版
    printCover() {
      if (this.modelType == '2') {
        this.printOpen(
          this.getServerPath() +
          '/tatolExcel/fmPageShow.do?flag=1&type=' +
          this.printParams.printDirection,
        );
      } else if (this.modelType == '3') {
        this.printOpen(
          this.getServerPath() +
          '/tatolExcel/fmPageShow.do?flag=0&type=' +
          this.printParams.printDirection,
        );
      }
    },
    // 纸张类型改变事件1.A4和2.自定义
    pageType(val) {
      console.log(val);
      if (val == '2') {
        this.printParams.printDirection = '1';
        this.isshow4 = false;
        this.printParams.everyPrintSize = '1';
      } else {
        this.printParams.printDirection = '2';
        this.isshow4 = true;
        this.printParams.everyPrintSize = '2';
      }
    },
    // 打印目录
    printDirectory() {
      let params = {
        type: '1',
      }
      if (this.modelType == '2') {
        // 总账
        params.type = "0"
      } else if (this.modelType == '3') {
        // 明细账
        params.type = "1"
      }
      this.$ajax({
        method: 'post',
        url: '' + '/tatolExcel/catalogue.do',
        data: params,
      }).then((res) => {
        if (res.data.code == 1) {
          this.$message.warning(res.data.msg);
        } else if (res.data.code == 2) {
          this.$message.warning(res.data.msg);
        } else if (res.data.code == 3) {
          this.$message.warning(res.data.msg);
        } else if (res.data.code == 5) {
          // 5调用生成目录接口（tatolExcel/creatPage.do 无参）
          this.$confirm('系统将按默认的页面边距、字号生成科目页码目录，确定生成目录?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              // 调用重发申请接口
              this.printFive();
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '取消生成目录!',
              });
            });
        } else if (res.data.code == 9) {
          // 9调用打印目录接口（tatolExcel/pageShow.do 无参get）
          this.printOpen(this.getServerPath() + '/tatolExcel/pageShow.do?type=' + params.type);
        }
      });
    },
    // 生成目录
    printFive() {
      let params = {
        type: '1',
      }
      if (this.modelType == '2') {
        // 总账
        params.type = "0"
      } else if (this.modelType == '3') {
        // 明细账
        params.type = "1"
      }
      this.$ajax({
        method: 'post',
        url: '' + '/tatolExcel/creatPage.do',
        data: params,
      }).then((res) => {
        if (res.data.code == 0) {
          this.printOpen(this.getServerPath() + '/tatolExcel/pageShow.do?type=' + params.type);
        } else {
          this.$message.warning('目录生成失败');
        }
      });
    },
    //获取树数据(明细账树)
    getTreeDataLedger() {
      this.loading1 = true;
      let params = {
        kmName: this.kmName,
        kmCode: this.kmCode,
        isAll: Number(this.isAll),
        kmJb: this.kmJb,
        endKm: String(Number(this.endKm)),
        ztId: this.form.ztId,
      };
      this.$ajax({
        method: 'post',
        url: '' + '/tatolExcel/kmTree.do',
        data: params,
      }).then((res) => {
        // console.log(res.data.data.tree, "树");
        if (res.data.code == 0) {
          // console.log(res);
          this.treeDataLedger = [];
          // this.assetsValue = "";
          if (res.data.data.tree != null) {
            for (let i = 0; i < res.data.data.tree.length; i++) {
              this.treeDataLedger.push(res.data.data.tree[i]);
            }
          }
          this.loading1 = false;
        } else {
          this.$message.warning(res.data.msg);
          //影藏加载中....动画
          this.loading1 = false;
        }
        this.treeData2 = [];
        this.treeToTable(this.treeDataLedger);
        this.$nextTick(() => {
          this.treeData2.map((item) => {
            if (!item.isleaf) {
              this.defaultShowNodes.push(item.id);
            }
          });
          this.$nextTick(() => {
            document.getElementsByClassName('is-checked')[6].scrollIntoView();
          });
        });
      });
    },
    //明细账全选
    isAllChange(val) {
      this.Ids = this.$refs.selectAssetsTree.$children[0].getCheckedKeys();
      if (val) {
        var keys = [];
        this.treeData2.map((item) => {
          keys.push(item.id);
        });
        this.$refs.selectAssetsTree.$refs.ndTree.setCheckedKeys(keys, false);
      } else {
        this.$refs.selectAssetsTree.$refs.ndTree.setCheckedNodes([]);
      }
      this.Ids = this.$refs.selectAssetsTree.$refs.ndTree.getCheckedKeys();
    },
    // 明细账仅选择最明细科目
    endKmChange() {
      this.isAll = false;
      this.getTreeDataLedger();
    },
    //获取树数据(辅助核算明细账)
    getTreeData(endkmId, kmId) {
      this.loading = true;
      this.$ajax({
        method: 'post',
        url: '' + '/fzhsExcel/fzhsMxTreePrint.do',
        data: {
          endKm: endkmId,
          kmId: kmId,
        },
      }).then((res) => {
        if (res.data.code == 0) {
          console.log(res.data.data);
          this.treeData = res.data.data.list;
          this.loading = false;
          this.nodeKey = this.treeData.map((item) => {
            return item.id;
          });
          // console.log(this.nodeKey);
          if (this.isShowTree1) {
            (this.arr = this.form.fzhsIds),
              // this.form.fzhsId左侧传过来的树id实现关联
              this.$refs.treeNode.$children[0].setCheckedKeys([this.form.fzhsIds]);
          }
        }
        // console.log(res);
        this.treeData1 = [];
        this.treeToTable1(this.treeData);
        // this.$nextTick(() => {
        //   this.treeData2.map((item) => {
        //     if (!item.isleaf) {
        //       this.defaultShowNodes.push(item.id);
        //     }
        //   })
        // });
      });
      // this.loading=false
    },
    // 辅助核算明细树全选按钮
    checkAllChange(val) {
      console.log(val, 'val');
      if (val == '1') {
        var keys = [];
        this.treeData1.map((item) => {
          keys.push(item.id);
        });
        this.$refs.treeNode.$refs.ndTree.setCheckedKeys(keys, false);
      } else {
        this.$refs.treeNode.$refs.ndTree.setCheckedNodes([]);
      }
    },
    // 辅助核算明细树仅选择最明细科目
    onlyDetailChange(val) {
      console.log(val, 'val1');
      if (val == '1') {
        this.getTreeData('1', this.form.id);
        this.printParams.isAll = '0';
      } else {
        this.getTreeData('0', this.form.id);
      }
    },
    // 获取当前时间
    getCurrentTime() {
      // end_mon有值时获取当前月份，并根据月份获取最后一天是哪一号
      if (this.printParams.end_mon) {
        let yy = new Date().getFullYear();
        let mm = this.printParams.end_mon;
        let dd = '';
        if (
          mm == '1' ||
          mm == '3' ||
          mm == '5' ||
          mm == '7' ||
          mm == '8' ||
          mm == '10' ||
          mm == '12'
        ) {
          dd = '31';
        } else if (mm == '2') {
          dd = '28';
        } else {
          dd = '30';
        }
        mm = mm > 9 ? mm : '0' + mm;
        this.printParams.printDate = yy + '-' + mm + '-' + dd + '';
      }
      //  endMonth有值时获取当前月份，并根据月份获取最后一天是哪一号
      if (this.printParams.endMonth) {
        let yy = new Date().getFullYear();
        let mm = this.printParams.endMonth;
        let dd = '';
        if (
          mm == '1' ||
          mm == '3' ||
          mm == '5' ||
          mm == '7' ||
          mm == '8' ||
          mm == '10' ||
          mm == '12'
        ) {
          dd = '31';
        } else if (mm == '2') {
          dd = '28';
        } else {
          dd = '30';
        }
        mm = mm > 9 ? mm : '0' + mm;
        this.printParams.printDate = yy + '-' + mm + '-' + dd + '';
      }
    },
    // 打开弹窗
    open(jsonArr, ids) {
      // this.defaultClickNodes = ids;
      console.log(jsonArr, '传过来的jsonArr');
      this.printParams.jsonArr = jsonArr;
      this.$refs.printTreeDialog.open();
      console.log(this.modelType, '传过来的modeltype');
      this.printParams.id = this.id;
      if (this.modelType === '1') {
        // 如果jsonArr有值的时候凭证录入页面
        if (jsonArr) {
          this.$ajax({
            method: 'post',
            url: '/pingzgl.do?method=pzPrintNewKmLevel',
            data: {
              jsonArr: this.printParams.jsonArr,
            },
          }).then((response) => {
            if (response.data.code == 0) {
              this.options1Str = response.data.data.kmMaxLevel;
              this.options2Str = response.data.data.kmFzhsMaxLevel;
              this.getOptions1(this.options1Str);
              this.getOptions2(this.options2Str);
            } else {
              this.$message({
                showClose: true,
                message: response.data.msg,
                type: 'warning',
              });
            }
          });
        } else {
          // // 凭证修改凭证查询页面
          this.$ajax({
            method: 'post',
            url: '/pingzgl.do?method=pzPrintNewKmLevel',
            data: {
              id: this.printParams.id,
            },
          }).then((response) => {
            if (response.data.code == 0) {
              this.options1Str = response.data.data.kmMaxLevel;
              this.options2Str = response.data.data.kmFzhsMaxLevel;
              this.getOptions1(this.options1Str);
              this.getOptions2(this.options2Str);
            } else {
              this.$message({
                showClose: true,
                message: response.data.msg,
                type: 'warning',
              });
            }
          });
        }
      }
      if (this.modelType === '1') {
        this.width = '470px';
        this.height = '380px';
        this.isshow10 = false;
        this.isshow11 = false;
        // this.isshow19 = true;
        this.isShowButton = false;
      } else if (this.modelType == '2') {
        // 总账打印设置
        this.width = '470px';
        this.height = '360px';
        // this.isShowTree2 = false;
        this.isshow1 = false;
        this.isshow4 = false;
        this.isshow6 = false;
        this.isshow7 = false;
        this.isshow8 = false;
        this.isshow11 = false; //不显示(显示打印人)
        this.isshow13 = true;
        this.isshow16 = true; //显示(显示打印单位负责人)
        this.isshow17 = true; //显示(显示财务主管)
        this.isshow18 = true; //显示(显示制表人)
        this.cataLogue();
        // this.getTreeDataLedger();
        // this.isshow19 = true;
      } else if (this.modelType == '3') {
        //明细账打印设置
        this.isShowTree2 = true;
        this.isshow1 = false;
        this.isshow4 = false;
        this.isshow6 = false;
        this.isshow7 = false;
        this.isshow8 = false;
        this.isshow11 = false; //不显示(显示打印人)
        this.isshow16 = true; //显示(显示打印单位负责人)
        this.isshow17 = true; //显示(显示财务主管)
        this.isshow18 = true; //显示(显示制表人)
        // this.isshow19 = true;
        this.cataLogue();
        this.getTreeDataLedger();
        this.Ids.push(this.form.fzhsIds);
        console.log(this.Ids, '~~~~~~~~~~~~~');
        this.arr = this.Ids;
        this.$nextTick(() => {
          this.$refs.selectAssetsTree.$children[0].setCheckedKeys([this.form.fzhsIds]);
        });
      } else if (this.modelType == '4' || this.modelType == '8' || this.modelType == '15') {
        this.width = '470px';
        this.height = '360px';
        this.isshow1 = false;
        this.isshow4 = false;
        this.isshow6 = false;
        this.isshow7 = false;
        this.isshow8 = false;
        this.isshow11 = false; //不显示(显示打印人)
        this.isshow16 = true; //显示(显示打印单位负责人)
        this.isshow17 = true; //显示(显示财务主管)
        this.isshow18 = true; //显示(显示制表人)
        // this.isshow19 = true;
        this.isShowButton = false;
      } else if (this.modelType == '9') {
        // 辅助核算明细表
        this.isShowTree1 = true;
        this.isshow1 = false;
        this.isshow4 = false;
        this.isshow6 = false;
        this.isshow7 = false;
        this.isshow8 = false;
        this.isshow12 = false;
        this.isshow13 = false;
        this.isshow11 = false; //不显示(显示打印人)
        this.isshow16 = true; //显示(显示打印单位负责人)
        this.isshow17 = true; //显示(显示财务主管)
        this.isshow18 = true; //显示(显示制表人)
        // this.isshow19 = true;
        this.getTreeData('0', this.form.id); //获取树结构
      } else if (this.modelType == '5' || this.modelType == '6' || this.modelType == '17') {
        // 资产负债表和收支明细表
        console.log('======报表管理========');
        this.width = '470px';
        this.height = '420px';
        this.isshow1 = false;
        this.isshow4 = false;
        this.isshow6 = false;
        this.isshow7 = false;
        this.isshow8 = false;
        this.isshow10 = true;
        this.isshow11 = false;
        this.isshow12 = false;
        this.isshow13 = false;
        this.isshow14 = false;
        this.isshow15 = false;
        this.isshow16 = true;
        this.isshow17 = true;
        this.isshow18 = true;
        this.isshow20 = true;
        this.isshow21 = true;
        this.isshow22 = true;
      } else if (this.modelType == '7' || this.modelType == '19') {
        console.log('======报表管理========');
        // 收益分配表
        this.width = '470px';
        this.height = '420px';
        this.isshow1 = false;
        this.isshow4 = false;
        this.isshow6 = false;
        this.isshow7 = false;
        this.isshow8 = false;
        this.isshow10 = true;
        this.isshow11 = false;
        this.isshow12 = false;
        this.isshow13 = false;
        this.isshow14 = false;
        this.isshow15 = false;
        this.isshow16 = true;
        this.isshow17 = true;
        this.isshow18 = true;
        this.isshow20 = true;
        this.isshow22 = true;
      } else if (this.modelType == '11' || this.modelType == '12' || this.modelType == '13') {
        console.log('======报表管理========');
        this.width = '470px';
        this.height = '360px';
        this.isshow1 = false;
        this.isshow4 = false;
        this.isshow6 = false;
        this.isshow7 = false;
        this.isshow8 = false;
        this.isshow10 = true;
        this.isshow11 = false;
        this.isshow12 = false;
        this.isshow13 = false;
        this.isshow14 = false;
        this.isshow15 = false;
        this.isshow16 = true;
        this.isshow17 = true;
        this.isshow18 = true;
      } else if (this.modelType == '16') {
        //多栏明细账打印设置
        this.width = '470px';
        this.height = '360px';
        this.isshow1 = false;
        this.isshow4 = false;
        this.isshow6 = false;
        this.isshow7 = false;
        this.isshow8 = false;
        this.isshow13 = false;
        this.isshow12 = false;
        this.isshow11 = false; //不显示(显示打印人)
        this.isshow16 = true; //显示(显示打印单位负责人)
        this.isshow17 = true; //显示(显示财务主管)
        this.isshow18 = true; //显示(显示制表人)
      } else if (this.modelType == '20') {
        // 科目统计纵表
        this.isShowLabel2 = false;
        this.width = '470px';
        this.height = '360px';
        this.isshow1 = true; //纸张类型
        this.isshow2 = true; //打印方向
        this.isshow3 = true; //边距调整
        this.isshow4 = false; //每页做多打印凭证数量
        this.isshow5 = true; //字体大小
        this.isshow6 = false; //凭证分录汇总打印
        this.isshow7 = false; //制单人
        this.isshow8 = false; //附件张数
        this.isshow9 = true; //页码
        this.isshow10 = true; //编制日期
        this.isshow11 = true; //打印人
        this.isshow12 = false; //打印封面
        this.isshow13 = false; //打印目录
        this.isshow22 = true; //报表名称
        this.isshow23 = true; //每页科目列数
      }
      if (this.form) {
        this.printParams.start_mon = this.form.start_mon; // 会计期间起
        this.printParams.end_mon = this.form.end_mon; // 会计期间止
        this.printParams.showYe = Number(this.form.showYe);
        this.printParams.kmStatus = this.form.kmStatus;
        this.printParams.isAssist = Number(this.form.isAssist);
        this.printParams.fzhsType = this.form.fzhsType;
        this.printParams.fzhsName = this.form.fzhsName;
        (this.printParams.isNoLj = Number(this.form.isNoLj)),
          (this.printParams.noZero = Number(this.form.noZero)),
          (this.printParams.noFse = Number(this.form.noFse));
        this.printParams.pz_state = Number(this.form.pz_state);
        this.printParams.kmId = this.form.id;
        this.printParams.fzhsIds = this.form.fzhsIds;
        this.printParams.isAll = Number(this.form.isAll);
        this.printParams.kmJb = this.form.kmJb;
        this.printParams.ztId = this.form.ztId;
        this.printParams.fzId = this.form.fzId;
        (this.printParams.startMonth = this.form.startMonth), // 会计期间起
          (this.printParams.endMonth = this.form.endMonth), // 会计期间止
          (this.printParams.kmLevel = this.form.kmLevel), // 科目级次
          (this.printParams.pzStatus = this.form.pzStatus), // 未记账凭证
          (this.printParams.isShowFzhs = this.form.isShowFzhs), // 显示辅助核算
          (this.printParams.showFx = this.form.showFx), // 是否借贷两列显示
          (this.printParams.allPath = this.form.allPath),
          (this.printParams.urlflag = this.form.urlflag),
          (this.printParams.startDate = this.form.startDate),
          (this.printParams.endDate = this.form.endDate);
        (this.printParams.kmIds = this.form.kmIds);
        (this.printParams.subjectCode = this.form.subjectCode);
      } else {
        console.log('this.form为空对象');
      }

      //报表管理的参数
      if (this.modelType == '5' || this.modelType == '6' || this.modelType == '7' || this.modelType == '17' || this.modelType == '19') {
        this.printParams.reportType = this.form.reportType;
        this.printParams.reportId = this.form.reportId;
      } else if (this.modelType == '12' || this.modelType == '13') {
        //12、综合查询-收支明细表纵向表 13、综合查询-收益及收益分配表纵向表
        this.printParams.unitId = this.form.unitId;
        this.printParams.unitType = this.form.unitType;
        this.printParams.villageType = this.form.villageType;
        this.printParams.yearAndMonth = this.form.yearAndMonth;
        this.printParams.month = this.form.month;
        this.printParams.orgType = this.form.orgType;
        this.printParams.moneyUnit = this.form.moneyUnit;
        this.printParams.moneyDecimalNum = this.form.moneyDecimalNum;
        this.printParams.reportType = this.form.reportType;
        this.printParams.exclusionsUnitAllpath = this.form.exclusionsUnitAllpath;
        this.printParams.reportId = this.form.reportId;
        this.printParams.startYearAndMonth = this.form.startYearAndMonth;
        this.printParams.endYearAndMonth = this.form.endYearAndMonth;
      } else if (this.modelType == '11') {
        //  11、综合查询-资产负债表纵向表
        this.printParams.unitId = this.form.unitId;
        this.printParams.unitType = this.form.unitType;
        this.printParams.villageType = this.form.villageType;
        this.printParams.yearAndMonth = this.form.yearAndMonth;
        this.printParams.month = this.form.month;
        this.printParams.orgType = this.form.orgType;
        this.printParams.moneyUnit = this.form.moneyUnit;
        this.printParams.moneyDecimalNum = this.form.moneyDecimalNum;
        this.printParams.reportType = this.form.reportType;
        this.printParams.exclusionsUnitAllpath = this.form.exclusionsUnitAllpath;
        this.printParams.reportId = this.form.reportId;
        this.printParams.startYearAndMonth = this.form.startYearAndMonth;
        this.printParams.endYearAndMonth = this.form.endYearAndMonth;
        this.printParams.villageDelete = this.form.villageDelete;
      } else if (this.modelType == "15") {
        this.printParams.moneyEnd = this.form.moneyEnd,
          this.printParams.moneyStart = this.form.moneyStart,
          this.printParams.pzhEnd = this.form.pzhEnd,
          this.printParams.pzhStart = this.form.pzhStart,
          this.printParams.subjectCode = this.form.subjectCode;
      }
      this.getCurrentTime();
      this.printSet();
      console.log(this.form, '传过来的form');
      console.log(this.printParams, '赋值过form里面值以后的pp');
    },
    // 获取科目汇总到~级选项
    getOptions1(number) {
      let arr1 = [];
      this.options1 = [];
      for (let i = 0; i < number; i++) {
        arr1.push({
          value: i + 1,
          label: i + 1,
        });
      }
      this.options1 = arr1;
      console.log(this.options1, 'this.options1this.options1');
    },
    // 获取辅助核算汇总到~级选项
    getOptions2(number) {
      let arr2 = [];
      this.options2 = [];
      for (let i = 0; i < number; i++) {
        arr2.push({
          value: i + 1,
          label: i + 1,
        });
      }
      this.options2 = arr2;
      console.log(this.options2, 'this.options2this.options2');
    },
    // 当为明细账时判断有无生成目录
    cataLogue() {
      let params = {
        type: '1',
      }
      if (this.modelType == '2') {
        // 总账
        params.type = "0"
      } else if (this.modelType == '3') {
        // 明细账
        params.type = "1"
      }
      this.$ajax({
        method: 'post',
        url: '' + '/tatolExcel/catalogue.do',
        data: params,
      }).then((res) => {
        if (res.data.code == 9) {
          this.isshow14 = true;
        } else {
          this.isshow14 = false;
          console.log(res.data.msg);
        }
      });
    },
    // 获取打印的参数
    printSet() {
      this.$ajax({
        method: 'post',
        url: '' + '/fzhsExcel/getUserPrintInfo.do',
        data: {
          modelType: this.modelType,
        },
      }).then((res) => {
        if (res.data.code == 0) {
          let _res = res.data.data.config;
          if (_res !== null) {
            this.printParams.pageType = _res.pageType == null ? null : _res.pageType + '';
            this.printParams.pageWidth = _res.pageWidth == null ? null : _res.pageWidth + '';
            this.printParams.pageLength = _res.pageLength == null ? null : _res.pageLength + '';
            this.printParams.printDirection =
              _res.printDirection == null ? null : _res.printDirection + '';
            this.printParams.backGaugeLeft =
              _res.backGaugeLeft == null ? null : _res.backGaugeLeft + '';
            this.printParams.backGaugeUp = _res.backGaugeUp == null ? null : _res.backGaugeUp + '';
            this.printParams.everyPrintSize =
              _res.everyPrintSize == null ? null : _res.everyPrintSize + '';
            this.printParams.subjectNumberCol =
              _res.subjectNumberCol == null ? null : _res.subjectNumberCol + '';
            this.printParams.pingzCollect =
              _res.pingzCollect == null ? '0' : _res.pingzCollect + '';
            this.printParams.isSelectCollectKmLevel =
              _res.isSelectCollectKmLevel == null ? '1' : _res.isSelectCollectKmLevel + '';
            this.printParams.isSelectCollectFzhs =
              _res.isSelectCollectFzhs == null ? '0' : _res.isSelectCollectFzhs + '';
            this.printParams.isJieDaiCollect =
              _res.isJieDaiCollect == null ? '0' : _res.isJieDaiCollect + '';
            this.printParams.isJieDaiCollectFzhs =
              _res.isJieDaiCollectFzhs == null ? '0' : _res.isJieDaiCollectFzhs + '';
            this.printParams.fontSize = _res.fontSize == null ? null : _res.fontSize + '';
            this.printParams.pzPersonShow =
              _res.pzPersonShow == null ? null : _res.pzPersonShow + '';
            if (this.modelType === '20') {
              this.printParams.titName = (_res.titName == null || _res.titName === "") ? '科目情况统计' : _res.titName + '';
            } else {
              this.printParams.titName = _res.titName == null ? null : _res.titName + '';
            }
            this.printParams.doNotShowBuilder =
              _res.doNotShowBuilder == null ? null : _res.doNotShowBuilder + '';
            this.printParams.isShowDeptPerson =
              _res.isShowDeptPerson == null ? '0' : _res.isShowDeptPerson + '';
            this.printParams.isShowAccountant =
              _res.isShowAccountant == null ? '0' : _res.isShowAccountant + '';
            this.printParams.isShowLister =
              _res.isShowLister == null ? '0' : _res.isShowLister + '';
            this.printParams.deptPerson = _res.deptPerson == null ? null : _res.deptPerson + '';
            this.printParams.accountant = _res.accountant == null ? null : _res.accountant + '';
            this.printParams.lister = _res.lister == null ? null : _res.lister + '';
            this.printParams.doNotShowFileLength =
              _res.doNotShowFileLength == null ? null : _res.doNotShowFileLength + '';
            this.printParams.isShowPage = _res.isShowPage == null ? null : _res.isShowPage + '';
            this.printParams.isShowPageMx =
              _res.isShowPageMx == null ? null : _res.isShowPageMx + '';
            this.printParams.isShowPrintDate =
              _res.isShowPrintDate == null ? null : _res.isShowPrintDate + '';
            // this.printParams.printDate = _res.printDateStr;
            this.printParams.isShowPrinter =
              _res.isShowPrinter == null ? null : _res.isShowPrinter + '';
            if(this.modelType === '11' || this.modelType === '12' || this.modelType === '13'){
              this.printParams.isShowPrinter = '0'
            }
            // this.printParams.isShowFzhsGroup =
            //   _res.isShowFzhsGroup == null ? null : _res.isShowFzhsGroup + '';
            this.printParams.isShowSeal = _res.isShowSeal == null ? null : _res.isShowSeal + '';
            this.printParams.isShowQrCode =
              _res.isShowQrCode == null ? null : _res.isShowQrCode + '';
          } else {
            console.log(res.data.msg, '设置参数为空');
          }
        }
        if (this.modelType == '1' && this.printParams.pageType == '2') {
          this.isshow4 = false;
        }
      });
    },
    // 点击确定打印打开新窗口
    printOpen(URL, PARAMS) {
      console.log(URL, PARAMS, '这是url..............');
      var temp = document.createElement('form');
      temp.action = URL;
      temp.method = 'post';
      temp.target = '_blank'; //1.跳转新窗口需要设置该参数 2.当前页跳转可以去掉该参数
      temp.style.display = 'none';
      for (var x in PARAMS) {
        var opt = document.createElement('textarea');
        opt.name = x;
        opt.value = PARAMS[x];
        temp.appendChild(opt);
      }
      document.body.appendChild(temp);
      temp.submit();
      return temp;
    },
    // 点击确定打印按钮0是打印，1是保存并打印
    print(printType) {
      this.printParams.printType = printType;
      if (this.isShowTree1 == true || this.isShowTree2 == true) {

        if (this.modelType == '9') {
          if (this.printParams.isAll == '1') {
            this.printParams.fzhsIds = null;
            console.log(this.printParams, '确定打印');
            this.printOpen(this.printurl, this.printParams);
          } else {
            this.getFzhsIds();
            console.log(this.printParams.fzhsIds, 'fzhsIdsfzhsIds');
            if (!this.arr[0] || this.printParams.fzhsIds.length < 1) {
              this.$message.warning('请选择打印科目');
            } else {
              console.log(this.printParams, '确定打印');
              this.printOpen(this.printurl, this.printParams);
            }
          }
        } else if (this.modelType == '3') {
          this.printParams.endKm = Number(this.endKm);
          if (Number(this.isAll)) {
            this.printParams.isAll = Number(this.isAll);

            this.printParams.fzhsIds = null;
            console.log(this.printParams, '确定打印');
            if (this.modelType == '2') {
              // 总账
              this.printParams.type = "0"
            } else if (this.modelType == '3') {
              // 明细账
              this.printParams.type = "1"
            }
            this.printOpen(this.printurl, this.printParams);
          } else {
            this.printParams.isAll = Number(this.isAll);
            let selectAssetsTreeLength = this.$refs.selectAssetsTree.$children[0].getCheckedKeys().length;
            if (
              !this.arr[0] ||
              selectAssetsTreeLength == 0
            ) {
              this.$message({
                type: 'warning',
                message: '请选择打印科目!',
              });
            } else {
              this.Ids = this.$refs.selectAssetsTree.$children[0].getCheckedKeys();
              this.printParams.fzhsIds = this.Ids;
              console.log(this.printParams, '确定打印');
              this.printOpen(this.printurl, this.printParams);
            }
          }
        } else if (this.modelType == '2') {
          this.printParams.endKm = Number(this.endKm);
          if (Number(this.isAll)) {
            this.printParams.isAll = Number(this.isAll);

            this.printParams.fzhsIds = null;
            console.log(this.printParams, '确定打印');
            if (this.modelType == '2') {
              // 总账
              this.printParams.type = "0"
            } else if (this.modelType == '3') {
              // 明细账
              this.printParams.type = "1"
            }
            this.printOpen(this.printurl, this.printParams);
          } else {
            this.printParams.isAll = Number(this.isAll);
            let selectAssetsTreeLength = this.$refs.selectAssetsTree.$children[0].getCheckedKeys().length;
            if (
              selectAssetsTreeLength == 0
            ) {
              this.$message({
                type: 'warning',
                message: '请选择打印科目!',
              });
            } else {
              this.Ids = this.$refs.selectAssetsTree.$children[0].getCheckedKeys();
              this.printParams.fzhsIds = this.Ids;
              console.log(this.printParams, '确定打印');
              this.printOpen(this.printurl, this.printParams);
            }
          }
        }
      } else {
        if (this.modelType == '20') {
          //科目统计确定打印
          Object.assign(this.printParams, {
            dataId: this.kmStatusList.dataId,
            dataType: this.kmStatusList.dataType,
            dataAllPath: this.kmStatusList.dataAllPath,
            statisticalContent: this.kmStatusList.statisticalContent,
            startMonth: this.kmStatusList.startMonth,
            endMonth: this.kmStatusList.endMonth,
            moneyUnitSet: this.kmStatusList.moneyUnitSet,
            moneySet: this.kmStatusList.moneySet,
            ruleOutARegionAllPath: this.kmStatusList.ruleOutARegionAllPath,
            subjectCode: this.kmStatusList.subjectCode,
            isContainKeepAccounts: this.kmStatusList.isContainKeepAccounts,
            kmDeptType: this.kmStatusList.kmDeptType,
          });
          console.log(this.printParams, '确定打印');
          this.printOpen(this.printurl, this.printParams);
        } else {
          // 如果父组件没有传form，空对象JSON.stringify(this.form)为true
          console.log(this.form, 'oooooo');
          if (this.form) {
            if (this.form.isAll == '1') {
              this.printParams.fzhsIds = null;
            }
          } else {
            console.log('this.form为空对象');
          }
          if (this.printParams.pageWidth == '' || this.printParams.pageLength == '') {
            this.$message({
              type: 'warning',
              message: '打印宽度,高度为空,请输入50~1500mm!',
            });
            return false;
          }
          if (this.modelType === '5' && this.printParams.titName == '') {
            this.$message({
              type: 'warning',
              message: '报表名称不能为空',
            });
            return false;
          }
          if (this.modelType === '6' && this.printParams.titName == '') {
            this.$message({
              type: 'warning',
              message: '报表名称不能为空',
            });
            return false;
          }
          if (this.modelType === '7' && this.printParams.titName == '') {
            this.$message({
              type: 'warning',
              message: '报表名称不能为空',
            });
            return false;
          }
          console.log(this.printParams, '确定打印');
          this.printOpen(this.printurl, this.printParams);
        }
      }
    },
    // 点击取消关闭弹框
    close() {
      this.$refs.printTreeDialog.close();
      this.printParams.pageType = '1';
      this.printParams.pageWidth = '124';
      this.printParams.pageLength = '210';
      this.printParams.printDirection = '2';
      this.printParams.backGaugeLeft = '15';
      this.printParams.backGaugeUp = '10';
      this.printParams.everyPrintSize = '2';
      this.printParams.subjectNumberCol = '1';
      this.printParams.fontSize = '15';
      this.printParams.pingzCollect = '0';
      this.printParams.isSelectCollectKmLevel = '1';
      this.printParams.isSelectCollectFzhs = '0';
      this.printParams.isJieDaiCollectFzhs = '0';
      this.printParams.isJieDaiCollect = '0';
      this.printParams.pzPersonShow = '0';
      this.printParams.doNotShowBuilder = '1';
      this.printParams.doNotShowFileLength = '0';
      this.printParams.isShowPage = '1';
      this.printParams.isShowPageMx = '0';
      this.printParams.printDate = '1';
      this.printParams.isShowPrintDate = '1';
      this.printParams.printDate = '';
      this.printParams.isShowPrinter = '1';
      // this.printParams.isShowFzhsGroup = '0';
      this.printParams.isShowSeal = '0';
      this.printParams.isShowQrCode = '0';
      this.printParams.isShowDeptPerson = '0';
      this.printParams.deptPerson = '';
      this.printParams.isShowAccountant = '0';
      this.printParams.accountant = '';
      this.printParams.isShowLister = '0';
      this.printParams.lister = '';
      this.printParams.titName = '';
      this.checkStrict = false;
      this.isShowLabel2 = true;
      if (this.modelType == '9') {
        (this.printParams.isAll = '0'), (this.printParams.endKm = '0');
      }
      if (this.modelType == '3') {
        this.isAll = false;
        this.endKm = false;
        this.getTreeDataLedger();
      }
      // this.defaultClickNodes = [];
    },
    // 点击右上角×按钮
    handleClose() {
      this.close();
    },
    //获取FzhsIds
    getFzhsIds() {
      let ids = this.$refs.treeNode.$children[0].getCheckedKeys();
      this.ids = [];
      for (let id of ids) {
        this.dg(this.treeData, id);
      }
      console.log(this.ids);
      this.printParams.fzhsIds = this.ids;
    },
    // 递归方法
    dg(nodes, id) {
      for (let node of nodes) {
        if (node.id === id) {
          this.ids.push(`${id}-${node.type}`);
          break;
        } else {
          if (node.sub) {
            this.dg(node.sub, id);
          }
        }
      }
    },
    //获取明细树选中的值
    treeClickChooseMx(nodeObj, SelectedObj) {
      console.log(SelectedObj);
      this.arr = SelectedObj.checkedKeys;
      this.selectValue = [];
      this.selectValue = SelectedObj.checkedNodes;
      var assetsvalue = '';
      for (let i = 0; i < this.selectValue.length; i++) {
        if (this.selectValue[i].children == null) {
          assetsvalue = this.selectValue[i].name;
          break;
        }
      }
      if (assetsvalue != '') {
        this.assetsValue = assetsvalue + '...';
      } else {
        this.assetsValue = '';
      }
      this.$emit('func', this.selectValue);
      this.Ids = SelectedObj.checkedKeys;
    },
    //获取辅助核算树选中的值
    treeClickChoosefzhs(nodeObj, SelectedObj) {
      // console.log(SelectedObj,'888888888888888888888889999');
      this.arr = SelectedObj.checkedKeys;
      // console.log(this.arr,'aaaaaaaaaaaaaaaaa');
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-content {
  .img {
    width: 12px;
    height: 12px;
  }

  .modal-left {
    width: 40%;
    padding-left: 15px;
    padding-top: 22px;

    .tree {
      width: 240px;
      height: 322px;
      overflow: hidden;
      overflow-y: scroll;
      border: 1px solid #d5e0f0;
      background: #fff;
    }

    .tree::-webkit-scrollbar {
      width: 10px;
    }

    .tree::-webkit-scrollbar-thumb {
      border-radius: 4px;
      // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      background: #d4e6fb;
    }

    .tree::-webkit-scrollbar-track {
      // box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 0;
      background: #f2f7ff;
    }

    .checkbox {
      margin-top: 16px;
      display: flex;
    }
  }

  .modal-right {
    padding-left: 17px;
    padding-top: 14px;

    .row {
      display: flex;
      align-items: center;
      line-height: 32px;
      font-size: 12px;
      height: 32px;

      .label {
        margin-right: 20px;
      }
    }
  }
}

.flex-center {
  display: flex;
  align-items: center;
}

.mr5 {
  margin-right: 5px;
}

.ml5 {
  margin-left: 5px;
}

.mr10 {
  margin-right: 20px;
}

::v-deep .dialog-content {
  display: flex;
}

::v-deep .el-checkbox__label {
  padding-left: 6px;
}

::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #555555;
}

::v-deep .el-radio__label {
  padding-left: 3px;
  font-size: 12px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #555555;
}

::v-deep .el-radio {
  margin-right: 10px !important;
}

::v-deep .el-radio__inner {
  width: 13px;
  height: 13px;
}

::v-deep .el-radio__input.is-checked+.el-radio__label {
  font-size: 12px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #555555;
}

::v-deep .nd-input-box .el-input__inner {
  height: 22px;
  line-height: 22px;
  border: 1px solid #dce5f3;
}

::v-deep .nd-select-box .el-input__inner {
  width: 74px;
  height: 18px;
  line-height: 18px;
  border: 1px solid #dce5f3;
}

::v-deep .nd-dropdown-box {
  width: 84px;
  height: 28px;
  background: #ffffff;
  border-radius: 5px;
  margin-right: 48px;
}

::v-deep .el-button {
  color: #0098ff;
  background-color: #ffffff;
  border: 1px solid #0098ff;
}

::v-deep .nd-date-picker-box .el-date-editor.el-input {
  width: 170px;
}

::v-deep .el-checkbox__label {
  font-family: Microsoft YaHei;
  font-weight: 400;
  font-size: 12px;
  color: #555555;
}
</style>
