<template>
  <div class="top-box">
    <div class="banner">
      <div class="banner-box">
        <el-carousel height="358px" loop arrow="always" v-if="
          datas.picFileList &&
          datas.picFileList.length != 0 &&
          datas.picFileList[0].fileList.length != 0
        ">
          <el-carousel-item v-for="(item, index) in datas.picFileList[0].fileList" :key="index">
            <div class="item"><img :src="item.fileUrl" alt="" /></div>
          </el-carousel-item>
        </el-carousel>
        <div v-else style="height: 358px">
          <img :src="defaultUrl" alt="" style="display: block; width: 100%; height: 100%" />
        </div>
      </div>
    </div>
    <div class="info-box">
      <div class="titles">
        {{ datas.projectName }}
      </div>
      <div class="centent-item">
        <div class="titles">项目编号：</div>
        <div class="centent-item-text">{{ datas.projectCode || '--' }}</div>
      </div>
      <div class="centent-item">
        <div class="titles">项目位置：</div>
        <div class="centent-item-text">{{ datas.addressLocation || '--' }}</div>
      </div>
      <div class="centent-item">
        <div class="titles">产权类型：</div>
        <div class="centent-item-text">{{ datas.childTradeVarietyName || '--' }}</div>
      </div>
      <div class="centent-item">
        <div class="titles">流转方式：</div>
        <div class="centent-item-text">
          {{ datas.tradeType || '--' }}
        </div>
      </div>
      <div class="centent-item">
        <div class="titles">流转年限：</div>
        <div class="centent-item-text">
          <template v-if="datas.yearNum !== null">
            {{ datas.yearNum + '年' }}
          </template>
          <template v-else> -- </template>
        </div>
      </div>
      <div class="centent-item">
        <div class="titles">登记日期：</div>
        <div class="centent-item-text">{{ datas.regTime || '--' }}</div>
      </div>
      <!-- <div class="centent-item" v-if="'A,E,G,D,B,F,O,Y,T,K'.indexOf(datas.baseLb) != -1">
        <div class="titles">交易面积：</div>
        <div class="centent-item-text">
          <span class="num"> {{ datas.tradeArea || '--' }}</span>
          <template v-if="
            datas.tradeArea !== '--' &&
            datas.tradeArea !== '' &&
            datas.tradeArea !== null &&
            datas.tradeArea !== undefined
          ">
            {{ datas.tradeAreaUnit }}
          </template>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  props: {
    datas: {
      type: Object,
      default: () => {
        return {};
      },
    },
    titles: {
      type: String,
      default: '',
    },
    showYXQ: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      defaultUrl: '',
    };
  },
  mounted() {
    this.defaultUrl = window.ipConfig.defaultImg;
  },
  methods: {

  },
};
</script>

<style lang="scss" scoped>
.top-box {
  width: 100%;
  max-width: 1300px;
  display: flex;
  margin-bottom: 30px;

  >div {
    margin-right: 15px;

    &:nth-last-of-type(1) {
      margin-right: 0;
    }
  }

  .banner {
    min-width: 450px;
    max-width: 450px;
    height: 358px;
    margin-right: 20px;

    .banner-box {
      margin-bottom: 20px;

      .item {
        width: 100%;
        height: 100%;
        background-color: #333;

        >img {
          width: 100%;
          height: 100%;
          display: block;
        }
      }

      ::v-deep .el-carousel__arrow {
        background-color: transparent;
      }

      ::v-deep .el-carousel__arrow i {
        color: #ffffff;
        font-size: 32px;
      }
    }

    .tool {
      display: flex;

      >div {
        margin-right: 20px;
        width: 80px;
        height: 36px;
        background: #fbfbfb;
        padding: 11px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        >img {
          margin-right: 9px;
          width: 16px;
          height: 14px;
        }

        >span {
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;
        }
      }
    }
  }

  .info-box {
    flex: 1;
    position: relative;
    // min-width: 445px;
    // max-width: 445px;

    >.titles {
      // font-size: 20px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      margin-bottom: 20px;
      word-wrap: break-word;
    }

    .zzbm {
      height: 62px;
      width: 100%;
      padding: 0 19px;
      background-image: url('@/assets/detail/zzbmBackGround.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .titles {
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #ff5a5a;
      }

      .main {
        display: flex;

        >div {
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #555555;
        }

        >div:nth-of-type(1) {
          margin-right: 20px;
        }
      }
    }

    .zyjj {
      .main {
        .num {
          font-size: 16px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #ff5a5a;
          margin-right: 10px;
          // line-height: 1;
        }
      }
    }

    .end {
      background-image: url('@/assets/detail/end.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      .titles {
        color: #828282;
      }
    }

    .lb {
      background-image: url('@/assets/detail/lb.png');

      .titles {
        color: #828282;
      }
    }

    .centent-item {
      display: flex;
      align-items: center;
      // margin-bottom: 15px;
      margin-bottom: 10px;

      &:nth-last-of-type(1) {
        // margin-bottom: 22px;
      }

      .titles {
        width: 70px;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #999999;
      }

      .centent-item-text {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #333333;
        display: flex;
        align-items: center;

        .num {
          font-size: 18px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #ed911f;
          margin-right: 10px;
          line-height: 1;
        }
      }
    }
  }

  .registration-box {
    // min-width: 256px;
    // max-width: 256px;
    min-width: 270px;
    max-width: 270px;
    // height: 414px;
    // height: 350px;
    height: 358px;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .ljbm-bottom {
      // position: absolute;
      // left: 0;
      // bottom: 0;
      width: 100%;
      height: 42px;
      // height: 36px;
      background: #ed911f;
      display: flex;
      align-items: center;
      justify-content: center;

      // font-size: 14px;

      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #ffffff;

      cursor: pointer;
    }

    >.registration-box-div {
      padding: 10px;
      border: 1px solid #eeeeee;
    }

    .title {
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
      margin-bottom: 10px;
    }

    .centent-item {
      display: flex;
      margin-bottom: 10px;

      &:nth-last-of-type(1) {
        margin-bottom: 0;
      }

      .titles {
        min-width: 100px;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #999999;
      }

      .centents {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #333333;
      }
    }
  }
}
</style>
