# 栏目列表组件使用说明

## 组件简化方案

为了降低栏目列表组件的使用成本，我们提供了两种使用方式：

### 1. 原有组件（保持兼容）
`ndColumnListTypeC.vue` - 保持原有功能不变，支持所有现有参数

### 2. 简化版组件（推荐新项目使用）
`ndColumnListSimple.vue` - 简化参数，自动推断面包屑信息

## 使用对比

### 原有方式（复杂）
```vue
<template>
  <ColumnListTypeC
    :columnId="currentColumnId"
    :breadcrumbName="currentBreadcrumbName"
    :parentBreadcrumbName="currentParentBreadcrumbName"
    :menuPath="currentMenuPath"
    :bannerType="getBannerType()"
  />
</template>

<script>
export default {
  data() {
    return {
      currentColumnId: '',
      currentBreadcrumbName: '',
      currentParentBreadcrumbName: '',
      currentMenuPath: []
    };
  },
  methods: {
    getBannerType() {
      const routeNameMap = {
        'newsCenter': '1',
        'businessRule': '3',
        // ... 更多映射
      };
      return routeNameMap[this.$route.name] || '1';
    }
  }
};
</script>
```

### 简化方式（推荐）
```vue
<template>
  <ColumnListSimple
    :columnId="currentColumnId"
    :breadcrumbName="currentBreadcrumbName"
  />
</template>

<script>
export default {
  data() {
    return {
      currentColumnId: '',
      currentBreadcrumbName: '', // 可选，不传则自动推断
    };
  }
};
</script>
```

## 简化版组件特性

### 自动推断功能
1. **自动推断父级面包屑**：根据路由名称自动确定父级面包屑
2. **自动推断bannerType**：根据路由名称自动确定banner类型
3. **简化参数传递**：只需要传递 `columnId` 和可选的 `breadcrumbName`

### 支持的路由映射
```javascript
const routeNameMap = {
  'newsCenter': { parent: '新闻中心', bannerType: '1' },
  'businessRule': { parent: '业务规则', bannerType: '3' },
  'lawsRegulation': { parent: '政策法规', bannerType: '4' },
  'profileDownload': { parent: '资料下载', bannerType: '5' },
  'aboutUs': { parent: '关于我们', bannerType: '6' },
  'interacteCommunication': { parent: '互动交流', bannerType: '7' }
};
```

## 完整页面组件

### ndColumnListPageSimple.vue
提供了完整的栏目列表页面，包含：
- 动态tab菜单
- 简化的列表组件
- 自动的面包屑处理

使用方式：
```vue
<template>
  <ColumnListPageSimple
    :columnId="columnId"
    :pageName="pageName"
  />
</template>
```

## 迁移建议

### 对于新项目
直接使用 `ndColumnListSimple.vue` 或 `ndColumnListPageSimple.vue`

### 对于现有项目
1. **保持现状**：现有代码无需修改，继续使用 `ndColumnListTypeC.vue`
2. **逐步迁移**：新功能使用简化版组件
3. **完全迁移**：将现有调用替换为简化版本

## 向后兼容性

- 原有组件 `ndColumnListTypeC.vue` 保持完全兼容
- 新增了 `autoInferBreadcrumb` 参数，可以在原组件中启用自动推断功能
- 所有现有功能和API保持不变

## 使用建议

1. **新项目**：优先使用简化版组件
2. **现有项目**：根据实际情况选择是否迁移
3. **复杂场景**：如果需要特殊的面包屑逻辑，继续使用原组件
4. **简单场景**：使用简化版组件，减少代码量和维护成本
