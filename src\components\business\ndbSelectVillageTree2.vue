<template>
  <div class="ndb-select-organize-tree-box">
    <nd-select v-if="show" id="wbJspTreeSelect" ref="ndSelect" v-el-select-loadmore="loadmore"
      popper-class="matterNameClass" :filterable="true" :remote="true" :remote-method="remoteMethod" :disabled="disabled"
      :value="nodeName" :placeholder="placeholder || '请选择'" :width="width" @change="change" @focus="focus">
      <!-- <el-empty description="暂无数据" v-show="isShowText" style="position:relative;top:-20px;"></el-empty> -->
      <div v-show="isShowText">
        <span style="color: #555555; font-size: 14px;display: block;text-align: center;padding: 15px 0px;">暂无数据</span>
      </div>
      <el-option v-for="(item, index) in options" v-show="showOption" :key="index" :label="item.name" :value="item"
        class="text-hover">
        <div v-if="item.type == 'dq'" style="position:relative;padding: 10px 0px;">
          <span style="color: #555555; font-size: 14px;display: block;height:10px;line-height:10px;"
            v-html="keyHights(item.name)" />
        </div>
        <div v-else style="position:relative;">
          <span style="color: #555555; font-size: 14px;display: block;height: 25px;line-height: 25px;"
            v-html="keyHights(item.name)" />
          <span
            style="display: block;color: #999999; font-size: 12px;height: 25px;line-height: 25px;position: relative;top: -6px;"
            v-html="keyHights(item.unitName)" />
        </div>
      </el-option>
      <el-option v-show="showOptionTree" :value="nodeName" :label="nodeName">
        <el-tree id="tree-option" ref="tree" highlight-current :load="loadNode" lazy :props="defaultProps" node-key="id"
          :default-expanded-keys="treeExpandIdList" :expand-on-click-node="false" :show-checkbox="showCheckbox"
          @node-expand="expand" @node-click="handleNodeClick" @check="checkHandler">
          <!-- <span slot-scope="{ data }" class="custom-tree-node">
            <span v-if="data.type == 1 ? true : false">
              <img src="@/assets/area.png">&nbsp;&nbsp;{{ data.name }}
            </span>
            <span v-if="data.type == 2 ? true : false">
              <img src="@/assets/organization.png">&nbsp;&nbsp;{{ data.name }}
            </span>
            <span v-if="data.type === 'dq'">
              <img src="@/assets/area.png">&nbsp;&nbsp;{{ data.name }}
            </span>
            <span v-if="data.type === 'bm'">
              <img src="@/assets/organization.png">&nbsp;&nbsp;{{ data.name }}
            </span>
          </span> -->
        </el-tree>
      </el-option>
    </nd-select>
  </div>
</template>
<script>
// import ndSelect from "@/components/ndSelect.vue";
import ndSelect from "../ndSelect.vue";
// Vue.prototype.ndPageSize = 100//全局分页数
// import { set } from 'vue';
// npm run lint src/components/business/ndbSelectVillageTree.vue
export default {
  components: {
    ndSelect,
  },
  directives: {//自定义指令事件
    'el-select-loadmore': {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight
          if (condition) {
            binding.value();
          }
        });
      }
    }
  },
  model: {
    prop: 'areaVal',
    event: "change",
  },
  props: {
    // 是否不可更改地区
    disabled: {
      type: Boolean,
      default: false,
    },
    // 宽度
    width: {
      type: String,
      default: "220px",
    },
    // 地区编码
    areaCode: {
      type: String,
      default: "",
    },
    // 部门Id
    areaVal: {
      // type: String | Array,
      type: [Number, String, Object],
      default: null,
    },
    // 部门Id
    deptId: {
      // type: String | Array,
      type: [String, Array],
      default: null,
    },
    // 可以选择的节点，传node.level的值，比如[-1,1,3]，其中-1比较特殊，代表组织，不传代表全部可以点
    canSelectNodes: {
      type: Array,
      default: () => [],
    },
    // 后端服务器名称
    serverName: {
      type: String,
      default: "nd-village",
    },
    // 是否是三资项目
    isSz: {
      type: Boolean,
      default: false,
    },
    // 是否多选
    showCheckbox: {
      type: Boolean,
      default: false,
    },
    // 是否包含组织
    showOrganization: {
      type: Boolean,
      default: true,
    },
    // 显示的最小层级
    minLevel: {
      type: Number,
      default: -1,
    },
    // 是否使用token进行数据过滤
    useToken: {
      type: Boolean,
      default: true,
    },
    // 地区节点不能点击的提示语
    organizationNotClickText: {
      type: String,
      default: "请选择申请组织",
    },
    // 组织节点不能点击的提示语
    areaNotClickText: {
      type: String,
      default: "请选择地区",
    },
    // 组织节点不能点击的提示语
    areaText: {
      type: String,
      default: "请选择单位",
    },
    // 以下是合并开始
    treeName: {
      type: String,
      default: "",
    },
    treeModule: {
      type: String,
      default: "",
    },
    payeeNum: {
      type: Number,
      default: 0,
    },
    deptType: {//只能单位赋值时 传dw
      type: String,
      default: "",
    },
    zjht: {
      type: String,
      default: "",
    },
    // 是否只显示地区
    isShowArea: {
      type: Boolean,
      default: false,
    },
    // 是否只显示地区且是否需要回显默认值
    isShowAreaValue: {
      type: Boolean,
      default: false,
    },
    // getDeptId:{
    //   type: String,
    //   default: "",
    // }
    //合并结束
  },
  data() {
    return {
      isShowText: false,
      placeholder: "",
      treeExpandIdList: [],
      defaultProps: {
        id: "id",
        level: "level",
        label: "name",
        children: "children",
        isLeaf: "isLeaf",
      },
      show: true,
      nodeName: "",
      firstWatchDeptId: true,
      connection: false, //所属地区默认显示字段
      options: [],
      showOption: false,
      showOptionTree: true,
      pageIndex: 1,
      queryParams: {
        id: '',
        name: this.inputVal,
        pageIndex: this.pageIndex,
        pageSize: '10',
        showBm: '1'
      },
      // 合并开始
      valueID: '',
      currentNode: [],
      completeFlag: true,
      areaCodeValue: '',
      isFirst:true
      // 合并结束
    };
  },
  watch: {
    'areaVal.deptName': {
      immediate: true,
      deep: true,
      handler(value) {
        // console.log(value+'11166')
        if (value != '' && value != undefined) {
          this.nodeName = value;
          // this.areaVal.deptName=value;
        }
      }
    },
    // 'areaVal.deptId': {
    //   immediate: true,
    //   deep: true,
    //   handler(value) {
    //     if (value != '' && value != undefined) {
    //       this.areaVal.deptId=value;
    //       console.log(this.areaVal.deptId+'this.areaVal.deptId')
    //     }
    //   }
    // },
    areaCode: {
      immediate: true,
      deep: true,
      handler(value) {
        if (value != '') {
          this.areaCodeValue = value;
        }
      }
    },
    deptId: {
      // immediate: true,
      handler(value) {
        if (value === "" || value == null || value == "null") {
          this.$refs.tree.setCheckedKeys([]);
          this.nodeName = "";
        }
        if (this.firstWatchDeptId) {
          this.firstWatchDeptId = false;
          let url = "";
          if (this.isSz) {
            if (value) {
              value = value.split("|");
              value = value[0];
            }
            let params = {
              id: value,
            };
            this.$ajax({
              url: "/financialAuthority/commonTreeDataAsync.do",
              method: "post",
              data: params,
            })
              .then((res) => {
                if (0 == res.data.code && res.data.data.tree.length > 0) {
                  // empty
                  // this.nodeName = res.data.data.tree[0].name;
                } else {
                  this.nodeName = "未知";
                }
              })
              .catch(function () {
                this.$message.error("未知");
              });
          } else {
            if (this.serverName === "nd-disciplineSupervision") {
              // url = "/area/findAll";
              url = "/area/toChild";
            } else {
              url = "/bnsProInfo/getAreaTree";
            }
            this.$ajax({
              method: "get",
              url: url,
              data: {
                areaId: value,
                // areaCode: "",
              },
              serverName: this.serverName,
            })
              .then((response) => {
                if (response.data.code == 200) {
                  this.nodeName = response.data.data[0].name;
                } else {
                  this.nodeName = "未知";
                }
              })
              .catch(() => {
                this.$message.error("未知");
              });
          }
        }
      },
    },
  },
  mounted() {
    // this.treeReload();
  },
  methods: {
    keyHights(title) {// 改变搜索关键字颜色为蓝色
      let s = this.queryParams.name;
      var str = title;
      s = s.replace(/[()]/ig,(item)=>{
        switch(item){
            case '(':
                return "";
            case ')':
                return "";
        }
    })
      let inputvalue = s.replace(",");
      let keyWordArr = inputvalue.split(",");
      if (str && str != "") {
        keyWordArr.forEach((e) => {
          let regStr = "" + `(${e})`;
          let reg = new RegExp(regStr, "g");
          str = str.replace(reg, '<span style="color:#0098FF;position:relative;top:-2px;">' + e + "</span>");
        });
      }
      return str;
    },
    // 远程搜索列表触底事件
    loadmore() {
      if (this.completeFlag == true) {
        this.pageIndex++;
        this.queryParams.pageIndex = this.pageIndex.toString()
        this.selectMethod();
      } else {
        if (this.options.length != 0) {
          this.isShowText = false;
        }
        return false;
      }

    },
    remoteMethod(query) {
      if (query !== '') {
        let that = this;
        setTimeout(() => {
          that.options = [];
          that.isShowText = false;
          that.showOption = true;
          that.showOptionTree = false;
          that.queryParams.name = query;
          that.pageIndex = 1;
          that.queryParams.pageIndex = '1';
          that.completeFlag = true;
          that.selectMethod();
        }, 200);
      } else {
        this.isShowText = false;
        this.pageIndex = 1;
        this.queryParams.pageIndex = '1';
        this.completeFlag = true;
        this.showOption = false;
        this.showOptionTree = true;
      }
    },
    focus() {//获得焦点时触发
      this.options = [];
      this.pageIndex = 1;
      this.queryParams.pageIndex = '1';
      this.showOption = false;
      this.showOptionTree = true;
      this.isShowText = false;
    },
    change(e) {//取用户下拉选取值
      this.isShowText = false;
      this.nodeName = e.name;
      if (this.isSz) {
        this.$emit('change', e)
      } else {
        this.$emit("nodeClick", e.id, e.level);
        this.$emit("getPlace", e);
      }

    },
    selectMethod() {
      // financialAuthority/commonTreeDataAsync
      return new Promise((resolve) => {
        let areaUrl = '/financialAuthority/commonTreeDataAsync.do'
        if (this.isShowArea == true) {
          this.queryParams.showBm = '0'
          // areaUrl='/financialAuthority/commonTreeDataAsyncStatistics.do'
        } else {
          this.queryParams.showBm = '1'
          // areaUrl='/financialAuthority/commonTreeDataAsyncStatisticsWithDept.do'
        }
        this.$ajax({
          method: "post",
          url: "" + areaUrl,
          data: this.queryParams,
        })
          .then((response) => {
            if (response.data.data.unitList.length == 0) {
              this.completeFlag = false;
              if (this.options.length == 0 && this.showOptionTree == false) {
                this.isShowText = true;
              } else {
                this.isShowText = false;
              }
              return false;
            } else {
              this.completeFlag = true;
              this.isShowText = false;
              this.options = [...this.options, ...response.data.data.unitList];
            }
            resolve(true);
          })
          .catch(() => {
            resolve(true);
          });

      });
    },
    // 重新加载数据
    treeReload() {
      this.show = false;
      this.$nextTick(() => {
        this.show = true;
      });
    },
    // 获得数据
    loadNode(node, resolve) {
      let _this = this;
      _this.currentNode = [];
      var id = "";
      // var zeroIndex = 0;
      if (node.parent === null) {
        id = "";
        // zeroIndex =0;
      } else {
        id = node.data.id;
        // zeroIndex = '';
      }
      let url = "";
      if (this.isSz) {
        if (this.isShowArea == true) {
          let params = {
            id: id,
          };
          this.$ajax({
            url: "/financialAuthority/commonTreeDataAsyncNew.do",
            method: "post",
            data: params,
          })
            .then((res) => {
              if (0 == res.data.code && res.data.data.tree.length > 0) {
                var currentNode = res.data.data.tree[0];
                _this.currentNode = res.data.data.tree;
                // 合并开始
                //添加默认展开节点id(hasChild代表有下级才会默认展开)
                if (node.level === 0 && _this.currentNode && Array.isArray(_this.currentNode) && _this.currentNode[0].hasChild) {
                  _this.currentNode.forEach(item => {
                    _this.treeExpandIdList.push(item.id);
                    _this.nodeName = item.name;
                    _this.valueID = item.id;
                  });
                  if (_this.treeModule == "financiaAddApplydqs") {
                    //如果是资金报账申请页面的，不是5类组织，不允许显示上去
                    if (_this.currentNode[0].type == "bm") {
                      _this.$emit('change', _this.currentNode[0]);
                    } else {
                      this.nodeName = '';
                      _this.currentNode[0].financiaAddApplyISNotBM = true;
                      _this.$emit('change', _this.currentNode[0]);
                    }
                  } else {
                    if (this.deptType == 'dw') {
                      if (this.areaVal == null || this.areaVal == undefined) {
                        _this.$emit('change', _this.currentNode[0]);
                        this.nodeName = '';
                      } else {
                        this.nodeName = this.areaVal.deptName || '';
                        _this.$emit('change', _this.areaVal);
                      }
                    } else {
                      // _this.nodeName=_this.currentNode[0].name;
                      // _this.$emit('change', _this.currentNode[0]);
                      // if (this.zjht == 1) {
                      //   // empty
                      // } else {
                      //   _this.$emit('change', _this.currentNode[0]);
                      // }
                    }

                  }
                  //针对需要回显到select上面的名称
                  if (this.treeName != '' && this.treeName != null && this.treeName != undefined) {
                    this.nodeName = this.treeName;
                  }
                  if (this.areaVal == null || this.areaVal == undefined) {
                    _this.$emit('change', _this.currentNode[0]);
                    this.nodeName = '';
                    if (this.isShowAreaValue == true) {
                      _this.nodeName = _this.currentNode[0].name;
                      _this.$emit('change', _this.currentNode[0]);
                    }
                  } else {
                    this.nodeName = this.areaVal.deptName || '';
                    _this.$emit('change', _this.areaVal);
                  }
                }
                // 合并结束
                if (node.level === 0) {
                  return resolve([currentNode]);
                } else {
                  return resolve(currentNode.children);
                }

              } else {
                this.$message.error("获取地区信息失败！");
              }
            })
            .catch(function () {
              this.$message.error("获取地区信息失败！");
            });

        } else {
          let params = {
            id: id,
          };
          this.$ajax({
            url: "/financialAuthority/commonTreeDataAsync.do",
            method: "post",
            data: params,
          })
            .then((res) => {
              if (0 == res.data.code && res.data.data.tree.length > 0) {
                var currentNode = res.data.data.tree[0];
                _this.currentNode = res.data.data.tree;
                // 合并开始
                //添加默认展开节点id(hasChild代表有下级才会默认展开)
                if (node.level === 0 && _this.currentNode && Array.isArray(_this.currentNode) && _this.currentNode[0].hasChild) {
                  _this.currentNode.forEach(item => {
                    _this.treeExpandIdList.push(item.id);
                    _this.nodeName = item.name;
                    _this.valueID = item.id;
                  });
                  _this.currentNode[0].isclick = false
                  // console.log(_this.currentNode[0],"===========_this.currentNode[0]");
                  // if (_this.treeModule == "financiaAddApplydqs") {
                  //   //如果是资金报账申请页面的，不是5类组织，不允许显示上去
                  //   if (_this.currentNode[0].type == "bm") {
                  //     _this.$emit('change', _this.currentNode[0]);
                  //   } else {
                  //     this.nodeName = '';
                  //     _this.currentNode[0].financiaAddApplyISNotBM = true;
                  //     _this.$emit('change', _this.currentNode[0]);
                  //   }
                  // } else {
                  //   _this.$emit('change', _this.currentNode[0]);
                  // }
                  if (_this.treeModule == "financiaAddApplydqs") {
                    //如果是资金报账申请页面的，不是5类组织，不允许显示上去
                    if (_this.currentNode[0].type == "bm") {
                      _this.$emit('change', _this.currentNode[0]);
                    } else {
                      this.nodeName = '';
                      _this.currentNode[0].financiaAddApplyISNotBM = true;
                      _this.$emit('change', _this.currentNode[0]);
                    }
                  } else {
                    if (this.deptType == 'dw') {
                      if (this.areaVal == null || this.areaVal == undefined) {
                        _this.$emit('change', _this.currentNode[0]);
                        this.nodeName = '';
                      } else {
                        this.nodeName = this.areaVal.deptName || '';
                        _this.$emit('change', _this.areaVal);
                      }
                    } else {
                      if (this.zjht == 1) {
                        // empty
                      } else {
                        _this.$emit('change', _this.currentNode[0]);
                      }
                      if (this.areaVal == null || this.areaVal == undefined || this.areaVal.deptId == '' || this.areaVal.deptName == '') {
                        _this.$emit('change', _this.currentNode[0]);
                      } else {
                        // this.nodeName = this.areaVal.deptName || this.areaVal.ssdw || '';
                        this.nodeName = this.areaVal.deptName || '';
                        _this.$emit('change', _this.areaVal);

                      }
                    }

                  }

                  //针对需要回显到select上面的名称
                  if (this.treeName != '' && this.treeName != null && this.treeName != undefined) {
                    this.nodeName = this.treeName;
                  }
                }
                // 合并结束
                // if (this.connection) {
                //   this.nodeName = currentNode.name;
                //   this.connection = false;
                // }
                if (node.level === 0) {
                  // this.node = node
                  //  this.resolveFunc = resolve
                  // 默认展开第一级
                  // this.treeExpandIdList.push(currentNode.id);
                  return resolve([currentNode]);
                } else {
                  return resolve(currentNode.children);
                }

              } else {
                this.$message.error("获取地区信息失败！");
              }
            })
            .catch(function () {
              this.$message.error("获取地区信息失败！");
            });

        }

      } else {
        if (this.serverName === "nd-disciplineSupervision") {
          // url = "/area/findAll";
          url = "/area/toChild";
        } else {
          url = "/bnsProInfo/getAreaTree";
        }
        // url = "/area/findAll";
        // let params;
        // if(this.isFirst==true){
        //   params={
        //   zeroIndex:0
        //   }
        // }else{
        //   params={
        //     areaId:id
        //   }
        // }
        // params=this.isFirst?{zeroIndex:0}:{areaId:id};
        this.$ajax({
          method: "get",
          url: url,
          data:{
            areaId: id,
            areaCode: this.areaCode,
            type: this.showOrganization ? "0" : "1",
            minLevel: this.minLevel,
            // useToken: this.useToken ? "1" : "0",
          },
          serverName: this.serverName,
        })
          .then((response) => {
            if (response.data.code == 200) {
              // console.log(this.areaVal.deptName)

              //  this.isFirst=false;
              // let currentNode = response.data.data;
              // currentNode.forEach(item => {
              //   item.name = item.areaName;
              //   if (item.type == 1) {
              //     item.level = 'dq'
              //   } else if (item.type == 2) {
              //     item.level = 'bm'
              //   }
              // })
              // if (node.level === 0) {
              //   currentNode.forEach(item => {
              //     _this.treeExpandIdList.push(item.id);
              //     _this.nodeName = item.name;
              //     _this.valueID = item.id;
              //   });
              // }
              // if (node.level === 0) {
              //   // 默认展开第一级
              //   this.placeholder = "请选择";
              //   // return resolve([currentNode]);
              //   _this.$emit('change', currentNode[0]);
              //   return resolve(currentNode);
              // } else {
              //   // return resolve(currentNode.children);
              //   // console.log(currentNode+'222')
              //   return resolve(currentNode);
              // }
              if(url=='/area/toChild'){
                     // var currentNode = response.data.data[0];
              let currentNode = response.data.data;
              // console.log(JSON.stringify(currentNode))
              currentNode.forEach(item=>{
                // item.isLeaf=item.leaf;
                // if(item.type==1){
                //   item.type='dq'
                // }else if(item.type==2){
                //   item.type='bm'
                // }
                if(item.type==1){
                  item.level='dq'
                }else if(item.type==2){
                  item.level='bm'
                }
              })
              if (node.level === 0) {
                // 默认展开第一级
                this.placeholder = "请选择";
                // return resolve([currentNode]);
                return resolve(currentNode);
              } else {
                // return resolve(currentNode.children);
                // console.log(currentNode+'222')
                // console.log([currentNode]+'333')
                return resolve(currentNode);
              }
              }else{
              let currentNode = response.data.data[0];
              // currentNode.forEach(item=>{
              //   item.deptId=item.id;
              //   item.deptName=item.name;
              //   if(item.type==1){
              //     item.type='dq'
              //   }else if(item.type==2){
              //     item.type='bm'
              //   }
              // })
              // currentNode.forEach(item=>{
              //   if(item.type==1){
              //     item.type='dq'
              //   }else if(item.type==2){
              //     item.type='bm'
              //   }
              // })
              // if (node.level === 0 && currentNode && Array.isArray(currentNode) && currentNode[0].hasChild){
              if (this.areaVal == null || this.areaVal == undefined || this.areaVal.deptId == '' || this.areaVal.deptName == '') {
                currentNode.deptId=currentNode.id;
                currentNode.deptName=currentNode.name;
                // console.log(JSON.stringify(currentNode))
                                      _this.$emit('change', currentNode);
                                      this.nodeName=currentNode.name;
                                    } else {
                                      // this.nodeName = this.areaVal.deptName || this.areaVal.ssdw || '';
                                      this.nodeName = this.areaVal.deptName || '';
                                      _this.$emit('change', _this.areaVal);

                                    }
              // }
              if (node.level === 0) {
                // 默认展开第一级
                this.placeholder = "请选择";
                // this.nodeName=currentNode.name;
                return resolve([currentNode]);
                // return resolve(currentNode);
              } else {
                return resolve(currentNode.children);
                // return resolve(currentNode);
              }
              }
              // var currentNode = response.data.data[0];
              var currentNode = response.data.data;
              currentNode.forEach(item=>{
                if(item.type==1){
                  item.type='dq'
                }else if(item.type==2){
                  item.type='bm'
                }
              })
              if (node.level === 0) {
                // 默认展开第一级
                this.placeholder = "请选择";
                // return resolve([currentNode]);
                return resolve(currentNode);
              } else {
                // return resolve(currentNode.children);
                return resolve(currentNode);
              }
            } else {
              this.$message.error("获取地区信息失败！");
            }
          })
          .catch(() => {
            return resolve([])
            // this.$message.error("获取地区信息失败！");
          });
      }
    },
    // 节点点击
    handleNodeClick(node) {
      // 合并开始
      if (this.treeModule == "financiaAddApplydqs") { //如果是资金报账申请页面的，不是5类组织，不允许显示上去
        if (node.type == "bm") {
          if ((node.id != this.valueID) && this.payeeNum > 0) {
            this.$confirm(
              "变更申请单位后将清空收款方，确定继续？",
              "提示",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              }
            )
              .then(() => {
                this.nodeName = node[this.defaultProps.label];
                node.financiaAddApplyISchange = true;
                this.valueID = node.id;
                node.deptId = node.id;
                node.deptName = node.name;
                this.$emit('change', node);
                this.treeExpandIdList = [];
                // 点击后关闭下拉框，因为点击树形控件后select不会自动折叠
                this.$refs.ndSelect.$refs.select.blur();
              })
              .catch(() => {
                return false;
              });
          } else {
            this.nodeName = node[this.defaultProps.label];
            this.valueID = node.id;
            node.deptId = node.id;
            node.deptName = node.name;
            this.$emit('change', node);
            this.treeExpandIdList = [];
            // 点击后关闭下拉框，因为点击树形控件后select不会自动折叠
            this.$refs.ndSelect.$refs.select.blur();
          }
        } else {
          this.nodeName = '';
          node.financiaAddApplyISNotBM = true;
          node.deptId = node.id;
          node.deptName = node.name;
          this.$emit('change', node)
          this.treeExpandIdList = []
          // 点击后关闭下拉框，因为点击树形控件后select不会自动折叠
          this.$refs.ndSelect.$refs.select.blur()
        }
      } else {
        if (this.deptType == 'dw') {
          if (node.type == "bm") {
            this.nodeName = node[this.defaultProps.label];
            node.deptId = node.id;
            node.deptName = node.name;
            this.$emit('change', node)
            this.treeExpandIdList = []
            // 点击后关闭下拉框，因为点击树形控件后select不会自动折叠
            this.$refs.ndSelect.$refs.select.blur()
          } else {
            // this.$message.error("请选择单位！");
            this.$message.error(this.areaText);
            this.nodeName = '';
            this.treeExpandIdList = []
          }
        } else {
          this.nodeName = node[this.defaultProps.label];
          node.deptId = node.id;
          node.deptName = node.name;
          this.$emit('change', node)
          this.treeExpandIdList = []
          // 点击后关闭下拉框，因为点击树形控件后select不会自动折叠
          this.$refs.ndSelect.$refs.select.blur()
          if (this.showCheckbox) {
            // empty
          } else {
            this.firstWatchDeptId = false;
            // 所有节点都可以点击
            if (this.canSelectNodes.length === 0) {
              this.nodeName = node[this.defaultProps.label];
              if (this.showCheckbox) {
                // 三资（目前单选）读取节点的allpath
                if (this.isSz) {
                  this.$emit("nodeClick", [node.allpath]);
                } else {
                  this.$emit("nodeClick", [node.id]);
                }
              } else {
                if (this.isSz) {
                  node.isclick = true
                  this.$emit("nodeClick", node.allpath, node.allpath, node);
                } else {
                  this.$emit("nodeClick", node.id, node.level);
                  this.$emit("getPlace", node);
                }
              }
              this.$refs.ndSelect.$refs.select.blur();
            }
            // 部分节点可以点击
            if (this.canSelectNodes.length > 0) {
              if (this.canSelectNodes.indexOf(node.level) === -1) {
                if (this.showOrganization) {
                  this.nodeName = '';;
                  node.deptId = '';
                  node.deptName = '';
                this.$emit('change', {})
                  this.$message({
                    type: "warning",
                    message: this.organizationNotClickText,
                  });
                } else {
                  this.$message({
                    type: "warning",
                    message: this.areaNotClickText,
                  });
                }
              } else {
                this.nodeName = node[this.defaultProps.label];
                if (this.showCheckbox) {
                  this.$emit("nodeClick", [node.id]);
                } else {
                  this.$emit("nodeClick", node.id, node.level);
                  this.$emit("getPlace", node);
                }
                this.$refs.ndSelect.$refs.select.blur();
              }
            }
          }
        }


      }
      // 合并结束
    },
    // 【多选】获取选中的值
    checkHandler(nodeObj, selectedObj) {
      // console.log(selectedObj);
      let selectValue = [];
      selectValue = selectedObj.checkedNodes;
      let areaArry = [];
      selectedObj.checkedNodes.forEach((item) => {
        areaArry.push(item.name);
      });
      if (areaArry.length !== 0) {
        this.nodeName = areaArry.splice(0, 4).join(",") + "...";
      } else {
        this.nodeName = "";
      }
      this.$emit("nodeClick", selectValue);
    },
    // 组织树展开式触发
    expand(data, node, self) {
      let emitData = {
        data,
        node,
        self
      }
      this.$emit('node-expand', emitData)
    },
  },
};
</script>
<style>
.el-scrollbar__thumb {
  background: #D4E6FB;
}

/* .el-scrollbar__bar.is-vertical {
  background-color: #f2f7ff !important;
  ;
} */

/* .el-select-dropdown__wrap{
  max-height: 200px!important;
  overflow: none!important;
  overflow-y: scroll;
} */
</style>
<style scoped>
*::-webkit-scrollbar {
  width: 10px;
  height: 10px !important;
  width: 6px;
  height: 12px !important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}

.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 230px;
  /* padding: 0; */
  padding: 0 10px;
  overflow: hidden;
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

ul li>>>.el-tree .el-tree-node__content:hover {
  background-color: #e8f7ff;
}

ul li>>>.el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
  background-color: #ffffff;
}

.el-tree-node__label {
  font-weight: normal;
  margin-right: 10px;
  font-size: 12px;
}

.el-tree .is-current .el-tree-node__label {
  color: #409eff;
  font-weight: 700;
}

.el-tree .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}

.el-select-dropdown__item.hover {
  background: transparent;
}

.el-select-dropdown__item span {
  margin-left: 10px;
}

::v-deep .el-scrollbar__bar.is-horizontal {
  height: 0px;
}

::v-deep .el-scrollbar__bar.is-vertical {
  width: 0px;
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #c8ecff;
}

.el-select-dropdown__item span {
  margin-left: 0px;
}

.el-scrollbar .el-scrollbar__view .text-hover:hover {
  background: #E8F7FF;
}

/* ::v-deep .el-scrollbar .el-scrollbar__bar.is-vertical .el-scrollbar__thumb {
  background: #D4E6FB !important;
} */
::v-deep .is-vertical .el-scrollbar__thumb {
  background: #D4E6FB !important;
}

::v-deep .el-scrollbar__bar.is-vertical {
  background-color: #f2f7ff !important;
}
</style>
