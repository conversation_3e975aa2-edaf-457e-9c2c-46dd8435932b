<template>
  <div class="column-detail-view">
    <ndColumnDetailPage
      :key="$route.query.id"
      :id="$route.query.id"
      :breadcrumbName="$route.query.breadcrumbName || '详情'"
      :fromPage="$route.query.fromPage || ''"
    />
  </div>
</template>

<script>
import ndColumnDetailPage from '@/components/business/ndColumnDetailPage.vue';

export default {
  name: 'columnDetailView',
  components: {
    ndColumnDetailPage,
  }
};
</script>

<style lang="scss" scoped>
.column-detail-view {
  width: 100%;
  height: 100%;
}
</style>
