<template>
  <div class="nd-dropdown-box">
    <el-dropdown split-button v-bind="$attrs" v-on="$listeners">
      {{ nameData }}
      <el-dropdown-menu slot="dropdown">
        <slot />
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>

export default {
  components: {

  },
  props:{
    nameData: {
      type: String,
      default:"",
    },
  },
  data() {
    return {
      // visible: false,
    };
  },
  methods: {
  }
};
</script>

<style scoped lang="scss">
.nd-dropdown-box {
  width: auto;
  height: auto;

  ::v-deep .el-button-group>.el-button:first-child {
    color: #0098ff;
    background-color: #ffffff;
    border: 1px solid #0098ff;
    border-right: 0px;
    padding: 7px 6px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: #ffffff;
      border-color: #0075c5;
      background-color: #0075c5;
    }
  }



  ::v-deep .el-button-group>.el-button:not(:last-child) {
    margin-right: -1px;
  }

  ::v-deep .el-button-group>.el-button:last-child {
    color: #0098ff;
    background-color: #ffffff;
    border: 1px solid #0098ff;
    border-left: 0px;
    padding: 7px 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      color: #ffffff;
      border-color: #0075c5;
      background-color: #0075c5;
    }
  }

  ::v-deep .el-dropdown .el-dropdown__caret-button.el-button--default::before {
    background-color: #0098ff;
  }

  ::v-deep .el-dropdown .el-button-group {
    display: flex;
  }
}
</style>