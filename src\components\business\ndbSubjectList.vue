<template>
  <div class="ndb-select-organize-tree-box">
    <nd-select
      id="subTreeSelect"
      ref="ndSelect"
      :value="nodeName"
      value-key="kmName"
      :filterable="true"
      :remote="true" 
      :remote-method="remoteMethod"
      :placeholder="placeholder ||'请输入科目代码或名称'"
      :width="width"
      @focus="focus"
      @change="changeVal" 
      @blur="blur"
    >
      <el-option 
        v-for="(item,index) in options"
        :key="index"
        :label="item.kmName"
        :value="item"
      />
    </nd-select>
  </div>
</template>
<script>
import ndSelect from "../../components/ndSelect.vue";
// import ndSelect from "@/components/ndSelect.vue";
export default {
  components: {
    ndSelect,
  },
  model: {
    prop: 'subjectVal',
    event: "change",
  },
  props: {
    subjectVal: {
      // type: String | Array,
      type: [Number, String, Object],
      default: null,
    },
    width: {
      type: String,
      default: "220px",
    }
  },
  data() {
    return {
      placeholder: "",
      nodeName:'',
      options:[
        
      ],
      list:[
        
      ],
      kmName:'',
      kmCode:''
    };
  },
  watch: {
    'subjectVal.kmName': {
      immediate: true,
      deep: true,
      handler(value) {
        if (value != '' && value != undefined) {
          this.nodeName = value;
        }
      }
    },
  },
  mounted() {
    
  },
  methods: {//不行就用input代替
    focus(){//获得焦点时触发
      this.nodeName='';
      this.kmCode='';
      this.options = [];//新加
      this.$emit('funcKm','' );
    },
    blur(event){//失去焦点取用户输入值
      let value = event.target.value; 
        if(value&&(this.kmCode==''||this.kmCode==null||this.kmCode==undefined)) { 
            this.nodeName = value
            let subObj={
              kmName:value,
              kmCode:value
            }
            this.$emit('change', subObj);
            this.$emit('funcKm', value);
        }
    },
    changeVal(e){//取用户下拉选取值
         this.nodeName=e.kmName;
         this.kmCode=e.kmCode;
         let subObj={
          kmName:e.kmName,
          kmCode:e.kmCode
         }
         this.$emit('change', subObj);
         this.$emit('funcKm', this.kmCode);
    }, 
    remoteMethod(query) {
        if (query !== '') {
          this.kmName=query;
          this.treeReload();
        } else{
          this.options = [];
        }
      },
    treeReload(){
      return new Promise((resolve) => {
        let params = {
        kmCodeOrName:this.kmName
      };
        this.$ajax({
          method: "post",
          url: "" + "/apiPzList/listKmCode.do", 
          data: params,
        })
          .then((response) => {
            this.options=response.data.data.list
            resolve(true);
          })
          .catch(() => {
            resolve(true);
          });
      });
    }
    
  },
};
</script>
<style scoped>
*::-webkit-scrollbar {
  width: 10px;
  height: 10px !important;
  width: 6px;
  height: 12px !important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}

.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 230px;
  /* padding: 0; */
  padding: 0 15px;
  overflow: hidden;
  overflow-y: auto;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

ul li>>>.el-tree .el-tree-node__content:hover {
  background-color: #e8f7ff;
}

ul li>>>.el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
  background-color: #ffffff;
}

.el-tree-node__label {
  font-weight: normal;
  margin-right: 10px;
  font-size: 12px;
}

.el-tree .is-current .el-tree-node__label {
  color: #409eff;
  font-weight: 700;
}

.el-tree .is-current .el-tree-node__children .el-tree-node__label {
  color: #606266;
  font-weight: normal;
}

.el-select-dropdown__item.hover {
  background: transparent;
}

.el-select-dropdown__item span {
  margin-left: 10px;
}

::v-deep .el-scrollbar__bar.is-horizontal {
  height: 0px;
}

::v-deep .el-scrollbar__bar.is-vertical {
  width: 0px;
}

::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #c8ecff;
}

.el-select-dropdown__item span {
  margin-left: 0px;
}

</style>
