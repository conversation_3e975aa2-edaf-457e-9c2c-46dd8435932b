<template>
  <div class="breadcrumbs">
    <div class="icon"><i class="el-icon-location" /></div>
    <div>当前位置&nbsp;:&nbsp;&nbsp;</div>
    <div v-for="(item, index) in titles" :key="index" @click="goback(index)">
      <span :style="index == titles.length - 1 ? 'color:#10AEC2;' : 'cursor:pointer'">{{
        item
      }}</span>
      <span v-if="index != titles.length - 1">&nbsp;>&nbsp;</span>
    </div>
  </div>
</template>

<script>
// 获取url参数
const getQueryVariable = (variable) => {
  let geturl = window.location.href;
  let getqyinfo = geturl.split('?')[1];
  let getqys = new URLSearchParams('?' + getqyinfo);
  return getqys.get(variable);
};

export default {
  props: {
    titles: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    goback(index) {
      if (!index) {
        // let path = JSON.parse(getQueryVariable('preInfo')).path;
        // this.$router.push(path);
        this.$router.back();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.breadcrumbs {
  // width: 1300px;
  height: 46px;
  display: flex;
  align-items: center;
  margin: 0 auto;
  background: #f7f7f7;
  padding-left: 20px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 40px;

  .icon {
    color: #10aec2;
    margin-right: 8px;
  }
}
</style>
