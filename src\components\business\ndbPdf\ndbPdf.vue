<template>
  <div class="ndb-pdf-box" :style="{ width: width, height: height }">
    <ndb-pdf-chrome v-if="isChrome" ref="pdfChrome" :url="url" />
    <ndb-pdf-legacy v-if="!isChrome" ref="pdfLegacy" :url="url" />
  </div>
</template>
<script>
import ndbPdfChrome from "./ndbPdfChrome.vue";
import ndbPdfLegacy from "./ndbPdfLegacy.vue";
export default {
  components: {
    ndbPdfChrome,
    ndbPdfLegacy
  },
  props: {
    width: {
      type: String,
      default: "auto",
    },
    height: {
      type: String,
      default: "auto",
    },
    url: {
      type: String,
      default: "",
    }
  },
  data() {
    return {
      isChrome: true,
    };
  },
  mounted() {
    var userAgent = navigator.userAgent;
    if (userAgent.indexOf('Safari') > -1) {
      this.isChrome = true;
    } else {
      this.isChrome = false;
    }
  },
  methods: {

  }
};
</script>
<style lang="scss" scoped>
.ndb-pdf-box {
  width: auto;
  height: auto;
}
</style>