<template>
  <div class="tree-cont">
    <div
      v-for="(item, index) in menu"
      :key="index"
      class="val-type"
      :class="[form.tradeCategory == item.proTypeId ? 'active-type' : 'deactive-type']"
      @click="handleActive2(item)"
    >
      {{ item.proTypeName }}
      <menu-tree v-if="item.children" :menu="item.children"></menu-tree>
    </div>
  </div>
</template>
   
  <script>
export default {
  name: 'menuTree',
  props: ['menu'],
  // 注意：组件必须明确声明它是递归组件
  recursive: true,

  data() {
    return {
      form: { tradeCategory: '' },
    };
  },
  methods: {
    handleActive2(item) {
      this.form.tradeCategory = item.proTypeId;
    },
  },
};
</script>

<style lang="scss" scoped>
.tree-cont {
  display: flex;
  align-items: center;
  flex-direction: row;
  flex-wrap: wrap;
  column-gap: 12px;
  width: 100%;

  .val-type {
    height: 28px;
    border-radius: 2px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    line-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px 10px;
  }

  .active-type {
    background: #10aec2;
    color: #ffffff;
  }

  .deactive-type {
    background: #fff;
    color: #333333;
  }
}
</style>