<template>
  <div class="main">
    <div class="box">
      <!-- 标题 -->
      <div class="box-title">
        <div class="box-title-left">
          <span class="box-title-text">成交公告</span>
          <el-select v-model="value2" class="m-2" placeholder="交易品种" style="margin-left: 100px;">
            <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="box-title-right">
          <span>MORE</span>
          <img class="address-img" src="@/assets/projectImg/right2.png" alt="">
        </div>
      </div>
      <!-- 标题 -->

      <!-- 内容 -->
      <div class="content-box">
        <div class="content-box-item" v-for="(item, index) in 8" :key="index">
          <img class="demo-img" src="@/assets/projectImg/demo.png" alt="">
          <div class="content-box-bottom">
            <span class="title">南宁市经开区友谊路西二里19号中 房碧翠园</span>
            <div>成交价格：<span>910.00</span> 元/年/亩</div>
            <div>成交时间：2022-10-10 15:30:00</div>
          </div>
          <img class="status-img" src="@/assets/projectImg/status1.png" alt="">
        </div>
      </div>
      <!-- 内容 -->
    </div>
  </div>

</template>

<script>
export default {
    
  }
// const value1 = ref('')
// const options1 = [
//   {
//     value: 'Option1',
//     label: 'Option1',
//   },
// ]
// const value2 = ref('')
// const options2 = [
//   {
//     value: 'Option1',
//     label: 'Option1',
//   },
// ]
// let list = ref([])

// 获取所有地区
const getData = () => {
  $axios({
    url: "/jygg/areaTree.do",
    method: "get",
  }).then((res) => {
    list.value = res.data.data;
  });
}

</script>

<style lang="scss" scoped>
$width: 1300px;

.main {
  width: 1300px;
  background: #F7F7F7;
  margin: 0 auto;
  // overflow: hidden;
  padding-bottom: 22px;

  // .el-select .el-input.is-focus .el-input__wrapper
  :deep(.el-input__wrapper) {
    box-shadow: none;
  }

  :deep(.el-select .el-input.is-focus .el-input__wrapper) {
    box-shadow: none;
  }

  .box {
    display: flex;
    flex-direction: column;

    .box-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-content: center;
      align-items: center;
      padding: 30px 0px 15px 0px;

      .box-title-text {
        color: #333333;
        font-size: 36px;
        font-weight: bold;
      }

      .box-title-left {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-content: center;
        align-items: center;
      }

      .box-title-right {
        width: 78px;
        height: 38px;
        line-height: 38px;
        text-align: center;
        border: 1px solid #CCCCCC;
        font-size: 14px;
        color: #999999;
        cursor: pointer;
        position: relative;
        margin-right: 20px;

        .address-img {
          width: 32px;
          height: 7px;
          position: absolute;
          top: 13px;
          right: -22px;
          z-index: 99999;

        }
      }
    }

    .content-box {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;

      .content-box-item {
        width: 24%;
        height: auto;
        background: #fff;
        padding-bottom: 5px;
        cursor: pointer;
        position: relative;
        margin-top: 15px;

        .status-img {
          width: 60px;
          height: 33px;
          position: absolute;
          top: 10px;
          right: -5px;
        }

        .demo-img {
          width: 100%;
          height: 210px;
        }

        .content-box-bottom {
          padding: 10px 10px;

          .title {
            font-size: 18px;
            color: #333333;
            font-weight: bold;
          }

          div {
            font-size: 14px;
            color: #666666;
            margin-top: 10px;

            span {
              font-size: 18px;
              color: #0084FF;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
}</style>
