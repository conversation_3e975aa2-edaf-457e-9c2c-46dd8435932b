<template>
  <div class="details-box">
    <div class="details-top">
      <div class="details-top-center">
        <breadcrumbs />
        <topBox :datas="datas" />
      </div>
    </div>
    <mainBox :datas="datas" ref="mainBox"/>
  </div>
</template>

<script>
import breadcrumbs from '@/views/detailsView/components/breadcrumbs.vue';
import topBox from './components/topBox.vue';
import mainBox from './components/mainBox.vue';
export default {
  components: {
    breadcrumbs,
    topBox,
    mainBox,
  },
  data() {
    return {
      datas: {},
      title: '--',
      id: '',
    };
  },
  watch: {
    $route: {
      immediate: true,
      deep: true,
      handler() {
        this.getData(this.$route.query.id);
      },
    },
  },
  mounted() {

  },
  methods: {
    getData(id) {
      let data = {
        projectId: id,
      };
      this.$ajax({
        url: '/notice/endAnnDetail',
        method: 'get',
        data,
      }).then((res) => {
        // console.log('resssss', res);
        if (res.data.code === 200) {
          this.datas = res.data.data;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.details-box {
  width: 100%;
  background: #f7f7f7;
  border-top: 1px solid #cbcbcb;

  .details-top {
    width: 100%;
    background-color: #fff;
    margin-bottom: 30px;

    .details-top-center {
      width: 1200px;
      margin: auto;
      padding-bottom: 30px;
    }
  }
}
</style>