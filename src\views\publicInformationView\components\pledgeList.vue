<!-- 政策法规列表 -->
<template>
    <div class="wrap-container">
      <!-- <div style="position: sticky; top: 0; z-index: 9">
        <tabs v-model="currentTab" :tab-names="tabNames" />
      </div> -->
      <listTypeB v-show="currentTab == '1'" requestUrl="/zcfg/gjzc/" :breadcrumbName="'登记公告'"></listTypeB>  
    </div>
  </template>
  <script>
  import listTypeB from '@/views/newsListView/components/listTypeB.vue';
  export default {
    props: {
      currentTab: {
      type: String,
      default: '1',
    },
  },
    components: {
      listTypeB,
    },
    data() {
      return {
        tabNames: [
          { name: '国家政策' },
          // { name: '地方政策' },
          // { name: '交易规则' },
          // { name: '交易指南' },
        ],
      };
    },
    methods: {},
  };
  </script>
  
  <style lang="scss" scoped>
  .wrap-container {
    min-width: 1000px;
    width: 100%;
    overflow: auto;
    height: 100%;
    background: #fff;
    position: relative;
    padding-bottom: 20px;
  }
  </style>
  