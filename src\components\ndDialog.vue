<template>
  <el-dialog
    v-el-dialog-drag
    v-if="dialogVisible"
    v-loading="loading"
    :visible.sync="dialogVisible"
    class="nd-dialog-box"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    v-bind="$attrs"
    :class="center ? 'nd-dialog-center-box' : ''"
    :top="center ? '0vh' : top"
    :width="width"
    v-on="$listeners"
  >
    <div slot="title" class="dialog-title-box">
      <div class="dialog-title">
        <div>{{ title }}</div>
      </div>
    </div>
    <div class="dialog-content-box">
      <div class="dialog-content" :style="{ height: height }">
        <slot></slot>
      </div>
    </div>
    <div v-if="showFooter" slot="footer" class="dialog-footer-box">
      <div class="dialog-footer">
        <slot name="footer"></slot>
      </div>
    </div>
    <div v-if="!showFooter" class="dialog-no-footer-box"></div>
  </el-dialog>
</template>
<script>
import elDialogDrag from "@/components/utils/el-dialog-drag";
export default {
  directives: {
    elDialogDrag
  },
  props: {
    title: {
      type: String,
      default: "未命名",
    },
    center: {
      type: Boolean,
      default: false,
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
    top: {
      type: String,
      default: "15vh",
    },
    width: {
      type: String,
      default: "50%",
    },
    height: {
      type: String,
      default: "auto",
    },
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
    };
  },
  mounted() {},
  methods: {
    // 打开
    open() {
      this.dialogVisible = true;
    },
    // 关闭
    close() {
      this.dialogVisible = false;
    },
    // 显示等待
    showLoading() {
      this.loading = true;
    },
    // 隐藏等待
    hideLoading() {
      this.loading = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.nd-dialog-center-box {
  display: flex;
  align-items: center;
  ::v-deep .el-dialog {
    margin: 0 auto;
  }
}
.nd-dialog-box {
  width: auto;
  height: auto;
  background-color: transparent;
  ::v-deep .el-dialog {
    background-color: transparent;
  }
  ::v-deep .el-dialog__header {
    padding: 0px;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
  }
  ::v-deep .el-dialog__body {
    padding: 0px;
  }
  ::v-deep .el-dialog__footer {
    padding: 0px;
  }
  ::v-deep .el-dialog__close {
    color: #ffffff;
  }
  // ::v-deep .el-dialog__headerbtn {
  //   top: 10px;
  // }
  ::v-deep .el-dialog__headerbtn {
    top: 5px;
    right: 10px;
    .el-dialog__close{
      font-size: 26px;
    }
  }
  .dialog-title-box {
    width: 100%;
    height: 35px;
    background-color: #f6faff;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    padding-left: 1px;
    padding-right: 1px;
    padding-top: 1px;
    .dialog-title {
      width: 100%;
      height: 100%;
      border-top-right-radius: 5px;
      border-top-left-radius: 5px;
      background-color: #0098ff;
      padding-left: 14px;
      font-size: 14px;
      color: #ffffff;
      display: flex;
      flex-direction: row;
      align-items: center;
    }
  }
  .dialog-content-box {
    width: auto;
    height: auto;
    background-color: #f6faff;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 10px;
    .dialog-content {
      width: auto;
      overflow-y: auto;
      border: 1px solid #e6e6e6;
      background-color: #ffffff;
      font-size: 12px;
    }
  }
  .dialog-footer-box {
    width: 100%;
    height: 60px;
    background-color: #f6faff;
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 10px;
    .dialog-footer {
      width: 100%;
      height: 100%;
      border-left: 1px solid #e6e6e6;
      border-right: 1px solid #e6e6e6;
      border-bottom: 1px solid #e6e6e6;
      background-color: #f6faff;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      font-family: "Microsoft YaHei";
      font-size: 12px;
    }
  }
  .dialog-no-footer-box {
    width: 100%;
    height: 10px;
    background-color: #f6faff;
  }
}
</style>
