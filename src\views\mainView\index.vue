<template>
  <div class="main">
    <!-- <div class="fix-cont">
      <div class="fix-list" v-for="(item, index) in sideImgArr" :key="index">
        <div class="fix-item" @click="handleActive(index)">
          <div class="fix-img">
            <img v-if="activeIndex !== index" :src="item.url" alt="" />
            <img v-if="activeIndex === index" :src="item.activeUrl" alt="" />
          </div>
          <div class="fix-title" :class="[activeIndex === index ? 'activeTitle' : '']">
            {{ item.title }}
          </div>
          <div v-if="index !== sideImgArr.length - 1" class="gap-line"></div>
        </div>
      </div>
    </div> -->
    <headers ref="headers"  @detailTopHeight="getDetailTopHeight" />
    <!-- <component :is="components" /> -->
    <keep-alive >
      <router-view @getDealInformation="getDealInformation"   :detailPaddingTop="detailPaddingTop"></router-view>
    </keep-alive>
    <footers />
  </div>
</template>

<script>
import headers from './conmponents/header.vue';
import footers from './conmponents/footer.vue';

import newsListView from '../newsListView/index.vue';
export default {
  components: {
    headers,
    footers,
    newsListView,
  },
  data() {
    return {
      isMain: false,
      components: newsListView,
      sideImgArr: [
        {
          url: require('@/assets/detail/zzgl1.png'),
          activeUrl: require('@/assets/detail/zzgl2.png'),
          title: '组织管理',
        },
        {
          url: require('@/assets/detail/cygl1.png'),
          activeUrl: require('@/assets/detail/cygl2.png'),
          title: '成员管理',
        },
        {
          url: require('@/assets/detail/gqgl1.png'),
          activeUrl: require('@/assets/detail/gqgl2.png'),
          title: '股权管理',
        },
        {
          url: require('@/assets/detail/zcgl1.png'),
          activeUrl: require('@/assets/detail/zcgl2.png'),
          title: '资产管理',
        },
        {
          url: require('@/assets/detail/cwgl1.png'),
          activeUrl: require('@/assets/detail/cwgl2.png'),
          title: '财务管理',
        },
        {
          url: require('@/assets/detail/jygl1.png'),
          activeUrl: require('@/assets/detail/jygl2.png'),
          title: '交易管理',
        },
      ],
      activeIndex: '',
      detailPaddingTop: '',
    };
  },

  methods: {
    handleActive(index) {
      this.activeIndex = index;
    },
    getDealInformation(val) {
      console.log(val, '2');
      this.isMain = val
      this.$refs.headers.getDealInformationAdd(val)
    },
    /**
     * 获取详情页顶部的padding间距
     * @param {string} e 顶部距离
     */
     getDetailTopHeight(e) {
      this.detailPaddingTop = e;
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/vue-quill.snow.css';

.main {

  // width: 100%;
  .fix-cont {
    position: fixed;
    // top: 320px;
    top: 21vh;
    right: 0px;
    width: 67px;
    // height: 462px;
    padding-top: 14px;
    box-sizing: border-box;
    z-index: 999;
    background: #ffffff;
    box-shadow: 0px 0px 8px 0px rgba(3, 3, 3, 0.2);
    border-radius: 5px;
    display: none;

    .fix-list {
      display: flex;
      flex-direction: column;
      align-items: center;

      .fix-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;

        // .fix-img {
        //   margin-bottom: 3px;
        // }

        .fix-title {
          display: flex;
          justify-content: center;
          // height: 11px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #535353;
          // line-height: 22px;
          margin-bottom: 15px;
        }

        .activeTitle {
          display: flex;
          justify-content: center;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #10aec2;
          margin-bottom: 15px;
        }

        .gap-line {
          width: 54px;
          height: 1px;
          background: #e7e7e7;
          margin-bottom: 14px;
        }

        .hidden-line {}
      }
    }
  }
}
</style>
