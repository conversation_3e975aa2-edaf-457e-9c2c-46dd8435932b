import Vue from 'vue';
import Element<PERSON> from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import App from './App.vue'
import router from './router'
import axios from '@/http/index';
import store from "@/store";

Vue.config.productionTip = false;
Vue.prototype.$ajax = axios;

Vue.use(ElementUI, { zIndex: 9500 });

// 导航守卫
router.beforeEach((to, from, next) => {
  next();
  // if (to.path === '/depreciationView') {
  //   axios({
  //     url: "/api_zhangt/zhangt_check.do",
  //     method: "post",
  //   })
  //     .then((res) => {
  //       if (1 == res.data.code) {
  //         //未选中账套
  //         if (typeof chioceBooks === "function") {
  //           //vueList.jsp中的方法
  //           chioceBooks()
  //         }
  //       } else if(0 == res.data.code){
  //         //已选中账套
  //         next();
  //       }
  //     })
  // } else {
  //   next();
  // }
  // document.title = '农村集体经济组织和成员管理平台'
  // let title = `${to.meta.title}`;
  // if (title !== 'undefined') {
  //   document.title = title + " | " + document.title;
  // }

  // const role = localStorage.getItem('ms_username');
  // if (!role && to.path !== '/login') {
  //   next('/login');
  // } else if (to.meta.permission) {
  //   // 如果是管理员权限则可进入，这里只是简单的模拟管理员权限而已
  //   role === 'admin' ? next() : next('/403');
  // } else {
  //   // 简单的判断IE10及以下不进入富文本编辑器，该组件不兼容
  //   if (navigator.userAgent.indexOf('MSIE') > -1 && to.path === '/editor') {
  //     Vue.prototype.$alert('vue-quill-editor组件不兼容IE10及以下浏览器，请使用更高版本的浏览器查看', '浏览器不兼容通知', {
  //       confirmButtonText: '确定'
  //     });
  //   } else {
  //     next();
  //   }
  // }
});

var vueObject = new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App),
  beforeCreate() {
    Vue.prototype.$bus = this;
  }
});

window.vueObject = vueObject;