<!-- 项目推荐 -->
<template>
  <div class="wrap-container">
    <list requestUrl="/xmtj/"></list>
  </div>
</template>
<script>
import list from '@/views/newsListView/components/list.vue';
export default {
  components: {
    list,
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.wrap-container {
  border-top: 1px solid #cbcbcb;
  min-width: 1000px;
  width: 100%;
  overflow: auto;
  height: 100%;
  background: #fff;
  position: relative;
  padding-bottom: 20px;
}
</style>
