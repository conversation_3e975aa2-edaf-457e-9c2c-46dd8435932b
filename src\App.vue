<template>
  <div id="app">
    <!-- <div style="width: 1300px; height: 30px; background-color: red"></div> -->
    <router-view />
    <!-- <HomeView></HomeView> -->
  </div>
</template>

<script>
// import HomeView from "./viewParts/chioceBooksView/index.vue";
// import HomeView from "./views/voucherAddView/index.vue"

export default {
  // components: {
  //   HomeView,
  // },
};
</script>

<style lang="scss">
#app {
  // width: 100%;
  min-width: 1300px;
  min-height: 100%;
  background: #fff;
  // overflow: auto;
  // height: auto;
}
*::-webkit-scrollbar {
  width: 10px;
  height: 10px !important;
  width: 6px;
  height: 12px !important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 12px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}

.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  height: auto;
  max-height: 230px;
  /* padding: 0; */
  padding: 0 10px;
  overflow: hidden;
  overflow-y: auto;
}
</style>
