import Vue from 'vue';
import VueRouter from 'vue-router';

Vue.use(VueRouter);

// // 解决vue路由重复导航错误
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};
const originalReplace = VueRouter.prototype.replace;
VueRouter.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch((err) => err);
};

const routes = [
  {
    // 测试
    path: '/testView',
    name: 'testView',
    component: () => import(/* webpackChunkName: "testView" */ '../views/testView/index.vue'),
  },
  {
    // 主路由
    path: '/',
    component: () => import(/* webpackChunkName: "testView" */ '../views/mainView/index.vue'),
    children: [
      {
        path: '/',
        meta: { pageName: '首页' },
        component: () =>
          import(/* webpackChunkName: "newsListView" */ '../views/indexView/index.vue'),
      },
      {
        // 详情页
        path: 'details',
        name: 'details',
        component: () =>
          import(/* webpackChunkName: "detailsView" */ '../views/detailsView/index.vue'),
      },
      {
        // 详情页
        path: 'endDetail',
        name: 'endDetail',
        component: () =>
          import(/* webpackChunkName: "detailsView" */ '../views/endDetailsView/index.vue'),
      },
      {
        // 新闻列表
        path: 'newsListView',
        name: 'newsListView',
        meta: { pageName: '新闻中心' },
        component: () =>
          import(/* webpackChunkName: "newsListView" */ '../views/newsListView/index.vue'),
      },
      {
        // 政策法规列表
        path: 'regulationsListView',
        name: 'regulationsListView',
        meta: { pageName: '政策法规' },
        component: () =>
          import(
            /* webpackChunkName: "regulationsListView" */ '../views/regulationsListView/index.vue'
          ),
      },
      {
        // 交易资讯列表
        path: 'projectRecommendations',
        name: 'projectRecommendations',
        meta: { pageName: '交易资讯' },
        component: () =>
          import(
            /* webpackChunkName: "projectRecommendations" */ '../views/projectRecommendations/index.vue'
          ),
      },
      {
        // 交易须知列表
        path: 'helpCenter',
        name: 'helpCenter',
        meta: { pageName: '交易须知' },
        component: () =>
          import(/* webpackChunkName: "helpCenter" */ '../views/helpCenter/index.vue'),
      },
      {
        // 列表通用详情
        path: 'listDetailView',
        name: 'listDetailView',
        component: () =>
          import(/* webpackChunkName: "listDetailView" */ '../views/listDetailView/index.vue'),
      },
      {
        // 栏目详情页
        path: 'columnDetailView',
        name: 'columnDetailView',
        component: () =>
          import(/* webpackChunkName: "columnDetailView" */ '../views/columnDetailView/index.vue'),
      },
      {
        // 项目信息
        path: 'projectInformationView',
        name: 'projectInformationView',
        meta: { pageName: '项目信息' },
        component: () =>
          import(
            /* webpackChunkName: "onlineContractSigningView" */ '../views/projectInformationView/index.vue'
          ),
      },
      {
        // 公告信息
        path: 'transactionAnnouncementView',
        name: 'transactionAnnouncementView',
        component: () =>
          import(
            /* webpackChunkName: "onlineContractSigningView" */ '../views/transactionAnnouncementView/index.vue'
          ),
      },
      {
        // 信息公开
        path: 'publicInformationView',
        name: 'publicInformationView',
        meta: { pageName: '公示公告' },
        component: () =>
          import(
            '../views/publicInformationView/index.vue'
          ),
      },
      {
        path: 'newsCenter',
        name: 'newsCenter',
        meta: { pageName: '新闻中心' },
        component: () =>
          import(
            '../views/newsCenter/index.vue'
          ),
      },
      {
        path: 'interacteCommunication',
        name: 'interacteCommunication',
        meta: { pageName: '互动交流' },
        component: () =>
          import(
            '../views/interacteCommunication/index.vue'
          ),
      },
      {
        // 关于我们
        path: 'aboutUs',
        name: 'aboutUs',
        meta: { pageName: '关于我们' },
        component: () =>
          import(
            '../views/aboutUs/index.vue'
          ),
      },

      {
        path: 'businessRule',
        name: 'businessRule',
        meta: { pageName: '业务规则' },
        component: () =>
          import(
            '../views/businessRule/index.vue'
          ),
      },
      {
        path: 'lawsRegulation',
        name: 'lawsRegulation',
        meta: { pageName: '政策法规' },
        component: () =>
          import(
            '../views/lawsRegulation/index.vue'
          ),
      },
      {
        path: 'profileDownload',
        name: 'profileDownload',
        meta: { pageName: '资料下载' },
        component: () =>
          import(
            '../views/profileDownload/index.vue'
          ),
      },
      {
        path: 'dealInformationIndex',
        name: 'dealInformationIndex',
        meta: { pageName: '招商服务' },
        component: () =>
          import(
            '../views/indexView/dealInformationIndex.vue'
          ),
      },
      {
        path: 'supplyHallIndex',
        name: 'supplyHallIndex',
        meta: { pageName: '供需大厅' },
        component: () =>
          import(
            '../views/indexView/supplyHallIndex.vue'
          ),
      },
      {
        // 交易资讯列表
        path: 'supplyAndDemandHall',
        name: 'supplyAndDemandHall',
        meta: { pageName: '供需大厅' },
        component: () =>
          import(
            /* webpackChunkName: "projectRecommendations" */ '../views/supplyAndDemandHall/index.vue'
          ),
      },
    ],
  },
   // 中介入驻申请
   {
    // 交易资讯列表
    path: '/applicationIntermediarySettlementView',
    name: 'applicationIntermediarySettlementView',
    meta: { pageName: '中介入驻申请' },
    component: () =>
      import(
        /* webpackChunkName: "projectRecommendations" */ '../views/applicationIntermediarySettlementView/index.vue'
      ),
  },

  // {
  //   // 项目信息
  //   path: '/projectInformationView',
  //   name: 'projectInformationView',
  //   component: () => import(/* webpackChunkName: "onlineContractSigningView" */ '../views/projectInformationView/index.vue')
  // },
  // {
  //   // 新闻列表
  //   path: '/newsListView',
  //   name: 'newsListView',
  //   component: () => import(/* webpackChunkName: "newsListView" */ '../views/newsListView/index.vue')
  // },
  // {
  //   // 政策法规列表
  //   path: '/regulationsListView',
  //   name: 'regulationsListView',
  //   component: () => import(/* webpackChunkName: "regulationsListView" */ '../views/regulationsListView/index.vue')
  // },
  // {
  //   // 列表通用详情
  //   path: '/listDetailView',
  //   name: 'listDetailView',
  //   component: () => import(/* webpackChunkName: "listDetailView" */ '../views/listDetailView/index.vue')
  // },
];

const router = new VueRouter({
  // mode: 'history',
  base: process.env.BASE_URL,
  routes: [...routes],
});

// router.beforeEach((from, to, next) => {
//   console.log(from);
//   console.log(to);
//   next();
// })
export default router;



