<template>
  <div class="ndl-statistic-box">
    <div
      v-for="(statistic, index) in statistics"
      :key="index"
      class="ndl-statistic-item"
    >
      <span class="statistic-item-line" />
      <span class="statistic-item-content">{{ statistic.name }}</span>
      <span v-if="statistic.unit == '（元）'" class="statistic-item-value one">{{ statistic.value | numFilter }}</span>
      <span v-else class="statistic-item-value two">{{ getDecimalFull(statistic.value) }}</span>
      <span class="statistic-item-content">{{ statistic.unit }}</span>
    </div>
  </div>
</template>

<script>
export default {
   filters: {
        numFilter(value) {
            let realVal = "";
            if (!isNaN(value) && value !== "") {
                // 截取当前数据到小数点后两位,改变toFixed的值即可截取你想要的数值
                realVal = parseFloat(value).toFixed(2);
                realVal = realVal.replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, '$1,');
            } else {
                realVal = "--";
            }
            return realVal;
        },
    },
  props: {
    statisticsData: {
      type: Array,
    },
  },
  data() {
    return {
      statistics: [],
    };
  },
  watch: {
    statisticsData(newName, oldName) {
      this.statistics = newName;
    },
  },
  mounted() {
    this.getStatisticsData();
  },
  methods: {
    //获取数据
    getStatisticsData() {
      this.$nextTick(() => {
        this.statistics = this.statisticsData;
      });
    },
    // 获取面积的科学计数法字符串
    getDecimalFull(num){
      let realVal = '';
      let xs = '';
      let zs = num;
      let numArr = []
      if (!isNaN(num) && num !== "") {
        if(num.indexOf(".") > -1){
          numArr = num.split('.');
          zs = numArr[0]
          xs = numArr[1]
        }
        realVal = zs.replace(/(\d{1,3})(?=(\d{3})+(?:$|\.))/g, '$1,');
      } else {
          realVal = "--";
      }
      return (xs?(realVal+'.'+xs):realVal)
    },
  },
};
</script>

<style lang="scss" scoped>
.ndl-statistic-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 10px 0;
  .ndl-statistic-item {
    display: flex;
    align-items: center;
    margin-right: 28.5px;
    .statistic-item-content {
      font-size: 12px;
      color: #666666;
    }
    .statistic-item-value {
      font-size: 14px;
      color: #ff9100;
    }
    .statistic-item-line {
      width: 4px;
      height: 12px;
      background: #0098ff;
      border-radius: 2px;
      margin-right: 5px;
    }
  }
}
</style>