module.exports = {
  extends: [
    // add more generic rulesets here, such as:
    'eslint:recommended',
    // 'plugin:vue/vue3-recommended',
    'plugin:vue/recommended' // Use this if you are using Vue.js 2.x.
  ],
  rules: {
    'vue/max-attributes-per-line': ['error', {
      singleline: 20 // 标签超出20个属性就会换行
    }],
    "vue/multi-word-component-names": 'off', // 这个检查待定，虽然是A级，但不允许起一个单词的名字有点说不过去
    "no-undef": 'warn', // 这个检查待定，因为VUE和JSP融合
    // "vue/multiline-html-element-content-newline": 'off', 
  }
}