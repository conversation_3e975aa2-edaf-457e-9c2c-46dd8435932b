<template>
  <div class="app">
    <headerView ref="headerViewRef" :has-tab="false" />
    <div class="main">
      <div class="content">
        <div class="banner">会员入驻申请</div>
        <div class="from-box">
          <div class="title">
            <span> 机构信息 </span>
          </div>
          <el-form ref="formRef" :model="formData" :rules="rules">
            <div class="double">
              <div class="from-item">
                <div class="from-label">
                  机构名称
                  <span style="color: #d54941">*</span>
                </div>
                <div class="from-centent">
                  <el-form-item prop="orgName">
                    <el-input
                      v-model="formData.orgName"
                      maxlength="50"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
              </div>
              <div class="from-item">
                <div class="from-label">
                  统一社会信用代码
                  <span style="color: #d54941">*</span>
                </div>
                <div class="from-centent">
                  <el-form-item prop="orgCode">
                    <el-input v-model="formData.orgCode" placeholder="请输入" style="width: 100%" />
                  </el-form-item>
                </div>
              </div>
            </div>

            <div class="from-item">
              <div class="from-label">注册地址</div>
              <div class="from-centent">
                <el-form-item>
                  <el-input
                    v-model="formData.address"
                    maxlength="100"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </el-form-item>
              </div>
            </div>

            <div class="from-item">
              <div class="from-label">经营范围</div>
              <div class="from-centent">
                <el-form-item>
                  <el-input
                    v-model="formData.businessScope"
                    maxlength="100"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </el-form-item>
              </div>
            </div>

            <div class="double">
              <div class="from-item">
                <div class="from-label">
                  法人姓名
                  <span style="color: #d54941">*</span>
                </div>
                <div class="from-centent">
                  <el-form-item prop="legalName">
                    <el-input
                      v-model="formData.legalName"
                      maxlength="20"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
              </div>
              <div class="from-item">
                <div class="from-label">
                  法人身份证号
                  <span style="color: #d54941">*</span>
                </div>
                <div class="from-centent">
                  <el-form-item prop="legalIdNumber">
                    <el-input
                      v-model="formData.legalIdNumber"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
              </div>
            </div>

            <div class="double">
              <div class="from-item">
                <div class="from-label">
                  法人电话
                  <span style="color: #d54941">*</span>
                </div>
                <div class="from-centent">
                  <el-form-item prop="legalPhone">
                    <el-input
                      v-model="formData.legalPhone"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
              </div>
              <div class="from-item">
                <div class="from-label">
                  联系人
                  <span style="color: #d54941">*</span>
                </div>
                <div class="from-centent">
                  <el-form-item prop="contactPerson">
                    <el-input
                      v-model="formData.contactPerson"
                      maxlength="20"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
              </div>
            </div>

            <div class="double">
              <div class="from-item">
                <div class="from-label">
                  联系人电话
                  <span style="color: #d54941">*</span>
                </div>
                <div class="from-centent">
                  <el-form-item prop="contactPhone">
                    <el-input
                      v-model="formData.contactPhone"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
              </div>
              <div class="from-item">
                <div class="from-label">开户银行</div>
                <div class="from-centent">
                  <el-form-item>
                    <el-input
                      v-model="formData.bankId"
                      maxlength="50"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
              </div>
            </div>

            <div class="double">
              <div class="from-item">
                <div class="from-label">
                  开户卡号
                  <span style="color: #d54941">*</span>
                </div>
                <div class="from-centent">
                  <el-form-item prop="bankAccount">
                    <el-input
                      v-model="formData.bankAccount"
                      maxlength="50"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
              </div>
              <div class="from-item">
                <div class="from-label">
                  银行预留手机号
                  <span style="color: #d54941">*</span>
                </div>
                <div class="from-centent">
                  <el-form-item prop="legalPersonPhone">
                    <el-input
                      v-model="formData.legalPersonPhone"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
              </div>
            </div>

            <div class="from-item" style="margin-bottom: 30px">
              <div class="from-label">
                代理地区
                <span style="color: #d54941">*</span>
              </div>
              <div class="from-centent">
                <el-form-item prop="agencyArea">
                  <el-select v-model="nodeName" style="width: 100%">
                    <el-option :value="nodeName">
                      <el-tree
                        ref="treeRef"
                        show-checkbox
                        :load="getAreaTree"
                        lazy
                        node-key="id"
                        :props="props"
                        @check="checks"
                      />
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </div>

            <div class="title">
              <span> 附件信息 </span>
            </div>

            <div class="from-item">
              <div class="from-label">
                营业执照
                <span style="color: #d54941">*</span>
              </div>
              <div class="from-centent is-file">
                <el-form-item prop="yyzzList">
                  <div class="files-item-box">
                    <el-upload
                      class="avatar-uploader"
                      action="#"
                      :auto-upload="false"
                      :show-file-list="false"
                      :on-success="handleAvatarSuccess"
                      :on-change="onChangeYYZZ"
                    />
                    <div v-for="(item, index) in formData.yyzzList" :key="index" class="img-box">
                      <div class="del" @click="delFile(item, 'YYZZ')">x</div>
                      <img
                        class="img-item"
                        :src="item.fileUrl"
                        alt=""
                        v-if="item.fileSuffix !== 'pdf'"
                      />
                      <img
                        class="img-item"
                        src="@/assets/pdfDefault.png"
                        v-else
                        @click="viewPdf(item)"
                      />
                    </div>
                  </div>
                </el-form-item>
              </div>
            </div>

            <div class="from-item">
              <div class="from-label">
                银行开户证明
                <span style="color: #d54941">*</span>
              </div>
              <div class="from-centent is-file">
                <el-form-item prop="yhkhzmList">
                  <div class="files-item-box">
                    <el-upload
                      class="avatar-uploader"
                      action="#"
                      :auto-upload="false"
                      :show-file-list="false"
                      :on-success="handleAvatarSuccess"
                      :on-change="onChangeYHKHZM"
                    />
                    <div v-for="(item, index) in formData.yhkhzmList" :key="index" class="img-box">
                      <div class="del" @click="delFile(item, 'YHKHZM')">x</div>

                      <img
                        class="img-item"
                        :src="item.fileUrl"
                        alt=""
                        v-if="item.fileSuffix !== 'pdf'"
                      />
                      <img
                        class="img-item"
                        src="@/assets/pdfDefault.png"
                        v-else
                        @click="viewPdf(item)"
                      />
                    </div>
                  </div>
                </el-form-item>
              </div>
            </div>

            <div class="from-item" style="margin-bottom: 30px">
              <div class="from-label">其他附件</div>
              <div class="from-centent is-file">
                <el-form-item>
                  <div class="files-item-box">
                    <el-upload
                      class="avatar-uploader"
                      action="#"
                      :auto-upload="false"
                      :show-file-list="false"
                      :on-success="handleAvatarSuccess"
                      :on-change="onChangeQTFJ"
                    />
                    <div v-for="(item, index) in formData.qtList" :key="index" class="img-box">
                      <div class="del" @click="delFile(item, 'QTFJ')">x</div>

                      <img
                        class="img-item"
                        :src="item.fileUrl"
                        alt=""
                        v-if="item.fileSuffix !== 'pdf'"
                      />
                      <img
                        class="img-item"
                        src="@/assets/pdfDefault.png"
                        v-else
                        @click="viewPdf(item)"
                      />
                    </div>
                  </div>
                </el-form-item>
              </div>
            </div>

            <div class="button">
              <el-button type="primary" @click="submit">提交申请</el-button>
              <el-button type="primary" plain @click="reset">取消</el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <footerView />
  </div>
</template>

<script>
// 公共组件
import headerView from '@/views/mainView/conmponents/header.vue';
import footerView from '@/views/mainView/conmponents/footer.vue';

// 工具函数
import { createdTreeDataById, getIdByLevel, desabledById } from './utils/treeUtils';
import { encryptByAESarr } from './utils/encryptedData';
export default {
  components: {
    headerView,
    footerView,
  },
  data() {
    return {
      nodeName: '',
      formData: {
        id: '',
        orgName: '', // 机构名称
        orgCode: '', // 统一社会信用代码
        address: '', // 注册地址
        businessScope: '', //经营范围
        legalName: '', // 法人姓名
        legalIdNumber: '', // 法人身份证号
        legalPhone: '', // 法人电话
        contactPerson: '', // 联系人
        contactPhone: '', // 联系人电话
        bankId: '', // 开户银行
        bankAccount: '', // 开户卡号
        legalPersonPhone: '', // 银行预留手机号
        agencyArea: '', // 代理地区（逗号分隔）
        agencyAreaName: '', // 代理地区名称（逗号分隔）
        yyzzList: [], // 营业执照附件集合
        yhkhzmList: [], // 银行开户证明附件集合
        qtList: [], // 其他附件集合
        list: [],
      },
      rules: {
        orgName: [{ required: true, message: '请输入机构名称', trigger: 'blur' }],
        orgCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
          {
            pattern: '^[0-9A-Z]{18}$',
            message: '请输入正确格式的统一社会信用代码',
            trigger: 'blur',
          },
        ],
        legalName: [{ required: true, message: '请输入法人姓名', trigger: 'blur' }],
        legalIdNumber: [
          { required: true, message: '请输入法人身份证号', trigger: 'blur' },
          {
            pattern:
              '^[1-9]\\d{5}(18|19|20|21|22)?\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|[Xx])$',
            message: '请输入正确格式的身份证号',
            trigger: 'blur',
          },
        ],
        legalPhone: [
          { required: true, message: '请输入法人电话', trigger: 'blur' },
          {
            pattern: '^(1[3-9]\\d{9})$|^((0\\d{2,3}-?)?\\d{7,8})$',
            message: '请输入正确格式的手机号或座机号',
            trigger: 'blur',
          },
        ],
        contactPerson: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
        contactPhone: [
          { required: true, message: '请输入联系人电话', trigger: 'blur' },
          {
            pattern: '^(1[3-9]\\d{9})$|^((0\\d{2,3}-?)?\\d{7,8})$',
            message: '请输入正确格式的手机号或座机号',
            trigger: 'blur',
          },
        ],
        bankAccount: [{ required: true, message: '请输入开户卡号', trigger: 'blur' }],
        legalPersonPhone: [
          { required: true, message: '请输入银行预留手机号', trigger: 'blur' },
          {
            pattern: '^(1[3-9]\\d{9})$',
            message: '请输入正确格式的手机号',
            trigger: 'blur',
          },
        ],
        agencyArea: [{ required: true, message: '请选择代理地区', trigger: 'change' }],
        yyzzList: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
        yhkhzmList: [{ required: true, message: '请上传银行开户证明', trigger: 'change' }],
      },
      props: {
        id: 'id',
        level: 'jiBie',
        label: 'name',
        children: 'children',
        isLeaf: 'isLeaf',
        // disabled: disabledFn,
        disabled: 'disabled',
      },
      treeData: [],
    };
  },
  mounted() {
    this.getId();
  },
  methods: {
    handleAvatarSuccess() {},
    beforeAvatarUploadYYZZ() {},

    // 校验文件类型
    tileType(e) {
      console.log(e);

      switch (e) {
        case 'image/jpeg':
          return true;
        case 'image/png':
          return true;
        case 'image/bmp':
          return true;
        case 'application/pdf':
          return true;
      }
      return false;
    },
    // 上传附件
    onChangeYYZZ(e) {
      console.log(e);
      if (!this.tileType(e.raw.type)) {
        return this.$message.error('请上传jpg、jpeg、png、bmp、pdf格式的文件');
      }
      let fileForm = new FormData();
      fileForm.append('files', e.raw);

      this.$ajax({
        url: `/webFile/uploadFile?busId=${this.formData.id}&configFileId=YYZZ`,
        method: 'post',
        data: fileForm,
      }).then((r) => {
        if (r.data.code !== 200) return this.$message.error(r.data.msg);
        this.formData.yyzzList = r.data.data;
      });
    },
    // 上传附件
    onChangeYHKHZM(e) {
      console.log(e);
      if (!this.tileType(e.raw.type)) {
        return this.$message.error('请上传jpg、jpeg、png、bmp、pdf格式的文件');
      }
      let fileForm = new FormData();
      fileForm.append('files', e.raw);

      this.$ajax({
        url: `/webFile/uploadFile?busId=${this.formData.id}&configFileId=YHKHZM`,
        method: 'post',
        data: fileForm,
      }).then((r) => {
        if (r.data.code !== 200) return this.$message.error(r.data.msg);
        this.formData.yhkhzmList = r.data.data;
      });
    },
    // 上传附件
    onChangeQTFJ(e) {
      //   console.log(e);
      if (!this.tileType(e.raw.type)) {
        return this.$message.error('请上传jpg、jpeg、png、bmp、pdf格式的文件');
      }
      let fileForm = new FormData();
      fileForm.append('files', e.raw);

      this.$ajax({
        url: `/webFile/uploadFile?busId=${this.formData.id}&configFileId=QTFJ`,
        method: 'post',
        data: fileForm,
      }).then((r) => {
        if (r.data.code !== 200) return this.$message.error(r.data.msg);
        this.formData.qtList = r.data.data;
      });
    },
    // 删除附件
    delFile(e, type) {
      let params = new FormData();
      params.append('id', e.id);
      this.$ajax({
        url: '/file/deleteFileById',
        method: 'post',
        data: params,
        // 后端不要加token
        // headers: {
        //   token: "3255def0-402c-4af6-98b1-37b9ed696fce",
        // },
      }).then((r) => {
        if (r.data.code !== 200) return;
        switch (type) {
          case 'YYZZ':
            this.formData.yyzzList = this.formData.yyzzList.filter((item) => item.id !== e.id);
            break;
          case 'YHKHZM':
            this.formData.yhkhzmList = this.formData.yhkhzmList.filter((item) => item.id !== e.id);
            break;
          case 'QTFJ':
            this.formData.qtList = this.formData.qtList.filter((item) => item.id !== e.id);
            break;
          default:
            break;
        }
      });
    },

    // 获取id
    getId() {
      this.$ajax({
        url: '/tradeIntermediaryOrgan/getId',
        method: 'GET',
      }).then((r) => {
        if (r.data.code !== 200) return this.$message.error(r.data.msg);
        this.formData.id = r.data.data;
      });
    },

    // 点击提交
    submit() {
      this.$refs.formRef.validate(async (flage, obj) => {
        // console.log(flage, obj);
        if (flage) {
          let data = {
            address: this.formData.address,
            agencyArea: this.formData.agencyArea.join(','),
            agencyAreaName: this.nodeName,
            applicationChannel: 1,
            bankAccount: this.formData.bankAccount,
            bankId: this.formData.bankId,
            businessScope: this.formData.businessScope,
            contactPerson: this.formData.contactPerson,
            contactPhone: this.formData.contactPhone,
            id: this.formData.id,
            legalIdNumber: this.formData.legalIdNumber,
            legalName: this.formData.legalName,
            legalPersonPhone: this.formData.legalPersonPhone,
            legalPhone: this.formData.legalPhone,
            orgCode: this.formData.orgCode,
            orgName: this.formData.orgName,
            intermediaryOrganList: this.formData.list,
            status: 1,
          };

          const aesArr = [
            { name: 'legalIdNumber', value: this.formData.legalIdNumber },
            { name: 'legalPhone', value: this.formData.legalPhone },
            { name: 'bankAccount', value: this.formData.bankAccount },
            { name: 'legalPersonPhone', value: this.formData.legalPersonPhone },
            { name: 'contactPhone', value: this.formData.contactPhone },
          ];
          let newArr = await encryptByAESarr(aesArr);
          data.legalIdNumber = newArr.legalIdNumber;
          data.legalPhone = newArr.legalPhone;
          data.bankAccount = newArr.bankAccount;
          data.legalPersonPhone = newArr.legalPersonPhone;
          data.contactPhone = newArr.contactPhone;
          data["redisKey"] = newArr.redisKey;

          this.$ajax({
            url: '/tradeIntermediaryOrgan/save',
            method: 'POST',
            data,
          }).then((r) => {
            if (r.data.code !== 200) return this.$message.error(r.data.msg);
            this.$router.go(-1);
          });
        }
      });
    },

    // 取消
    reset() {
      this.$router.go(-1);
    },

    // 获取代理地区
    getAreaTree(node, resolve) {
      console.log('node:', node);
      if (node.level === 0) {
        this.$ajax({
          method: 'get',
          url: '/area/webServiceLazyLoadRegionTree',
          data: {},
        }).then((res) => {
          if (res.data.code === 200) {
            this.$nextTick(() => {
              const nodeArrs = this.$refs.treeRef.getCheckedNodes();
              this.nodeName = '';
              let arr = [];
              let namearr = [];
              nodeArrs.forEach((item, index) => {
                if (index !== 0) {
                  this.nodeName += ', ';
                }
                this.nodeName += item.name;
                namearr.push(item.name);
                arr.push({
                  areaId: item.id,
                  areaName: item.name,
                  areaPath: item.allpath,
                  intermediaryOrganId: this.formData.id,
                });
              });
              this.formData.list = arr;
              this.formData.agencyArea = namearr;
            });
            this.treeData = res.data.data.map((item) => {
              item['newAllPath'] = '|' + item.id;
              if (item.level < 2) item['disabled'] = true;
              return item;
            });
            return resolve(res.data.data);
          }
        });
      } else {
        this.$ajax({
          method: 'get',
          url: '/area/webServiceLazyLoadRegionTree',
          data: {
            areaId: node.data.id,
          },
        }).then((res) => {
          if (res.data.code == 200) {
            this.$nextTick(() => {
              const nodeArrs = this.$refs.treeRef.getCheckedNodes();

              this.nodeName = '';
              let arr = [];
              let namearr = [];
              nodeArrs.forEach((item, index) => {
                if (index !== 0) {
                  this.nodeName += ', ';
                }
                this.nodeName += item.name;
                namearr.push(item.name);
                arr.push({
                  areaId: item.id,
                  areaName: item.name,
                  areaPath: item.allpath,
                  intermediaryOrganId: this.formData.id,
                });
              });
              this.formData.list = arr;
              this.formData.agencyArea = namearr;
            });
            createdTreeDataById(this.treeData, node.data.id, res.data.data);
            return resolve(res.data.data);
          }
        });
      }
    },
    // 地区属点击
    checks(checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys) {
      desabledById(this.treeData, getIdByLevel(this.treeData, checkedNodes, 2, checkedKeys));
      this.$nextTick(() => {
        this.nodeName = '';
        let arr = [];
        checkedKeys.checkedNodes.forEach((item, index) => {
          if (index !== 0) {
            this.nodeName += ', ';
          }
          this.nodeName += item.name;
          arr.push({
            areaId: item.id,
            areaName: item.name,
            areaPath: item.allpath,
            intermediaryOrganId: this.formData.id,
          });
        });

        this.formData.list = arr;
      });
      this.formData.agencyArea = checkedKeys.checkedKeys;
    },
    // pdf预览
    viewPdf(option) {
      window.open(option.fileUrl);
    },
  },
};
</script>

<style lang="scss" scoped>
.app {
  height: 100%;
  width: 100%;
  background: #f7f7f7;

  .main {
    padding: 30px 0;
    margin: auto;

    .content {
      width: 1300px;
      margin: auto;
      background-color: #fff;

      .banner {
        width: 100%;
        height: 70px;
        background-image: url('@/assets/applicationIntermediarySettlement/banner.png');
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 24px;
        color: #333333;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 30px;
      }

      .from-box {
        padding: 0 150px;

        .title {
          position: relative;
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 14px;
          color: #333333;
          height: 14px;
          line-height: 1;
          margin-bottom: 22px;

          span {
            position: absolute;
            z-index: 1;
          }

          &::after {
            position: absolute;
            bottom: 0;
            left: 0;
            content: '';
            width: 62px;
            height: 4px;
            background-image: url('@/assets/header/titleLine.png');
            background-position: center center;
            background-size: 100% 100%;
            border-radius: 4px;
          }
        }

        .double {
          width: 100%;
          display: flex;
          gap: 20;

          .from-item {
            &:nth-of-type(1) {
              margin-right: 20px;
            }

            flex: 1;
          }
        }

        .from-item {
          //   margin-bottom: 15px;

          .from-label {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 12px;
            color: #333333;
            line-height: 12px;
            margin-bottom: 10px;
          }

          .from-centent {
            ::v-deep .el-input__inner {
              height: 32px;
              line-height: 32px;
            }

            ::v-deep .el-form-item {
              margin-bottom: 15px;
            }

            ::v-deep .el-form-item__error {
              padding-top: 0;
            }

            ::v-deep .el-upload {
              width: 80px;
              height: 80px;
              background: #ffffff;
              border-radius: 4px;
              border: 1px solid #dcdfe6;
              display: flex;
              align-items: center;
              justify-content: center;

              &::after {
                content: '+';
                color: #dcdfe6;
                font-size: 30px;
                line-height: 1;
                font-weight: 100;
                height: 30px;
                width: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
          }

          .is-file {
            //   width: 80px;
            // height: 80px;
            //   background: #ffffff;
            //   border-radius: 4px;
            //   border: 1px solid #dcdfe6;

            .files-item-box {
              display: flex;
              flex-wrap: wrap;
              gap: 10px;

              .img-box {
                position: relative;
                width: 80px;
                height: 80px;

                &:hover {
                  .del {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 20px;
                    height: 20px;
                    background-color: #333333;
                    color: #fff;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                    cursor: pointer;
                  }
                }

                .del {
                  display: none;
                }

                .img-item {
                  width: 80px;
                  height: 80px;
                }
              }
            }
          }
        }

        .button {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 20px;
          padding-bottom: 30px;

          ::v-deep .el-button {
            width: 160px;
            height: 44px;
          }

          ::v-deep .el-button--primary {
            background: #ed911f;
            border-color: #ed911f;
          }

          ::v-deep .el-button--primary.is-plain {
            color: #ed911f;
            border-color: #ed911f;
            background-color: #fff;
          }
        }
      }
    }
  }
}
</style>
