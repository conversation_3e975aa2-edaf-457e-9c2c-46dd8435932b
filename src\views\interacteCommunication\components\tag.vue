<template>
  <div class="tag">
    <div class="tag-content">
      <div
        v-for="(item, index) in tagList"
        :key="index"
        class="tag-item"
        :class="{ active: activeTab === item.value }"
        @click="tagCahnge(item.value)"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tagList: [
        {
          name: '我有问题',
          value: 0,
        },
        {
          name: '问题公示',
          value: 1,
        },
      ],
      activeTab: 0,
    };
  },
  mounted() {
    this.tagCahnge(this.activeTab);
  },
  methods: {
    /**
     * 切换标签
     */
    tagCahnge(e) {
      this.activeTab = e;
      this.$emit('tagChange', this.activeTab);
    },
  },
};
</script>

<style lang="scss" scoped>
.tag {
  width: 100%;
  height: 50px;
  border-bottom: 1px solid #e5e5e5;

  .tag-content {
    width: 1300px;
    height: 100%;
    margin: auto;
    display: flex;
    align-items: center;

    .tag-item {
      height: 100%;
      margin-right: 50px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 20px;
      color: #333333;
      line-height: 24px;
      display: flex;
      align-items: center;
      cursor: pointer;
      user-select: none;

      &:nth-last-of-type(1) {
        margin-right: 0;
      }
    }

    .active {
      color: #ed911f;
      font-weight: bold;
      font-size: 20px;
      border-top: #ed911f 2px solid;
    }
  }
}
</style>
