<template>
  <div class="nd-switch-box">
    <el-switch v-bind="$attrs" :active-color="activeColor" :inactive-color="inactiveColor" v-on="$listeners" />
  </div>
</template>

<script>
export default {
    props: {
        activeColor: {
            type: String,
            default: "#13CE66",
        },
        inactiveColor: {
            type: String,
            default: "#FF4949",
        },
    },

    data() {
        return {
         
        };
    },

    mounted() {

    },

    methods: {

    },
};
</script>

<style lang="scss" scoped>

</style>