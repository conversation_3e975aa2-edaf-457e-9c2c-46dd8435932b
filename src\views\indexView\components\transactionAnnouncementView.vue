<template>
  <div class="main">
    <div class="main-box">
      <div class="top-box">
        <div class="left">
          <div class="title-text">竞价大厅</div>
          <img src="@/assets/detail/title-icon.png" alt="" srcset="" />
        </div>
        <div class="more-text" @click="toMore">查看更多>></div>
      </div>
      <div class="bot-box">
        <el-table
          :data="tableData"
          style="width: 100%"
          :header-cell-style="{
            'text-align': 'center',
            'font-size': '18px',
            color: '#333333',
            'font-weight': 'bold',
            background: '#EDEDED',
            'border-color': '#EDEDED',
            height: '50px',
          }"
          :cell-style="{
            'font-size': '16px',
            'font-family': 'Microsoft YaHei',
            'font-weight': '400',
            color: '#FFFFFF',
            background: '#FFFFFF',
            cursor: 'pointer',
          }"
          height="440"
          @row-click="goDetail"
        >
          <el-table-column prop="proName" label="项目名称" align="left" show-overflow-tooltip>
            <template #default="scope">
              <!-- <div v-if="scope.$index < 3" style="color: #ff0000">{{ scope.row.proName }}</div>
              <div v-if="scope.$index < 6 && scope.$index > 2" style="color: #13be13">
                {{ scope.row.proName }}
              </div>
              <div v-if="scope.$index < 9 && scope.$index > 5" style="color: #fe9b00">
                {{ scope.row.proName }}
              </div> -->
              <div v-if="scope.row.xmStatus === '4'" style="color:  #333333">
                {{ scope.row.proName }}
              </div>
              <div v-if="scope.row.xmStatus === '11'" style="color:  #333333">
                {{ scope.row.proName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="proCode"
            label="竞价阶段"
            align="center"
            show-overflow-tooltip
            width="140px"
          >
            <template #default="scope">
              <!-- <div v-if="scope.$index < 3" style="color: #ff0000">正在竞价</div>
              <div v-if="scope.$index < 6 && scope.$index > 2" style="color: #13be13">等待竞价</div>
              <div v-if="scope.$index < 9 && scope.$index > 5" style="color: #fe9b00">竞价终止</div> -->
              <!-- <div v-if="scope.row.xmStatus === '4'" style="color: #ff0000">{{ '正在竞价' }}</div> -->
              <div style="display: flex;justify-content: center;">
                <div v-if="scope.row.xmStatus === '4'" class="tag1">{{ '正在竞价' }}</div>
                <div v-if="scope.row.xmStatus === '11'" class="tag2">{{ '等待竞价' }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="bmDateEnd"
            label="报名截止时间"
            width="200"
            align="center"
            show-overflow-tooltip
          >
            <template #default="scope">
              <!-- <div v-if="scope.$index < 3" style="color: #ff0000">{{ scope.row.bmDateEnd }}</div>
              <div v-if="scope.$index < 6 && scope.$index > 2" style="color: #13be13">
                {{ scope.row.bmDateEnd }}
              </div>
              <div v-if="scope.$index < 9 && scope.$index > 5" style="color: #fe9b00">
                {{ scope.row.bmDateEnd }}
              </div> -->
              <div v-if="scope.row.xmStatus === '4'" style="color: #333333">
                {{ scope.row.bmDateEnd }}
              </div>
              <div v-if="scope.row.xmStatus === '11'" style="color: #333333">
                {{ scope.row.bmDateEnd }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="biddTime"
            label="竞价开始时间"
            width="200"
            align="center"
            show-overflow-tooltip
          >
            <template #default="scope">
              <!-- <div v-if="scope.$index < 3" style="color: #ff0000">{{ scope.row.biddTime }}</div>
              <div v-if="scope.$index < 6 && scope.$index > 2" style="color: #13be13">
                {{ scope.row.biddTime }}
              </div>
              <div v-if="scope.$index < 9 && scope.$index > 5" style="color: #fe9b00">
                {{ scope.row.biddTime }}
              </div> -->
              <div v-if="scope.row.xmStatus === '4'" style="color: #333333">
                {{ scope.row.biddTime }}
              </div>
              <div v-if="scope.row.xmStatus === '11'" style="color: #333333">
                {{ scope.row.biddTime }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="name"
            label="挂牌价格"
            width="200"
            align="center"
            show-overflow-tooltip
          >
            <template #default="scope">
              <!-- <div v-if="scope.$index < 3" style="color: #ff0000">
                {{ scope.row.upsetLower }} {{ scope.row.upsetLowerDw }}
              </div>
              <div v-if="scope.$index < 6 && scope.$index > 2" style="color: #13be13">
                {{ scope.row.upsetLower }} {{ scope.row.upsetLowerDw }}
              </div>
              <div v-if="scope.$index < 9 && scope.$index > 5" style="color: #fe9b00">
                {{ scope.row.upsetLower }} {{ scope.row.upsetLowerDw }}
              </div> -->
              <div v-if="scope.row.xmStatus === '4'" style="font-size: 16px; color: #333333">
                {{ scope.row.upsetLower }} {{ scope.row.upsetLowerDw }}
              </div>
              <div v-if="scope.row.xmStatus === '11'" style="font-size: 16px; color: #333333">
                {{ scope.row.upsetLower }} {{ scope.row.upsetLowerDw }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- <div class="pagination-box">
          <div class="left-cont">
            <div class="tag-item">
              <div class="rect-img1"></div>
              <div class="tag-text">正在竞价</div>
            </div>
            <div class="tag-item">
              <div class="rect-img2"></div>
              <div class="tag-text">等待竞价</div>
            </div>
            <div class="tag-item">
              <div class="rect-img3"></div>
              <div class="tag-text">竞价终止</div>
            </div>
          </div>
          <div class="center-cont">
            <img class="center-img" src="@/assets/detail/dengpao.png" alt="" srcset="" />
            <div class="center-text">提醒：页面定时30秒刷新，竞价情况以实际情况为准</div>
          </div>
          <div class="right-cont">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page.sync="pageData.pageNo"
              :page-size="pageData.pageSize"
              layout="total, prev, pager, next"
              :total="pageData.total"
            >
            </el-pagination>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      unitId: '',
      level:'',
      tableData: [], // 表格数据
      list: [], //初始化数据列表
      pageData: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  mounted() {
    // this.$bus.$on('refreshList', (data) => {
    //   this.unitId = data.unitId;
    //   this.level = data.level
    //   this.getData();
    // });
    this.getData();
    // this.getProType();
  },
  beforeDestroy() {
    // 销毁自定义事件
    this.$bus.$off(['refreshList']);
  },

  methods: {
    toMore(remark) {
      // this.$message.success('敬请期待！');
      window.open(ipConfig.memberUrl)
    },
    // 跳转 详情
    goDetail(item) {
      let type;
      let query = [];
      if (item.xmStatus == 2) type = 1;
      if (item.xmStatus == 4) type = 2;
      query = [{ name: '首页', route: '/', query: { type: type } }];
      // this.$router.push({ name: "details", query: { id: item.tendersId, title: item.proName, type: 3, route: JSON.stringify(query) } })
      if (window.location.origin == 'http://localhost:8080') {
        window.open(
          window.location.origin +
            `/#/details?id=${item.tendersId}&route=${JSON.stringify(query)}&type=3&homeType=2&noticeType=0`,
        );
      } else {
        window.open(
          window.location.origin +
            window.location.pathname +
            `#/details?id=${item.tendersId}&route=${JSON.stringify(query)}&type=3&homeType=2&noticeType=0`,
        );
      }
    },
    getData() {
      //---初始化数据
      this.$ajax({
        url: '/notice/noticeInfo',
        method: 'post',
        data: {
          unitId: this.unitId, //地区id
          level:this.level,
          fromHome: '1',
          proTypeId: '',
          proTypeParentId: '',
          page: this.pageData.pageNo,
          size: this.pageData.pageSize,
          noticeType: '0',
          xmStatus: '12', //项目状态
          orderType: '1',
          order: '5',
        },
      }).then((response) => {
        if (response.data.code === 200) {
          this.tableData = response.data.data.records;
          this.pageData.total = response.data.data.total;
        }
      });
    },

    handleSizeChange(val) {
      this.pageSize = val;
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getData();
    },
  },
};
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  padding: 30px 0;
  background: #f7f7f7;
  display: flex;
  justify-content: center;

  .main-box {
    width: 1300px;

    .top-box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 21px;
      user-select: none;
      .left {
        display: flex;
        flex-direction: column;
        height: 32px;

        .title-text {
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 24px;
          color: #333333;
          line-height: 24px;
        }
      }

      .more-text {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 24px;
        cursor: pointer;
      }
    }

    .bot-box {
      // ::v-deep .el-table--fit {
      //   border-radius: 10px 10px 0px 0px;
      //   border-bottom: 1px solid #e5e5e5;
      // }

      ::v-deep .el-table--border .el-table__cell:first-child .cell {
        padding-left: 30px;
      }

      ::v-deep .el-table__body-wrapper {
        // background: #000;
        background: #ffffff;

        .el-table__row:nth-of-type(2n - 1) {
          td {
            background-color: #e6f5f0 !important;
          }
        }
      }

      ::v-deep .el-table .el-table__cell.gutter {
        background: #f0f0f0;
      }

      ::v-deep .el-table tr {
        height: 40px;
      }

      ::v-deep .el-table .el-table__cell {
        padding: 0px;
      }

      .pagination-box {
        width: 100%;
        height: 50px;
        background: #535353;
        border-right: 1px solid #e5e5e5;
        border-bottom: 1px solid #e5e5e5;
        border-left: 1px solid #e5e5e5;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 31px;

        .left-cont {
          display: flex;
          flex-direction: row;
          align-items: center;

          .tag-item:nth-child(-n + 2) {
            margin-right: 20px;
          }
          .tag-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            .rect-img1 {
              width: 15px;
              height: 15px;
              background: #ff0000;
              border-radius: 2px;
              margin-right: 10px;
            }
            .rect-img2 {
              width: 15px;
              height: 15px;
              background: #13be13;
              border-radius: 2px;
              margin-right: 10px;
            }
            .rect-img3 {
              width: 15px;
              height: 15px;
              background: #fe9b00;
              border-radius: 2px;
              margin-right: 10px;
            }

            .tag-text {
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 14px;
              color: #f0f0f0;
              line-height: 40px;
            }
          }
        }

        .center-cont {
          display: flex;
          flex-direction: row;
          align-items: center;

          .center-img {
            margin: 0 10px 0 41px;
          }

          .center-text {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #f0f0f0;
            line-height: 40px;
          }
        }

        .right-cont {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;

          ::v-deep .el-pagination__total {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #f0f0f0;
            // float: right;
          }

          ::v-deep .el-pagination button:disabled {
            background: none;
          }

          ::v-deep .el-pagination .btn-next {
            background: none;
            color: #fff;
          }

          ::v-deep .el-pagination .btn-prev {
            background: none;
            color: #fff;
          }

          ::v-deep .el-pagination .btn-prev:hover {
            color: #f2ae0f;
          }

          ::v-deep .el-pagination .btn-next:hover {
            color: #f2ae0f;
          }

          ::v-deep .el-pager li {
            background: none;
            color: #fff;
          }

          ::v-deep .el-pager li.active {
            background: #f2ae0f;
            color: #fff;
          }

          ::v-deep .el-pager li.active:hover {
            color: #fff;
          }

          ::v-deep .el-pager li:hover {
            color: #f2ae0f;
          }
        }
      }
    }
  }
}
.more-text:hover{
  color: #ed911f!important;
}


.tag1 {
  width: 86px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #fff;
  background: #34AC0B;
  border-radius: 4px;
  font-size: 16px;
}

.tag2 {
  width: 86px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #fff;
  background: #DE8D24;
  border-radius: 4px;
  font-size: 16px;
}
</style>
