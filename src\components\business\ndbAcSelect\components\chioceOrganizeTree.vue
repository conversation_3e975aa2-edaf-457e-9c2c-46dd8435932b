<template>
  <div class="ndb-chioce-organize-tree">
    <el-tree
      highlight-current
      :load="loadNode"
      lazy
      :props="defaultProps"
      node-key="id"
      :default-expanded-keys="treeExpandIdList"
      @node-click="handleNodeClick"
    />
  </div>
</template>
<script>
export default {
  data() {
    return {
      valueTitle: "",
      currentNode: [],
      treeExpandIdList: [],
      defaultProps: {
        label: "name",
        children: "children",
        isLeaf: "isLeaf",
      },
    };
  },
  mounted() {
    //初始化滚动条
    let scrollWrap = document.querySelectorAll(
      ".el-scrollbar .el-select-dropdown__wrap"
    )[0];
    if (scrollWrap) {
      scrollWrap.style.cssText =
        "margin: 0px; max-height: none;overflow: hidden;";
    }
    let scrollBar = document.querySelectorAll(
      ".el-scrollbar .el-scrollbar__bar"
    );
    scrollBar.forEach((ele) => (ele.style.width = 0));
  },
  methods: {
    //懒加载方法
    async loadNode(node, resolve) {
      let _this = this;
      _this.currentNode = [];

      await _this.getData(node);
      //添加默认展开节点id(hasChild代表有下级才会默认展开)
      if (
        node.level === 0 &&
        _this.currentNode &&
        Array.isArray(_this.currentNode) &&
        _this.currentNode[0].hasChild
      ) {
        _this.currentNode.forEach((item) => {
          _this.treeExpandIdList.push(item.id);
          _this.valueTitle = item.name;
        });
        _this.$emit("func", _this.currentNode[0]);
      }
      return resolve(_this.currentNode);
    },
    // 获取组织树数据
    async getData(node) {
      let _this = this;
      let params;
      let url = "";

      // 初始的 node.level 就是 0，第一层
      if (node.level === 0) {
        params = { id: "" };

        await this.$ajax({
          url: url + "/financialAuthority/commonTreeDataAsync.do",
          method: "post",
          data: params,
        })
          .then((res) => {
            if (0 == res.data.code && res.data.data.tree.length > 0) {
              _this.currentNode = res.data.data.tree;
            } else {
              _this.$message.error(res.data.msg);
              _this.currentNode = [];
            }
          })
          .catch(function () {
            _this.$message.error("获取地区信息失败！");
            _this.currentNode = [];
          });
      }

      // 当用户手动点击节点时，node.level 就都是大于等于 1 的了
      if (node.level >= 1) {
        if (node.data.hasChild) {
          params = { id: node.data.id };
        } else {
          return fasle;
        }

        // // 发送请求，获取子节点的数据
        await this.$ajax({
          url: url + "/financialAuthority/commonTreeDataAsync.do",
          method: "post",
          data: params,
        })
          .then((res) => {
            if (
              0 == res.data.code &&
              res.data.data.tree[0].children.length > 0
            ) {
              _this.currentNode = res.data.data.tree[0].children;
            } else {
              _this.$message.error(res.data.msg);
              _this.currentNode = [];
            }
          })
          .catch(function () {
            _this.$message.error("获取地区信息失败！");
            _this.currentNode = [];
          });
      }
    },

    // 切换选项
    handleNodeClick(node) {
      this.valueTitle = node[this.defaultProps.label];
      this.$emit("func", node);
      this.treeExpandIdList = [];
    },
    //【jsp】获得服务器路径
    getServerPath() {
      if (typeof getContextPath === "function") {
        return getContextPath();
      } else {
        return "/sz_product_new";
      }
    },
  },
};
</script>
<style scoped lang="scss">
/*滚轮样式*/
*::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  background-color: #d4e6fb;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  background-color: #f2f7ff;
}
/*滚轮样式*/
.ndb-chioce-organize-tree {
  ::v-deep .el-tree {
    width: 175px;
    font-size: 12px;
    padding-left: 5px;
    font-family: Microsoft YaHei;
    color: #555555;
  }
  ::v-deep .el-tree > .el-tree-node {
    min-width: 100%;
    display: inline-block;
  }
  .el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
    height: auto;
    padding: 0;
    overflow-y: auto;
    overflow-x: auto;
  }
  .el-select-dropdown__item.selected {
    font-weight: normal;
  }

  ul li >>> .el-tree .el-tree-node__content {
    height: auto;
    padding: 0 20px;
    background-color: #ffffff;
  }
  ::v-deep .el-tree-node__label {
    font-weight: normal;
    margin-right: 10px;
    font-size: 12px;
  }
  .el-tree .is-current .el-tree-node__label {
    color: #409eff;
    font-weight: 700;
  }
  .el-tree .is-current .el-tree-node__children .el-tree-node__label {
    color: #606266;
    font-weight: normal;
  }
  .el-select-dropdown__item.hover {
    background: transparent;
  }
  .el-select-dropdown__item span {
    margin-left: 10px;
  }
  ::v-deep .el-scrollbar__bar.is-horizontal {
    height: 0px;
  }
  ::v-deep .el-scrollbar__bar.is-vertical {
    width: 0px;
  }
  ::v-deep .el-tree-node__content:hover {
    background-color: #e8f7ff;
  }
  ::v-deep
    .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: #c8ecff;
  }
}
</style>