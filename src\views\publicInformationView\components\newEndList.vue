<!-- 终止公告 -->
<template>
  <div class="wrap-container">
    <listTypeD requestUrl="/notice/endAnnListV2" :postParams="postParams" :breadcrumbName="'终止公告'"></listTypeD>
  </div>
</template>
<script>
import listTypeD from '@/views/newsListView/components/listTypeD.vue';
export default {
  props: {
    postParams: {
      type: Object,
      default: null
    }
  },
  components: {
    listTypeD,
  },
  data() {
    return {
    };
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.wrap-container {
  min-width: 1000px;
  width: 100%;
  overflow: auto;
  height: 100%;
  background: #fff;
  position: relative;
  padding-bottom: 20px;
}
</style>