<template>
  <el-radio v-bind="$attrs" v-on="$listeners">
    <slot />
  </el-radio>
</template>
<script>
export default {
  props: {
    width: {
      type: String,
      default: "220px",
    },
  },
  data() {
    return {
    };
  },
  mounted() {
  },
  methods: {
  },
};
</script>
<style lang="scss" scoped>
// .nd-radio-box {
//   ::v-deep .el-checkbox__label {
//     font-size: 12px;
//   }
// }
::v-deep .el-radio__label {
  font-size: 16px;
  color: #333;
  padding-left: 6px;
}
::v-deep .el-radio__inner:hover {
  border-color: #316c2c;
}
::v-deep .el-radio__input.is-checked .el-radio__inner {
  border-color: #316c2c;
  background-color: #316c2c;
}
::v-deep .el-radio__input.is-checked + .el-radio__label {
  color: #316c2c;
}
</style>
