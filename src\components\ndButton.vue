<template>
  <button class="nd-button-box" :class="disabled ? 'disabled' : type === 'primary' ? 'primary' : type === 'more' ? 'more' : type === 'table' ? 'table' : 'normal'" :disabled="disabled" type="button" @click="handleClick">
    <i v-if="loading" class="el-icon-loading icon" />
    <i v-if="icon === 'el-icon-search'" class="el-icon-search icon" />
    <span ref="text">
      <slot />
    </span>
  </button>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: "default",
    },
    icon: {
      type: String,
      default: "",
    },
    textSpace: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  mounted() {
    if (this.textSpace) {
      var text = this.$refs.text.innerHTML.trim();
      if (text.toString().split("").length === 2) {
        var a = text.toString().split("");
        this.$refs.text.innerHTML = "<span style='margin-right:10px;'>" + a[0] + "</span>" + "<span>" + a[1] + "</span>";
      }
    }
  },
  methods: {
    handleClick(e) {
      this.$emit("click", e);
    },
  },
};
</script>

<style lang='scss' scoped>
.nd-button-box {
  cursor: pointer;
  position: relative;
}

.icon {
  margin-right: 8px;
}

.nd-button-box + .nd-button-box {
  margin-left: 10px;
}

.normal {
  width: auto;
  height: 36px;
  padding-left: 12px;
  padding-right: 12px;
  font-size: 16px;
  text-align: center;
  border-radius: 5px;
  color: #316c2c;  //#ED911F
  background-color: #ffffff;
  border: 1px solid #316c2c; //#ED911F

  &:hover {
    // color: #ffffff;
    // border-color: #ffffff;
    // background-color: #ffffff;
    box-shadow: 0px 0px  2px #053301;
  }
}

.disabled {
  width: auto;
  height: 28px;
  padding-left: 12px;
  padding-right: 12px;
  font-size: 12px;
  text-align: center;
  border-radius: 5px;
  border: 1px solid rgba(19, 1, 1, 0.3);
  color: rgba(19, 1, 1, 0.3);
  background-color: rgb(239, 239, 239);
  cursor: no-drop;
}

.primary {
  width: auto;
  height: 36px;
  padding-left: 12px;
  padding-right: 12px;
  font-size: 16px;
  text-align: center;
  border-radius: 5px;
  border: 1px solid #316c2c;
  background-color: #316c2c;
  color: #ffffff;

  &:hover {
    // color: #ffffff;
    // border-color: #053301;
    // background-color: #053301;
    box-shadow: 0px 0px  2px #053301;
  }
}

.more {
  width: 100%;
  height: 35px;
  text-align: center;
  font-size: 12px;
  text-align: center;
  background-color: #ffffff;
  border: 0px solid #0098ff;

  &:hover {
    color: #409eff;
    border-color: #c6e2ff;
    background-color: #ecf5ff;
  }
}

.table {
  width: auto;
  height: 28px;
  font-size: 12px;
  text-align: center;
  background-color: #ffffff;
  border: 0px solid #0098ff;
  color: #409eff;

  &:hover {
    color: #ffffff;
    background-color: #0098ff;
  }
}
</style>
