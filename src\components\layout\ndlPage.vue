<template>
  <div class="ndl-page-box">
    <nd-bread-crumb />
    <div class="content">
      <slot />
    </div>
  </div>
</template>

<script>
import ndBreadCrumb from "../ndBreadCrumb.vue";
export default {
  components: {
    ndBreadCrumb,
  },
  props: {
    // title: {
    //   type: String,
    //   default: "未命名",
    // },
  },
};
</script>

<style lang='scss' scoped>
.ndl-page-box {
  width: 100%;
  height: 100%;
  .content {
    height: calc(100% - 30px);
    background-color: #ffffff;
    padding-top: 14px;
    padding-left: 14px;
    padding-right: 14px;
    padding-bottom: 14px;
  }
}
</style>
