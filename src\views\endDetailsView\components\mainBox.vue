<template>
  <div>
    <div class="main-box">
      <div class="endAnnList" v-if="endAnnList.length > 0">
        <div class="endAnnItem" v-for="(item, index) in endAnnList" :key="index">
          <div class="big_title">{{ item.type == 1 ? '项目终止公告' : `标段${item.code} 终止公告` }}</div>
          <div class="annBox">
            <img class="endIcon" src="@/img/zzgg_proEnd.png" alt="">
            <div class="proName">{{ item.projectName }}{{ item.type == 1 ?'':`标段${item.code}`}}终止公告</div>
            <div class="noticeTxt">
              <p>终止理由：</p>
              <p>因<b>{{ item.endReason }}</b>，根据相关规定，本中心对<b>{{ item.projectName }}</b>项目（项目编号：<b>{{ item.projectCode
                  }}</b>
                <template v-if="item.type != 1">
                  —— 标的<b>{{ item.code }}</b>
                </template> ）进行终止。
              </p>
              <p>特此通告</p>
            </div>
            <div class="endddd">
              <p>{{ item.userName }}</p>
              <p>{{ item.endTime }}</p>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <empty :boxHeight="300"></empty>
      </div>
    </div>
    <div style="background-color: #f7f7f7;height: 30px;"></div>
  </div>
</template>

<script>
import empty from '@/views/empty/index.vue';

export default {
  props: {
    datas: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    empty
  },
  data() {
    return {
      endAnnList: []
    };
  },
  watch: {
    datas: {
      immediate: true,
      deep: true,
      handler() {
        this.endAnnList = this.datas.list;
      },
    },
  },
  methods: {

  },
};
</script>

<style lang="scss" scoped>
.main-box {
  width: 1200px;
  margin: auto;
  padding: 30px 20px;
  background-color: #fff;

  .endAnnItem {
    margin-bottom: 30px;

    .big_title {
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
      line-height: 1;
      margin-bottom: 20px;
    }

    .annBox {
      position: relative;
      padding: 20px 24px;
      border: 1px solid #E5E5E5;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 28px;

      .endIcon {
        height: 105px;
        position: absolute;
        top: 13px;
        right: 20px;
      }

      .proName {
        font-size: 20px;
        line-height: 30px;
        text-align: center;
        font-weight: bold;
        margin: 0 100px 20px;
      }

      .noticeTxt {
        margin-bottom: 13px;

        p:nth-child(2) {
          text-indent: 2em;
          width: 1030px;
        }
      }

      .endddd {
        text-align: right;
      }
    }
  }
}
</style>
