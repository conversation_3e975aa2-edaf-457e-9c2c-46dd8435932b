<template>
  <div class="wrap-container">
    <!-- 动态tab菜单 -->
    <div style="position: sticky; top: 0; z-index: 9">
      <div class="nav-area">
        <div
          class="item-area"
          v-for="(item, index) in tabList"
          :key="index"
          :class="[currentTab === item.order ? 'active-item' : 'deactive-item']"
          @click="navClick(item.order, index)"
          @mouseleave="handleLeave"
          @mouseenter="handleEnter(index)"
        >
          <span>{{ item.title }}</span>
          <div class="arrow-img" v-if="item.children && item.children.length > 0">
            <i v-if="isShowNav && currentTab === item.order" class="el-icon-arrow-up"></i>
            <i v-if="!isShowNav" class="el-icon-arrow-down"></i>
          </div>
          <!-- 二级菜单 -->
          <div class="out-cont" v-if="item.children && item.children.length > 0 && currentTab === item.order">
            <div class="menu-cont">
              <div
                class="menu-item"
                v-for="(subItem, subIdx) in item.children"
                :key="subIdx"
                :class="[activeSubMenu === subItem.order ? 'active-menu' : 'deactive-menu']"
                @click.stop="subMenuClick(subItem)"
              >
                {{ subItem.title.length > 9 ? subItem.title.slice(0, 9) + '...' : subItem.title }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="line-gap"></div>
    </div>

    <!-- 列表内容 -->
    <ColumnListTypeC
      :columnId="currentColumnId"
      :breadcrumbName="currentBreadcrumbName"
      :parentBreadcrumbName="currentParentBreadcrumbName"
      :menuPath="currentMenuPath"
      :bannerType="getBannerType()"
    />
  </div>
</template>

<script>
import ColumnListTypeC from '@/components/business/ndColumnListTypeC.vue';

export default {
  name: 'ColumnListPage',
  components: {
    ColumnListTypeC,
  },
  props: {
    columnId: {
      type: String,
      required: true
    },
    pageName: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      tabList: [],
      currentTab: 1,
      activeSubMenu: 1,
      isShowNav: false,
      currentColumnId: '',
      currentBreadcrumbName: '',
      currentParentBreadcrumbName: '',
      // 当前选中的菜单路径（支持多层级）
      currentMenuPath: []
    };
  },
  watch: {
    columnId: {
      handler(newVal) {
        if (newVal) {
          this.getColumnData();
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 移除这里的调用，改为在 watch 中处理
  },
  methods: {
    // 获取栏目数据
    getColumnData() {
      this.$ajax({
        url: '/jcms/site/column/listBySiteId',
        method: 'get',
        serverName: 'nd-ss',
        data: {
          siteId: window.ipConfig.jcms.siteId
        }
      }).then((res) => {
        if (res.data.code === 200) {
          const filteredData = this.filterColumnData(res.data.data, this.columnId);
          console.log(filteredData,'filteredData')

          // 检查是否找到对应的栏目配置，如果没有则提示
          if (filteredData.length === 0) {
            this.$message.warning(`该模块（${this.pageName}）暂未在后台配置相关栏目信息`);
            console.warn(`未找到栏目ID为 ${this.columnId} 的配置，请检查后台栏目管理`);
            return;
          }

          this.tabList = this.convertToTabList(filteredData);

          // 默认选中第一个tab
          if (this.tabList.length > 0) {
            this.currentTab = this.tabList[0].order;
            const firstTab = this.tabList[0];

            // 获取第一个可选择的菜单项（可能是多层级的）
            const firstSelectableItem = this.getFirstSelectableItem(firstTab);
            const menuPath = this.findMenuItemPath(this.tabList, firstSelectableItem);

            // 移除路径中的最后一项（因为最后一项是当前选中项）
            const parentPath = menuPath ? menuPath.slice(0, -1) : [];

            this.setCurrentColumn(firstSelectableItem, parentPath);

            // 如果第一个tab有子菜单，显示下拉
            if (firstTab.children && firstTab.children.length > 0) {
              this.activeSubMenu = 1;
            }
          }
        }
      }).catch(error => {
        console.error('获取栏目数据失败:', error);
        this.$message.error('获取栏目数据失败，请稍后重试');
      });
    },

    // 根据columnId筛选栏目数据
    filterColumnData(data, targetColumnId) {
      if (!data || !Array.isArray(data)) {
        return [];
      }
      
      for (let i = 0; i < data.length; i++) {
        const item = data[i];
        // 如果当前项匹配
        if (item.id === targetColumnId) {
          return item.children || [item];
        }
        // 递归查找子栏目
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
          const found = this.findInChildren(item.children, targetColumnId);
          if (found) {
            return found;
          }
        }
      }
      return [];
    },

    // 在子栏目中查找
    findInChildren(children, targetColumnId) {
      if (!children || !Array.isArray(children)) {
        return null;
      }
      
      for (let i = 0; i < children.length; i++) {
        const child = children[i];
        if (child.id === targetColumnId) {
          return child.children || [child];
        }
        if (child.children && Array.isArray(child.children) && child.children.length > 0) {
          const found = this.findInChildren(child.children, targetColumnId);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },

    // 转换数据格式（支持递归多层级）
    convertToTabList(data) {
      return data.map((item, index) => this.convertMenuItem(item, index + 1));
    },

    // 递归转换菜单项
    convertMenuItem(item, order) {
      const menuItem = {
        title: item.columnName,
        order: order,
        columnId: item.id,
        columnPath: item.columnPath,
        children: null
      };

      // 递归处理子菜单
      if (item.children && Array.isArray(item.children) && item.children.length > 0) {
        menuItem.children = item.children.map((subItem, subIndex) =>
          this.convertMenuItem(subItem, subIndex + 1)
        );
      }

      return menuItem;
    },

    // tab点击
    navClick(order, index) {
      this.currentTab = order;
      const tabData = this.tabList[index];

      if (tabData.children && tabData.children.length > 0) {
        this.isShowNav = true;
        this.activeSubMenu = 1;

        // 获取第一个可选择的子菜单项
        const firstSelectableChild = this.getFirstSelectableItem(tabData.children[0]);
        const menuPath = this.findMenuItemPath(this.tabList, firstSelectableChild);
        const parentPath = menuPath ? menuPath.slice(0, -1) : [];

        this.setCurrentColumn(firstSelectableChild, parentPath);
      } else {
        this.isShowNav = false;
        this.setCurrentColumn(tabData, []);
      }
    },

    // 子菜单点击（支持任意层级）
    subMenuClick(subItem) {
      this.activeSubMenu = subItem.order;

      // 获取最终选择的菜单项（可能还有更深层级）
      const finalSelectableItem = this.getFirstSelectableItem(subItem);
      const menuPath = this.findMenuItemPath(this.tabList, finalSelectableItem);
      const parentPath = menuPath ? menuPath.slice(0, -1) : [];

      this.setCurrentColumn(finalSelectableItem, parentPath);
    },

    // 设置当前栏目（支持多层级路径）
    setCurrentColumn(tabData, menuPath = []) {
      this.currentColumnId = tabData.columnId;
      this.currentBreadcrumbName = tabData.title;
      this.currentMenuPath = [...menuPath];

      // 构建面包屑路径（除了最后一级，因为最后一级是当前页面）
      if (menuPath.length > 0) {
        this.currentParentBreadcrumbName = menuPath[menuPath.length - 1].title;
      } else {
        this.currentParentBreadcrumbName = '';
      }
    },

    // 查找菜单项并返回路径
    findMenuItemPath(menuList, targetItem, currentPath = []) {
      for (let menu of menuList) {
        const newPath = [...currentPath, menu];

        if (menu.columnId === targetItem.columnId) {
          return newPath;
        }

        if (menu.children && menu.children.length > 0) {
          const foundPath = this.findMenuItemPath(menu.children, targetItem, newPath);
          if (foundPath) {
            return foundPath;
          }
        }
      }
      return null;
    },

    // 获取第一个可选择的菜单项（递归查找到最深层的第一个项）
    getFirstSelectableItem(menuItem) {
      if (!menuItem.children || menuItem.children.length === 0) {
        return menuItem;
      }
      return this.getFirstSelectableItem(menuItem.children[0]);
    },

    // 鼠标事件
    handleEnter(index) {
      const tabData = this.tabList[index];
      if (tabData.children && tabData.children.length > 0 && this.currentTab === tabData.order) {
        this.isShowNav = true;
      }
    },

    handleLeave() {
      // 延迟隐藏，避免鼠标移动到下拉菜单时闪烁
      setTimeout(() => {
        this.isShowNav = false;
      }, 200);
    },

    // 根据当前路由获取bannerType
    getBannerType() {
      const routeNameMap = {
        'newsCenter': '1',
        'publicInformationView': '2', 
        'businessRule': '3',
        'lawsRegulation': '4',
        'profileDownload': '5',
        'aboutUs': '6',
        'interacteCommunication': '7'
      };
      return routeNameMap[this.$route.name] || '1';
    }
  }
};
</script>

<style lang="scss" scoped>
.wrap-container {
  width: 100%;
  overflow: auto;
  height: 100%;
  background: #fff;
  position: relative;
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-area {
  width: 1300px;
  height: 50px;
  display: flex;
  align-items: center;
  flex-direction: row;

  .active-item {
    font-family: Microsoft YaHei;
    font-weight: bold;
    font-size: 20px;
    color: #ed911f;
    line-height: 24px;
    border-top: 2px solid #ed911f;
    height: 100%;
    cursor: pointer;
  }

  .deactive-item {
    font-family: Microsoft YaHei;
    font-weight: 400;
    font-size: 20px;
    color: #333333;
    line-height: 24px;
    height: 100%;
    cursor: pointer;
  }

  .item-area:nth-child(-n + 3) {
    margin-right: 50px;
  }

  .item-area {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .arrow-img {
      margin-left: 10px;
    }

    .out-cont {
      display: none;
      position: absolute;
      top: 37px;
      left: -15px;
      width: 204px;
      height: 122px;
      z-index: 999;
    }

    &:hover .out-cont {
      display: block;
    }

    .menu-cont {
      width: 100%;
      display: flex;
      flex-direction: column;
      background: #ffffff;
      border-radius: 8px;
      position: relative;
      bottom: -11px;
      border: 1px solid #e5e5e5;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.07);

      .menu-item {
        height: 34px;
        width: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 13px 17px 14px 16px;
        cursor: pointer;
      }

      .active-menu {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #f1a600;
        line-height: 34px;
        background: rgba(241, 166, 0, 0.17);
      }

      .deactive-menu {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 34px;
      }

      .deactive-menu:hover {
        background: #f7f7f7;
        color: #ed911f;
      }
    }

  }
}

.line-gap {
  width: 100%;
  height: 1px;
  background: #e5e5e5;
}

.deactive-item:hover {
  background: #f7f7f7;
  color: #ed911f !important;
}
</style>










