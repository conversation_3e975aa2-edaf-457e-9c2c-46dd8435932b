<template>
  <div class="ndb-pdf-chrome-box">
    <vue-office-pdf ref='canvasDOM' :options="options" :src="url" @rendered="rendered" />
  </div>
</template>
<script>
import vueOfficePdf from '@vue-office/pdf'
export default {
    components: {
    vueOfficePdf
  },
  props: {
    url: {
      type: String,
      default: "",
    }
  },
  data() {
    return {
      pdfSrc: "",
      options: {
        width: 700,
      },
    };
  },
  watch: {
   
  },
  mounted() {
    
  },
  methods: {
    // 渲染完成
    rendered() {
      console.log("渲染完成")
    },
  }
};
</script>
<style lang="scss" scoped>
</style>