<template>
  <nd-dialog ref="dialog" title="账套切换" width="620px" center append-to-body @close="cancelVueDiaLog()">
    <div class="chioce-books-box">
      <div class="slot_content">
        <!-- 左边树 -->
        <div
          style="
                                        width: 176px;
                                        height: 360px;
                                        border-right: 1px solid #d5e0f0;
                                        font-size: 12px;
                                        overflow-y: auto;
                                        overflow-x: auto;
                                        float: left;
                                      "
        >
          <div
            style="
                                          font-family: Microsoft YaHei;
                                          color: #a9a9a9;
                                          padding-top: 10px;
                                          padding-left: 15px;
                                          padding-bottom: 10px;
                                        "
          >
            地区选择
          </div>
          <chioce-organize-tree @func="getValue($event)" />
        </div>
        <!-- 右边查询 -->
        <div style="float: left; width: 420px; height: 360px">
          <div
            style="
                                          height: 50px;
                                          border-bottom: 1px solid rgb(213, 224, 240);
                                          padding-top: 10px;
                                          padding-left: 11px;
                                        "
          >
            <el-form :inline="true" size="mini" @submit.native.prevent>
              <el-form-item style="width: 90px">
                <!-- <nd-select
                  v-model="selectYear"
                  id="wbNavUsSelect"
                  @change="searchZhangT()"
                >
                  <el-option
                    v-for="item in yearData"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </nd-select> -->
                <nd-date-picker
                  v-model="selectYear"
                  value-format="yyyy"
                  placement="bottom-start"
                  type="year"
                  :clearable="false"
                  :editable="false"
                  width="90px"
                  placeholder=""
                  @change="searchZhangT()"
                />
              </el-form-item>
              <el-form-item style="width: 204px">
                <nd-input v-model.trim="inputZhangt" placeholder="请输入账套名称" width="204px" style="height: 28px" />
              </el-form-item>
              <el-form-item>
                <nd-button type="primary" @click="searchZhangT">
                  搜索账套
                </nd-button>
              </el-form-item>
            </el-form>
          </div>

          <div style="width: 100%; height: calc(100% - 53px); overflow-y: auto" @scroll="scrollEvent">
            <nd-table
              v-loading="loading"
              :data="tableData"
              :show-header="false"
              :border="false"
              :highlight-current-row="true"
              style="width: 100%"
              @row-click="onceClick"
              @row-dblclick="dbClick"
            >
              <el-table-column>
                <template #default="scope">
                  <div class="table_ztName">
                    {{ scope.row.ztName }}
                  </div>
                  <div class="table_unitName">
                    {{ scope.row.unitName }}
                  </div>
                </template>
              </el-table-column>
            </nd-table>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <nd-button type="primary" @click="vueZtSave()">
        确定
      </nd-button>
      <nd-button @click="cancelVueDiaLog()">
        关闭
      </nd-button>
    </template>
    <!-- 用户jsp代码中，有Object标签。用iframe覆盖object -->
    <div
      style="
                                    opacity: 0;
                                    position: absolute;
                                    top: 0;
                                    height: 100%;
                                    width: 100%;
                                    z-index: -1;
                                  "
    >
      <iframe frameborder="0" width="100%" height="100%" />
    </div>
  </nd-dialog>
</template>

<script>
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndSelect from "@/components/ndSelect.vue";
import ndTable from "@/components/ndTable.vue";
import ndInput from "@/components/ndInput.vue";
import ndDatePicker from "@/components/ndDatePicker.vue";
import chioceOrganizeTree from "./components/chioceOrganizeTree.vue";
export default {
  components: {
    ndDialog,
    ndButton,
    ndTable,
    ndSelect,
    ndInput,
    ndDatePicker,
    chioceOrganizeTree,
  },
  props: {

  },
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "label",
      },

      inputZhangt: "", // 搜索账套名
      selectYear: 0, //当前年份（默认当年）
      yearData: [], // 年份数组
      // agoData: 2, //可选年份（以当前年往前算几年）默认2年
      // pickerOptions: this.beginDate(), //设置年选中器的范围
      ztParams: {
        //账套请求参数
        dataId: "",
        ztYear: "",
        ztName: "",
        pageIndex: 1,
        pageSize: 10,
      },
      ztNextPageIndex: 1, //账套列表中下一页的页数（默认1）
      ztAllPage: 0, //账套列表中的总页数
      loading: false, //加载中动画
      tableData: [], //账套列表数据
      timer: null, //设置timer为定时器,处理IE下滚轮触发事件的防抖
      ztID: "", //选中的账套ID
      ztSave: {
        //账套保存参数
        ztId: "",
      },
      getValueNum: 0, // 获取到地区的次数。首次进来默认加载，其他需点击《搜索账套》按钮
      rowData: [], // 选中的账套数据
      redirectURL: "", // 点击保存账套后，需要重定向到的页面URL
    };
  },
  mounted() {
    // this.openVueDiaLog();
  },
  methods: {
    //获取各xxx.jsp页面中getVueDialogNeedType方法，返回1代表不需要刷新账套
    getVueDialogNeedType() {
      if (typeof getVueDialogNeedType === "function") {
        return getVueDialogNeedType(); //xxx.jsp中该方法返回“1”
      } else {
        return "0";
      }
    },
    //禁止select在IE下，光标闪现
    stateChange() {
      // 处理在ie浏览器出现光标的问题
      const elem = document.getElementById("wbNavUsSelect");
      if (elem.hasAttribute("unselectable") === false) {
        elem.setAttribute("unselectable", "on");
        elem.setAttribute("onfocus", "this.blur()");
      }
    },
    // 打开
    openVueDiaLog() {
      this.inputZhangt = "";
      //获取年份
      this.selectYear = "" + new Date().getFullYear() + ""; // 默认赋值当前时间的年份
      this.$refs.dialog.open();
    },
    // 取消
    cancelVueDiaLog() {
      // console.log(this.$parent.$parent.$parent);
      this.$refs.dialog.close();
    },
    //设置可选择的年份
    // beginDate() {
    //   let self = this;
    //   return {
    //     disabledDate(time) {
    //       let curDate = new Date().toString(); // 当前时间戳转为字符串
    //       let curDateYear = new Date().getFullYear(); // 当前时间的年份
    //       let agoDataYearAgoDate = curDate.replace(
    //         curDateYear,
    //         curDateYear - self.agoData
    //       ); // 字符串年份替换为 self.agoData 年前
    //       let agoDataYear = new Date(agoDataYearAgoDate).getTime(); //self.agoData 年前字符串转为时间戳
    //       return time.getTime() > Date.now() || time.getTime() < agoDataYear;
    //     },
    //   };
    // },
    // 子组件组织树发过来的数据----取值
    getValue(value) {
      let _this = this;
      if (value) {
        _this.ztParams.dataId = value.id;
        _this.searchZhangT();
      } else {
        _this.ztParams.dataId = "";
      }
    },
    //获取时间范围
    getYear() {
      let _this = this;

      this.$ajax({
        url: "/apiZhangbglController/getYear.do",
        method: "post",
      })
        .then((res) => {
          if (0 == res.data.code) {
            _this.yearData = res.data.data;
            //_this.agoData = res.data.data.length - 1;
          } else {
            _this.$message.error(res.data.msg);
          }
        })
        .catch(function () {
          _this.$message.error("获取年份数据失败！");
        });
    },
    //搜索账套
    searchZhangT() {
      let _this = this;
      //加载中....动画
      _this.loading = true;
      //年份和输入框赋值
      _this.ztParams.ztYear = _this.selectYear;
      _this.ztParams.ztName = _this.inputZhangt;
      //数据列表
      _this.tableData = [];
      _this.ztAllPage = 0;
      _this.ztNextPageIndex = 1;
      _this.ztParams.pageIndex = 1;

      if (_this.ztParams.dataId && _this.ztParams.ztYear) {
        this.$ajax({
          url: "/apiZhangbglController/indexData.do",
          method: "post",
          data: _this.ztParams,
        })
          .then((res) => {
            //console.log(res,"=================res");
            if (0 == res.data.code) {
              if (res.data.data.list.length > 0) {
                //设置下一页页数
                _this.ztNextPageIndex = _this.ztNextPageIndex + 1;
                //设置总页数
                _this.ztAllPage = res.data.data.totalPage;
                //数据列表
                _this.tableData = res.data.data.list;
                if (_this.getValueNum == 0) {
                  // 首次进入，存URL
                  //系统重定向URL
                  _this.redirectURL = res.data.data.url;
                  _this.getValueNum++;
                }

                //console.log(_this.redirectURL,"=================_this.redirectURL");

                //影藏加载中....动画
                _this.loading = false;
              } else {
                _this.$message("暂无数据");
                //清空数据列表和总页数
                _this.tableData = [];
                _this.ztAllPage = 0;
                //影藏加载中....动画
                _this.loading = false;
              }
            } else {
              _this.$message.error(res.data.msg);
            }
          })
          .catch(function () {
            _this.$message.error("获取账套列表失败！");
            //清空数据列表和总页数
            _this.tableData = [];
            _this.ztAllPage = 0;
            //影藏加载中....动画
            _this.loading = false;
          });
      } else {
        _this.$message.warning("请选择地区或时间！");
        //影藏加载中....动画
        _this.loading = false;
      }
    },
    //加载更多或滚轮
    loadMore() {
      let _this = this;

      //加载中....动画
      _this.loading = true;
      _this.loading = false;
      //年份和输入框、页数赋值
      _this.ztParams.ztYear = _this.selectYear;
      _this.ztParams.ztName = _this.inputZhangt;
      _this.ztParams.pageIndex = _this.ztNextPageIndex;

      //取消加载更多或者滚轮事件（下一页页数必须小于等于总页数）
      if (
        _this.ztNextPageIndex !== 1 &&
        _this.ztNextPageIndex > _this.ztAllPage
      ) {
        //影藏加载中....动画
        _this.loading = false;
        this.$message.success("全部加载完毕");
        return false;
      }

      this.$ajax({
        url: "/apiZhangbglController/indexData.do",
        method: "post",
        data: _this.ztParams,
      })
        .then((res) => {
          if (0 == res.data.code) {
            if (res.data.data.list.length > 0) {
              //数据列表
              res.data.data.list.forEach((item1) => {
                _this.tableData.push(item1);
              });

              //设置下一页页数
              _this.ztNextPageIndex = _this.ztNextPageIndex + 1;
              //影藏加载中....动画
              _this.loading = false;
            } else {
              _this.$message("暂无数据");
              //数据列表
              _this.tableData = [];
              //影藏加载中....动画
              _this.loading = false;
            }
          } else {
            _this.$message.error(res.data.msg);
            //数据列表
            _this.tableData = [];
            //影藏加载中....动画
            _this.loading = false;
          }
        })
        .catch(function () {
          _this.$message.error("获取账套列表失败！");
          //影藏加载中....动画
          _this.loading = false;
        });
    },
    //滚动到达底部触发加载更多事件
    scrollEvent(e) {
      let _this = this;
      //e.srcElement.scrollTop > 1  滚轮必须滚动后，才会触发加载更多。避免重新搜索账套时，请求2次
      if (
        e.srcElement.scrollTop > 1 &&
        e.srcElement.scrollTop + e.srcElement.clientHeight >
        e.srcElement.scrollHeight - 100
      ) {
        //加载更多(兼容IE-- IE浏览器需要防抖)
        _this.shakeSubmit(_this.loadMore, 500)();
      }
    },
    //滚动条触发事件防抖
    shakeSubmit(fn, delay) {
      let _this = this;
      //闭包原理，返回一个函数
      return function (e) {
        //如果定时器存在则清空定时器
        if (_this.timer) {
          clearTimeout(_this.timer);
        }
        //设置定时器，规定时间后执行真实要执行的函数
        _this.timer = setTimeout(function () {
          fn.apply(this, e);
        }, delay);
      };
    },
    //点击某一行时
    onceClick(row) {
      let _this = this;
      _this.rowData = [];
      //选中账套ID赋值
      _this.ztID = row.ztId;
      _this.rowData.push(row);
    },
    //双击某一行时
    dbClick(row) {
      let _this = this;
      _this.rowData = [];
      //选中账套ID赋值
      _this.ztID = row.ztId;
      _this.rowData.push(row);
      //直接调用保存
      _this.vueZtSave();
    },
    //确定选中账套
    vueZtSave() {
      let _this = this;
      if (_this.ztID == "" || this.ztID == null || this.ztID == "undefined") {
        _this.$message.warning("请先选择账套！");
        return false;
      }

      if (this.ieVersion() === 11) {
        window.open(window.document.location.href);
      }

      _this.ztSave.ztId = _this.ztID;
      // console.log(_this.ztID, "===========-------------");


      //不需要保存账套
      if (_this.getVueDialogNeedType() == 1) {
        //传值给ztVue_commonDialog.jsp文件
        getRowData(_this.rowData);
        //关闭弹窗
        _this.cancelVueDiaLog();
      } else {
        this.$ajax({
          url: "/apiZhangbglController/zhangtSave.do",
          method: "post",
          data: _this.ztSave,
        })
          .then((res) => {
            if (0 == res.data.code) {
              if (this.ieVersion() === 11) {
                window.opener = null;
                window.open("", "_self");
                window.close();
              } else {
                // window.location.reload();
                this.cancelVueDiaLog();
                this.$emit("getZt");
              }
              if (this.$parent.$parent.$parent) {
                this.$parent.$parent.$parent.getZtId(this.ztSave.ztId);
              }
            } else if (999 == res.data.code) {
              _this.cancelVueDiaLog();
              if (this.$parent.$parent.$parent) {
                this.$parent.$parent.$parent.getZtId(this.ztSave.ztId);
              }
            } else {
              _this.$message.error(res.data.msg);
            }
          })
          .catch((e) => {
            // _this.$message.error("获取账套列表失败！");
          });
      }
    },
    // 判断是否为IE浏览器
    ieVersion() {
      var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
      var isIE =
        userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
      var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
      var isIE11 =
        userAgent.indexOf("Trident") > -1 && userAgent.indexOf("rv:11.0") > -1;
      if (isIE) {
        var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
        reIE.test(userAgent);
        var fIEVersion = parseFloat(RegExp["$1"]);
        if (fIEVersion == 7) {
          return 7;
        } else if (fIEVersion == 8) {
          return 8;
        } else if (fIEVersion == 9) {
          return 9;
        } else if (fIEVersion == 10) {
          return 10;
        } else {
          return 6; //IE版本<=7
        }
      } else if (isEdge) {
        return "edge"; //edge
      } else if (isIE11) {
        return 11; //IE11
      } else {
        return -1; //不是ie浏览器
      }
    },
  },
};
</script>
<style lang="scss" scoped>
/*滚轮样式*/
*::-webkit-scrollbar {
  width: 10px;
  height: 10px !important;
  /*height: 100%;*/
}

*::-webkit-scrollbar-thumb {
  width: 4px;
  height: 60px;
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #d4e6fb;
  border-radius: 10px;
}

*::-webkit-scrollbar-track {
  /*background-color: rgba(50, 50, 50, .2);*/
  background-color: #f2f7ff;
}

/*滚轮样式*/

.chioce-books-box {
  .top_search {
    height: 48px;
    background: #f6fbff;
    border: 1px solid #e2eaf5;
    padding: 10px;
  }

  .slot_content {
    height: 360px;
  }

  .table_ztName {
    height: 13px;
    font-size: 12px;
    font-family: Microsoft YaHei;
    color: #555555;
  }

  .table_unitName {
    font-size: 12px;
    margin-top: 5px;
    font-family: Microsoft YaHei;
    color: #999999;
  }

  /*加载更多样式*/
  .zt_loadMore {
    width: 100%;
    height: 20px;
    bottom: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-size: 12px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #0098ff;
    background: #fdfdfd;
    box-shadow: 0px -1px 5px 0px rgba(17, 17, 17, 0.2);
  }

  .zt_loadNoMore {
    width: 100%;
    height: 20px;
    bottom: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #999999;
    background: #fdfdfd;
    box-shadow: 0px -1px 5px 0px rgba(17, 17, 17, 0.2);
  }

  ::v-deep .el-table {
    cursor: default;
  }

  ::v-deep .el-table::before {
    height: 0px;
  }

  ::v-deep .el-table .el-table__cell {
    padding: 2px 0;
  }

  ::v-deep .el-form-item__content {
    width: 100%;
  }

  ::v-deep .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 120px;
  }

  ::v-deep .el-input__inner {
    height: 28px;
    line-height: 28px;
  }

  ::v-deep .el-table tr {
    height: 53px;
    cursor: pointer;
  }

  ::v-deep .el-date-editor.el-input {
    width: 90px;
  }
}
</style>
