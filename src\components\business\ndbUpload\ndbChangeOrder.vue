<template>
    <nd-dialog ref="changeOrderRef" title="调整顺序" class="change-order-box" width="350px" height="180px" append-to-body
        center>
        <div class="order-box">
            <div class="order-box-input">
                <div class="order-box-input-text">
                    调整至第
                </div>
                <nd-input ref="orderNumRef" v-model.trim="orderNum" class="order-box-in" width="70px"
                    @input="orderNum = orderNum.replace(/^(0+)|[^\d]+/g, '')" />
                <div class="order-box-input-text">
                    位
                </div>
            </div>
            <div class="order-box-text">
                调整到第几位，就排到第几位，
            </div>
            <div class="order-box-text">
                输入值超附件最大序号时，默认排列到最后
            </div>
        </div>
        <template #footer>
            <div>
                <nd-button type="primary" @click="OrderSubmit">
                    确定
                </nd-button>
                <nd-button @click="close">
                    关闭
                </nd-button>
            </div>
        </template>
    </nd-dialog>
</template>

<script>
import ndDialog from '@/components/ndDialog.vue';
import ndButton from '@/components/ndButton.vue';
import ndInput from '@/components/ndInput.vue';
export default {
    components: {
        ndDialog,
        ndButton,
        ndInput,
    },
    props: {
        // 是否是三资
        isSz: {
            type: Boolean,
            default: false,
        },
        // 批次id
        dataId: {
            type: String,
            default: "",
        },
        // 类型id
        fileTypeId: {
            type: String,
            default: "",
        },
        // 所属模块id
        functionId: {
            type: String,
            default: "",
        },
        // 是否是报账单据管理
        // isNeedDescribe: {
        isDocumentManagement: {
            type: Boolean,
            default: false,
        },
    },

    data() {
        return {
            orderNum: '',//改变的序号，默认99
            changeOrderData: {},//改变顺序的数据
            ListIndex: 0,//列表的序号
            dataBaseIndex: 0,//数据库里的序号
            newFileList: [],//新的list
        };
    },

    mounted() {

    },

    methods: {
        //确定
        OrderSubmit() {
            if(this.orderNum == ""){
                this.$message.warning('请输入调整位置')
                return
            }
            if (this.orderNum == this.ListIndex + 1) {
                this.close()
                return false
            }
            if (this.isSz) {
                let params = {
                    doId: this.dataId,
                    fileTypeId: this.fileTypeId,
                    functionId: this.functionId,
                    fileId: this.changeOrderData.fileId,
                    nowSort: this.changeOrderData.ORDERNUMBER,
                    checkSort: this.orderNum,
                    indexNum: this.ListIndex,
                };
                if (this.isDocumentManagement) {
                    this.$ajax({
                        url: "/szOriginalFile/updateFilesOrderCode.do",
                        method: "post",
                        params: params,
                    }).then((res) => {
                        if (res.data.code === 0) {
                            this.newFileList = res.data.data.files
                            // console.log(this.newFileList, "====this.newFileList1");
                            this.newFileList.forEach(element => {
                                this.$set(element, "curName", element.FILE_NAME)
                                this.$set(element, "fileId", element.ID)
                                this.$set(element, "changeName", false)
                                this.$set(element, "suffix", element.FILE_SUFFIX_TYPE)
                                this.$set(element, "filePath", element.fileAllPath)
                            });
                            // console.log(this.newFileList, "====this.newFileList2");
                            this.$emit("orderNumfuc", this.newFileList)
                            this.close()
                        } else {
                            this.$message.warning(res.data.msg)
                        }
                    });
                    return;
                }
                this.$ajax({
                    url: "/szFileNew/updateFilesOrderCode.do",
                    method: "post",
                    params: params,
                }).then((res) => {
                    if (res.data.code === 0) {
                        this.newFileList = res.data.data.files
                        // console.log(this.newFileList, "====this.newFileList1");
                        this.newFileList.forEach(element => {
                            this.$set(element, "curName", element.FILENAME + '.' + element.FILESUFFIX)
                            this.$set(element, "fileId", element.ID)
                            this.$set(element, "changeName", false)
                            this.$set(element, "suffix", element.FILESUFFIX)
                        });
                        // console.log(this.newFileList, "====this.newFileList2");
                        this.$emit("orderNumfuc", this.newFileList)
                        this.close()
                    } else {
                        this.$message.warning(res.data.msg)
                    }
                });
            }
        },
        //弹出弹窗
        open(item, index) {
            if (item) {
                this.changeOrderData = item
                console.log(this.changeOrderData, "this.changeOrderData");
            }
            if (index !== undefined) {
                this.ListIndex = index
            }
            this.$refs.changeOrderRef.open(); //打开弹框
            this.$nextTick(() => {
                this.$refs.orderNumRef.$children[0].focus()
            })
        },
        reset() {
            this.orderNum = 99
        },
        //关闭弹窗
        close() {
            this.reset()
            this.$refs.changeOrderRef.close();
        },
        enter() {
            this.reset()
            this.$refs.changeOrderRef.close();
        },
        handleClose() {
            this.reset()
            this.$refs.changeOrderRef.close();
        },
    },
};
</script>

<style lang="scss" scoped>
.change-order-box {
    .order-box {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        margin-top: 10%;

        .order-box-input {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-bottom: 5px;

            .order-box-input-text {
                color: #555;
                font-size: 16px;
            }

            .order-box-in {
                margin: 0 10px;
                color: #555;
                font-size: 16px;
                border: 1px solid #DCE0E7;
                border-radius: 4px;
            }
        }

        .order-box-text {
            color: #999999;
            font-size: 12px;
            line-height: 18px;
        }
    }
}
</style>