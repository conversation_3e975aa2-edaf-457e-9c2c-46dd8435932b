<template>
  <el-dialog
    v-if="dialogVisible"
    :visible.sync="dialogVisible"
    class="nd-full-screen-dialog-box"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    v-bind="$attrs"
    :top="0"
    @before-close="close()"
    v-on="$listeners"
  >
    <div slot="title" class="dialog-title-box">
      <div class="dialog-title">
        <div>{{ title }}</div>
      </div>
    </div>
    <div
      class="dialog-content-box"
      :class="showFooter ? 'dialog-content-box-with-footer' : 'dialog-content-box-no-footer'"
    >
      <div class="dialog-content">
        <slot />
      </div>
    </div>
    <div v-if="showFooter" slot="footer" class="dialog-footer-box">
      <div class="dialog-footer">
        <slot name="footer" />
      </div>
    </div>
    <div v-if="!showFooter" class="dialog-no-footer-box" />
  </el-dialog>
</template>
<script>
import ndButton from "./ndButton.vue";
export default {
  components: {
    ndButton,
  },
  props: {
    title: {
      type: String,
      default: "未命名",
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: false,
    };
  },
  mounted() {
  },
  methods: {
    // 打开
    open() {
      this.dialogVisible = true;
    },
    // 关闭
    close() {
      this.dialogVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.nd-full-screen-dialog-box {
  width: 100%;
  height: 100%;
  background-color: transparent;
  ::v-deep .el-dialog {
    width: 100%;
    height: 100%;
    background-color: transparent;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__header {
    padding: 0px;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    height: 35px;
  }
  ::v-deep .el-dialog__body {
    padding: 0px;
    flex: 1;
    // height: calc(100% - 35px);
  }
  ::v-deep .el-dialog__footer {
    height: 60px;
    padding: 0px;
  }
  ::v-deep .el-dialog__close {
    color: #ffffff;
  }
  ::v-deep .el-dialog__headerbtn {
    top: 10px;
  }
  .dialog-title-box {
    width: 100%;
    height: 35px;
    background-color: #f6faff;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
    padding-left: 1px;
    padding-right: 1px;
    padding-top: 1px;
    .dialog-title {
      width: 100%;
      height: 100%;
      border-top-right-radius: 5px;
      border-top-left-radius: 5px;
      background-color: #0098ff;
      padding-left: 14px;
      font-size: 14px;
      color: #ffffff;
      display: flex;
      flex-direction: row;
      align-items: center;
    }
  }
  .dialog-content-box {
    width: 100%;
    background-color: #f6faff;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 10px;
    .dialog-content {
      width: 100%;
      height: 100%;
      border: 1px solid #e6e6e6;
      background-color: #ffffff;
      font-size: 12px;
    }
  }
  .dialog-content-box-with-footer {
    height: 100%;
  }
  .dialog-content-box-no-footer {
    height: calc(100% - 10px);
  }
  .dialog-footer-box {
    width: 100%;
    height: 60px;
    background-color: #f6faff;
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 10px;
    .dialog-footer {
      width: 100%;
      height: 100%;
      border-left: 1px solid #e6e6e6;
      border-right: 1px solid #e6e6e6;
      border-bottom: 1px solid #e6e6e6;
      background-color: #f6faff;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
    }
  }
  .dialog-no-footer-box {
    width: 100%;
    height: 10px;
    background-color: #f6faff;
  }
}
</style>
