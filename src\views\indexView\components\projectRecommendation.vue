<template>
  <div class="project">
    <div class="title">
      <div class="title-text">项目推荐</div>
      <div class="box-title-right" @click="toMore">
        <span>MORE</span>
        <img class="address-img" src="@/assets/projectImg/right2.png" alt="" />
      </div>
    </div>
    <div class="item-box">
      <div
        class="item"
        v-for="(item, index) in newList"
        :key="index"
        @click="toDetail(item.path)"
        style="cursor: pointer"
      >
        <img :src="item.img" alt="" />
        <div class="position">
          <div :title="item.title">{{ item.title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  watch: {
    tabActive: {
      handler() {
        // console.log(this.tabActive);
      },
    },
  },
  data() {
    return {
      tabActive: 0,
      newList: [],
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.$ajax({
        url: '/xmtj/',
        method: 'get',
        serverName: 'nd-ss',
      }).then((r) => {
        let newArr = eval(r.data);
        if (newArr.length > 4) {
          this.newList = newArr.slice(0, 4);
        } else {
          this.newList = newArr;
        }
      });
    },

    // 点击去往详情
    toDetail(path) {
      this.$router.push({ path: 'listDetailView', query: { path: path,bannerType:3 } });
    },
    // 点击more
    toMore() {
      this.$router.push('projectRecommendations');
    },
  },
};
</script>

<style lang="scss" scoped>
.project {
  width: 1300px;
  margin: auto;

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 17px;
    user-select: none;

    .title-text {
      font-size: 36px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
      line-height: 1;
    }

    .box-title-right {
      width: 78px;
      height: 38px;
      line-height: 38px;
      text-align: center;
      border: 1px solid #cccccc;
      font-size: 14px;
      color: #999999;
      cursor: pointer;
      position: relative;
      margin-right: 20px;

      .address-img {
        width: 32px;
        height: 7px;
        position: absolute;
        top: 13px;
        right: -22px;
        z-index: 99999;
      }
    }
  }

  .item-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .item {
      width: 310px;
      height: 200px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        display: block;
      }

      .position {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        padding: 7px 14px;
        background: #0069ca;
        opacity: 0.68;
        width: 100%;
        text-align: center;
        height: 56px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;

        > div {
          color: #fff;
          font-size: 14px;
          font-weight: bold;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
      }
    }
  }
}
</style>