<!-- 登记公告 -->
<template>
    <div class="wrap-container">
      <listTypeC v-show="currentTab == '1'" requestUrl="/djgg/" :breadcrumbName="'登记公告'"></listTypeC>  
    </div>
  </template>
  <script>
  import listTypeC from '@/views/newsListView/components/listTypeC.vue';
  export default {
    props: {
      currentTab: {
      type: String,
      default: '1',
    },
  },
    components: {
      listTypeC,
    },
    data() {
      return {
      };
    },
    methods: {},
  };
  </script>
  
  <style lang="scss" scoped>
  .wrap-container {
    min-width: 1000px;
    width: 100%;
    overflow: auto;
    height: 100%;
    background: #fff;
    position: relative;
    padding-bottom: 20px;
  }
  </style>
  