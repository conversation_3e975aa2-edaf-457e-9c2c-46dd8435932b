<template>
  <div class="main">
    <div class="line-gap"></div>
    <div class="main-top">
      <!-- <breadcrumbs :titles="[fromPage, '公示公告']" /> -->
      <div class="nav-search">
        <div class="nav-area">
          <div class="item-area" v-for="(item, index) in navArr" :key="index"
            :class="[activeNav === item.order ? 'active-item' : 'deactive-item']" @click="navClick(item.order, index)"
            @mouseleave="handleLeave(index)" @mouseenter="handleEnter(index)">
            <span>{{ item.title }}</span>
            <div class="arrow-img" v-if="item.order == 2 || item.order == 6">
              <i v-if="isShowNav && activeNav === 2" class="el-icon-arrow-up"></i>
              <i v-if="!isShowNav" class="el-icon-arrow-down"></i>
            </div>
            <div class="out-cont" v-if="item.order == 2 && activeNav === 2">
              <div class="menu-cont">
                <div class="menu-item" v-for="(it, idx) in menuArr" :key="idx"
                  :class="[activeMenu === it.order ? 'active-menu' : 'deactive-menu']" @click.stop="menuClick(it)">
                  {{ it.title }}
                </div>
              </div>
            </div>
            <div class="out-cont" v-if="item.order == 6 && activeNav === 6">
              <div class="menu-cont">
                <div class="menu-item" v-for="(it, idx) in menuArr2" :key="idx"
                  :class="[activeMenu === it.order ? 'active-menu' : 'deactive-menu']" @click.stop="menuClick(it)">
                  {{ it.title }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="search-area" v-if="activeNav === 0 || activeNav === 1 || activeNav === 4 || activeNav === 5">
          <div class="search-item">
            <div class="search-title">产权类型&nbsp;:&nbsp;</div>
            <div class="search-val">
              <div v-for="(item, index) in formList.tradeCategoryList" :key="index" class="val-item1"
                :class="[form.tradeCategory === item.proTypeKey ? 'active-val1' : 'deactive-val1']"
                @click="changeTrade(item, index, 1)">
                {{ item.proTypeName }}
              </div>
              <!-- <menuTree :menu="formList.tradeCategoryList"></menuTree> -->
            </div>
          </div>

          <div class="search-item" v-show="form.tradeCategory">
            <div class="search-title"></div>
            <div class="search-val child">
              <div class="val-item1"
                :class="[form.tradeCategory2 === item.proTypeKey ? 'active-val1' : 'deactive-val1']"
                v-for="(item, index) in formList.tradeCategoryList2" :key="index" @click="changeTrade(item, index, 2)">
                {{ item.proTypeName }}
              </div>
            </div>
          </div>
          <div class="search-item">
            <div class="search-title">项目地区&nbsp;:&nbsp;</div>
            <div class="search-val">
              <div v-for="(item, index) in formList.projectAreaList" :key="index" class="val-item1"
                :class="[form.projectArea === item.id ? 'active-val1' : 'deactive-val1']" @click="changeArea(item, 1)">
                {{ item.name }}
              </div>
            </div>
          </div>

          <div class="search-item" v-show="form.projectArea && formList.projectAreaList2">
            <div class="search-title"></div>
            <div class="search-val child">
              <div class="val-item1" :class="[form.projectArea2 === item.id ? 'active-val1' : 'deactive-val1']"
                v-for="(item, index) in formList.projectAreaList2" :key="index" @click="changeArea(item, 2)">
                {{ item.name }}
              </div>
              <!-- <div v-show="form.projectArea2 && formList.projectAreaList3">
                <span
                  :class="{ active: form.projectArea3 == '' }"
                  style="margin-right: 16px"
                  @click="changeArea({ unitId: '', allPath: '', unitName: '全部' }, 3)"
                  >全部</span
                >
                <ndRadio
                  v-model="form.projectArea3"
                  v-for="(item, index) in formList.projectAreaList3"
                  :key="index"
                  :label="item.allPath"
                  :value="item.allPath"
                  @change="pathChange"
                  >{{ item.unitName }}</ndRadio
                >
              </div> -->
            </div>
          </div>

          <div class="search-item" v-show="form.projectArea2 && formList.projectAreaList3">
            <div class="search-title"></div>
            <div class="search-val child">
              <div class="val-item1" :class="[form.projectArea3 === item.id ? 'active-val1' : 'deactive-val1']"
                v-for="(item, index) in formList.projectAreaList3" :key="index" @click="changeArea(item, 3)">
                {{ item.name }}
              </div>
            </div>
          </div>

          <div class="search-item" v-if="activeNav === 0 || activeNav === 5">
            <div class="search-title">项目状态&nbsp;:&nbsp;</div>
            <div class="search-val">
              <div v-for="(item, index) in typeList" :key="index" class="val-item1"
                :class="[activeStatus === item.order ? 'active-val1' : 'deactive-val1']"
                @click="handleActive3(item.order)">
                {{ item.name }}
              </div>
            </div>
          </div>
        </div>
        <div class="query-cont" v-if="activeNav === 0 || activeNav === 1 || activeNav === 5">
          <!-- <div class="query-item" v-if="activeNav === 0">
            <div class="query-title">成交起止时间：</div>
            <div class="query-body">
              <el-date-picker
                style="width: 373px"
                v-model="form.dealTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="~"
                start-placeholder=""
                end-placeholder=""
                @change="changeDate1"
              >
              </el-date-picker>
            </div>
          </div> -->
          <!-- <div class="query-item" v-if="activeNav === 0">
            <div class="query-title">报名截止时间：</div>
            <div class="query-body">
              <el-date-picker
                style="width: 373px"
                v-model="form.applyTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="~"
                start-placeholder=""
                end-placeholder=""
                @change="changeDate2"
              >
              </el-date-picker>
            </div>
          </div> -->
          <div class="query-item">
            <div class="query-title">交易面积：</div>
            <div class="query-body">
              <el-input style="width: 160px" v-model="form.dealAcreage1" @input="(e) => {
                priceFormat(e, 1);
              }
                " placeholder="" clearable></el-input>
              <span style="margin: 0 8px">~</span>
              <el-input style="width: 160px" v-model="form.dealAcreage2" @input="(e) => {
                priceFormat(e, 2);
              }
                " placeholder="" clearable></el-input>
              <span class="text-mu">亩</span>
            </div>
          </div>
          <div class="btn-cont" v-if="activeNav === 0 || activeNav === 1 || activeNav === 5">
            <el-button style="height: 34px; width: 90px; color: #ed911f; border: 1px solid #ed911f" plain
              @click="reset">重置</el-button>
            <el-button style="
                height: 34px;
                width: 90px;
                background: #ed911f;
                border: 1px solid #ed911f;
                color: #fff;
              " @click="handleSearch">查询</el-button>
          </div>
          <!-- <div class="query-item">
            <div class="query-title">项目查询：</div>
            <div class="query-body">
              <el-input
                style="width: 373px"
                v-model="form.projectNameOrCode"
                placeholder="请输入项目名称/项目编号"
              ></el-input>
            </div>
          </div> -->
        </div>
        <div class="query-cont" v-if="activeNav === 4">
          <div class="btn-cont">
            <el-button style="height: 34px; width: 90px; color: #ed911f; border: 1px solid #ed911f" plain
              @click="reset">重置</el-button>
            <el-button style="
                height: 34px;
                width: 90px;
                background: #ed911f;
                border: 1px solid #ed911f;
                color: #fff;
              " @click="handleSearch">查询</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="main-box" style="margin-top: 31px" v-if="activeNav === 0 || activeNav === 1 || activeNav === 5">
      <!-- <div class="top-box">
        <div
          class="sort-box"
          v-for="(item, index) in sortList"
          :key="index"
          @click="handleActive4(index)"
        >
          <div class="by-item" :class="[activeSort === index ? 'active-by' : 'deactive-by']">
            {{ item.sortBy }}
            <div v-if="index !== 0 && activeSort === index">
              <i v-if="item.order" class="el-icon-top"></i>
              <i v-else class="el-icon-bottom"></i>
            </div>
          </div>
        </div>
      </div> -->
      <div v-if="list.length !== 0" class="bot-box">
        <div class="list-box" v-for="(item, index) in list" :key="index" @click="goDetail(item)">
          <div class="list-top" :style="{
            backgroundImage: item.proPicPath
              ? `url('${item.proPicPath}')`
              : `url('${defaultImgUrl}')`,
          }">
            <div style="background-color: #ed911f"
              v-if="(item.xmStatus === '2' || item.xmStatus === '6') && (activeNav === 0 || activeNav === 5)"
              class="list-tag">
              <span>报名中</span>
            </div>
            <div
              style="background-color: #f43d3d"
              v-if="(item.xmStatus === '4' || item.xmStatus === '6') && activeNav === 0"
              class="list-tag"
            >
              <span>竞价中</span>
            </div>
            <div
              style="background-color: #399676"
              v-if="item.xmStatus === '1' && activeNav === 0"
              class="list-tag"
            >
              <span>待报名</span>
            </div>
            <!-- <div
              style="background-color: #0dc58b"
              v-if="item.xmStatus === '5' && activeNav === 0"
              class="list-tag"
            >
              <span>已成交</span>
            </div> -->
            <div style="background-color: #afafaf" v-if="item.tendersType === '2' && activeNav === 1" class="list-tag">
              <span>已流标</span>
            </div>
            <div style="background-color: #0dc58b" v-if="item.tendersType === '1' && activeNav === 1" class="list-tag">
              <span>已成交</span>
            </div>
            <!-- <div class="list-tag">
              <img
                v-if="item.xmStatus === '2'"
                src="@/assets/detail/bmz-tag.png"
                alt=""
                srcset=""
              />
              <img
                v-if="item.xmStatus === '4'"
                src="@/assets/detail/jjz-tag.png"
                alt=""
                srcset=""
              />
              <img
                v-if="item.xmStatus === '5'"
                src="@/assets/detail/ycj-tag.png"
                alt=""
                srcset=""
              />
              <img
                v-if="item.xmStatus === '6'"
                src="@/assets/detail/bmz-tag.png"
                alt=""
                srcset=""
              />
              <img
                v-if="item.xmStatus === '6'"
                src="@/assets/detail/jjz-tag.png"
                alt=""
                srcset=""
              />
            </div> -->

            <div class="project-code">
              <div class="project-text">项目编号：{{ item.proCode }}</div>
            </div>
          </div>
          <div class="list-bot" v-if="activeNav === 0 || activeNav === 5">
            <div class="text1" v-if="activeNav === 0">
              <template v-if="item.changeFlag"><span>已变更</span></template>
              {{ item.projectName ? item.projectName.length > 8 ? item.projectName.slice(0, 8) + '...' :
                item.projectName : "" }}{{ item.dataSource == 1 && item.tenderCode ? ' - 标段' + item.tenderCode : '' }}
            </div>
            <div class="text1" v-else>
              <template v-if="item.changeFlag"><span>已变更</span></template>
              {{ item.projectName ? item.projectName.length > 8 ? item.projectName.slice(0, 8) + '...' :
                item.projectName : "" }}{{ item.tenderCode ? ' - 标段' + item.tenderCode : '' }}
            </div>
            <div class="text-cont text-cont1">
              <img class="gpxm-img" src="@/assets/detail/gpjg.png" alt="" />
              <div class="text2">挂牌价格：</div>
              <div class="text3">
                <span class="text-num">{{ item.upsetLower }}</span>
                <span class="text-unit">{{ item.upsetLowerDw }}</span>
              </div>
            </div>
            <div class="text-cont text-cont2">
              <img class="gpxm-img" src="@/assets/detail/cqlx.png" alt="" />
              <div class="text2">产权类型：</div>
              <div class="text3">{{ item.childTradeVarietyName }}</div>
            </div>
            <div class="text-cont text-cont2">
              <img class="gpxm-img" src="@/assets/detail/gpsj.png" alt="" />
              <div class="text2">挂牌时间：</div>
              <div class="text3">{{ item.annoTime }}</div>
            </div>
          </div>
          <div class="list-bot" v-if="activeNav === 1">
            <div class="text1">
              <template v-if="item.changeFlag==1"><span>已变更</span></template>
              {{ item.projectName ? item.projectName.length > 8 ? item.projectName.slice(0, 8) + '...' :
                item.projectName : "" }}{{ item.tenderCode ? ' - 标段' + item.tenderCode : '' }}
            </div>
            <div class="text-cont text-cont1">
              <img class="gpxm-img" src="@/assets/detail/whcjjg.png" alt="" />
              <div class="text2">成交价格：</div>
              <div class="text3" v-if="item.tendersType === '2'">
                <span>流标</span>
              </div>
              <div class="text3" v-else>
                <span class="text-num">{{ item.upsetLower }}</span>
                <span class="text-unit">元</span>
              </div>
            </div>
            <div class="text-cont text-cont2">
              <img class="gpxm-img" src="@/assets/detail/whcjr.png" alt="" />
              <div class="text2">成交人：</div>
              <div class="text3" v-if="item.tendersType === '2'">--</div>
              <div class="text3" v-else>{{ item.traderPerson }}</div>
            </div>
            <div class="text-cont text-cont2" style="transform: translateX(-2px)">
              <img class="gpxm-img" src="@/assets/detail/gpsj.png" alt="" />
              <div class="text2">公示日期：</div>
              <div class="text3">{{ item.annoDate }}</div>
              <!-- <div class="text3" v-if="item.tendersType === '2'">--</div>
              <div class="text3" v-else>{{ item.annoDate }}</div> -->
            </div>
          </div>
        </div>
      </div>
      <div v-else class="bot-box">
        <empty :boxHeight="300"></empty>
      </div>
    </div>
    <!-- 鉴证公告 -->
    <div class="main-box" v-if="activeNav === 2">
      <!-- <financeTable /> -->
      <noticeList :currentTab="currentTab" :key="addOption" :keyWords="form.projectNameOrCode" />
      <!-- <newNoticeList :currentTab="currentTab" :key="addOption" :keyWords="form.projectNameOrCode" /> -->
    </div>
    <!-- 登记公告 -->
    <div class="main-box" v-if="activeNav === 3">
      <!-- <pledgeList /> -->
      <newPledgeList />
    </div>
    <!-- 终止公告 -->
    <div class="main-box" v-if="activeNav === 4">
      <newEndList :postParams="postParams" />
    </div>
    <!-- 变更公告 -->
    <div class="main-box" v-if="activeNav === 6&&activeMenu==1">
      <resultChange :currentTab="currentTab"  />
    </div>
    <!-- 结果变更 -->
    <div class="main-box" v-if="activeNav === 6&&activeMenu==2">
      <resultChange2 :currentTab="currentTab"  />
    </div>

    <div class="pagination-cont" v-if="activeNav === 0 || activeNav === 1 || activeNav === 5">
      <!-- 分页组件 -->
      <ndPagination :total="total" :current-page="currentPage" :total-page="totalPage" :page-size="pageSize"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>

    <div class="search-box">
      <el-input v-model="form.projectNameOrCode" style="width: 300px" placeholder="请输入关键字查询" class="custom-placeholder"
        clearable @clear="handleClear">
        <el-button @click="handleSearch" slot="append" icon="el-icon-search"></el-button>
      </el-input>
    </div>
  </div>
</template>

<script>
import breadcrumbs from './components/breadcrumbs.vue';
import menuTree from './components/menuTree.vue';
import financeTable from './components/financeTable.vue';
import ndRadio from '@/components/ndRadio.vue';
import ndSelect from '@/components/ndSelect.vue';
import ndPagination from '@/components/ndPagination.vue';
import ndButton from '@/components/ndButton.vue';
//旧
import noticeList from './components/noticeList.vue';
import pledgeList from './components/pledgeList.vue';
//新
import newNoticeList from './components/newNoticeList.vue';
import newPledgeList from './components/newPledgeList.vue';
import newEndList from './components/newEndList.vue';
import resultChange from './components/resultChange.vue';
import resultChange2 from './components/resultChange2.vue';
import empty from '@/views/empty/index.vue';

let isRouter = '';
export default {
  components: {
    ndRadio,
    breadcrumbs,
    ndPagination,
    ndSelect,
    ndButton,
    financeTable,
    menuTree,
    noticeList,
    pledgeList,
    empty,
    newNoticeList,
    newPledgeList,
    newEndList,
    resultChange,
    resultChange2
  },
  name: 'home',
  data() {
    return {
      addOption: 1,
      defaultImgUrl: window.ipConfig.defaultImg,
      navArr: [
        {
          title: '挂牌项目',
          url: require('@/assets/detail/deacnygg.png'),
          noUrl: require('@/assets/detail/acnygg.png'),
          bgurl: require('@/assets/detail/navbg.png'),
          order: 0,
        },
        {
          title: '推荐项目',
          url: require('@/assets/detail/deacnygg.png'),
          noUrl: require('@/assets/detail/acnygg.png'),
          bgurl: require('@/assets/detail/navbg.png'),
          order: 5,
        },
        {
          title: '结果公告',
          url: require('@/assets/detail/deacncjgg.png'),
          noUrl: require('@/assets/detail/acncjgg.png'),
          bgurl: require('@/assets/detail/centerbg.png'),
          order: 1,
        },
        {
          title: '变更公告',
          url: require('@/assets/detail/deacntzgg.png'),
          noUrl: require('@/assets/detail/acntzgg.png'),
          bgurl: require('@/assets/detail/rightbg.png'),
          order: 6,
        },
        {
          title: '终止公告',
          url: require('@/assets/detail/deacnrzgg.png'),
          noUrl: require('@/assets/detail/acnrzgg.png'),
          order: 4,
        },
        {
          title: '鉴证公告',
          url: require('@/assets/detail/deacntzgg.png'),
          noUrl: require('@/assets/detail/acntzgg.png'),
          bgurl: require('@/assets/detail/rightbg.png'),
          order: 2,
        },

        {
          title: '登记公告',
          url: require('@/assets/detail/deacnrzgg.png'),
          noUrl: require('@/assets/detail/acnrzgg.png'),
          order: 3,
        },
      ],
      isShowNav: false,
      menuArr: [
        { title: '注销类', order: '1' },
        { title: '变更类', order: '2' },
        { title: '遗失类', order: '3' },
      ],
      menuArr2: [
        { title: '公告变更', order: '1' },
        { title: '结果变更', order: '2' },
      ],
      activeMenu: '1',
      currentTab: '1',
      typeList: [
        { name: '全部', order: '' },
        { name: '待报名', order: '1' },
        { name: '报名中', order: '2' },
        { name: '竞价中', order: '4' },
      ],
      activeNav: 0,
      activeIndex: 1,
      fromPage: '',
      sortList: [
        { sortBy: '默认', order: '' },
        { sortBy: '价格', order: false },
        { sortBy: '面积', order: false },
        { sortBy: '流转年限', order: false },
        { sortBy: '报名时间', order: false },
        // { sortBy: '成交时间' },
      ], //排序
      activeStatus: '', //项目状态
      activeSort: 0, //排序
      orderType: '',
      currentPage: 1,
      total: 0, //总数据条数
      totalPage: 1, //总页数
      pageSize: 16,
      list: [], //初始化数据列表
      nodeName: '',
      treeExpandIdList: [],
      defaultProps: {
        id: 'id',
        level: 'jibie',
        label: 'name',
        children: 'children',
        isLeaf: 'isLeaf',
      },

      options: [],
      areaId: '',
      jypz: '',
      treeExpandIdList: [],

      // search查询
      form: {
        tradeCategory: '', // 选中 交易品种
        tradeCategory2: '', // 选中 交易品种2
        proTypeParentId: '', // 父级id
        projectArea: '', // 选中 项目地区
        projectArea2: '', // 选中 项目地区2
        projectArea3: '', // 选中 项目地区3
        level: '',
        level1: '',
        level2: '',
        projectStatus: '0', // 选中 项目状态
        dealAcreage1: '', // 交易面积
        dealAcreage2: '', // 交易面积
        dealTime: [], // 成交起止时间
        cjDateStart: '', //成交开始时间
        cjDateEnd: '', //成交结束时间
        applyTime: [], //报名起止时间
        bmEndFirst: '', //报名开始时间
        bmEndSecond: '', //报名结束时间
        projectNameOrCode: '', // 项目名称/项目编号
      },
      // search查询 数组
      formList: {
        // 交易品种
        tradeCategoryList: [],
        // 交易品种2
        tradeCategoryList2: [],
        // 项目地区
        projectAreaList: [],
        // 项目地区2
        projectAreaList2: [],
        // 项目地区3
        projectAreaList3: [],
        // 项目状态
        projectStatusList: [
          { dataKay: '0', dataValue: '全部' },
          { dataKay: '2', dataValue: '报名中' },
          { dataKay: '4', dataValue: '竞价中' },
        ],
      },
      postParams: null
    };
  },
  // beforeRouteEnter(to, from, next) {
  //   next((_vm) => {
  //     _vm.getData();
  //     _vm.fromPage = from.meta.pageName || localStorage.getItem('list_detail_pageName_cache');
  //     if (from.meta.pageName)
  //       localStorage.setItem('list_detail_pageName_cache', from.meta.pageName);
  //   });
  // },
  beforeRouteEnter(to, from, next) {
    console.log('前置守卫', from.fullPath);
    isRouter = from.fullPath;
    next();
  },
  beforeRouteLeave(to, from, next) {
    sessionStorage.removeItem('aaaSelectedNav')
    next();
  },
  activated() {
    let selectNav = sessionStorage.getItem('aaaSelectedNav')
    if (selectNav) {
      this.activeNav = selectNav * 1
    }
    window.addEventListener("beforeunload", function (e) {
      sessionStorage.removeItem('aaaSelectedNav')
    });
    this.fromHome(); // 处理首页调过来的情况
  },
  mounted() {
    // this.getData();
    this.getTradeCategoryList();
    // this.getProType();
    // this.getProjectArea(false, 'one');
    this.getProjectArea();
    // window.addEventListener("beforeunload", function (e) {
    //   sessionStorage.removeItem('aaaSelectedNav')
    // });
  },

  methods: {
    // 项目地区切换
    changeArea(item, flag) {
      console.log(item, flag, '节点数据');
      // this.form.level = item.
      if (flag == 1) {
        this.form.projectArea2 = '';
        this.form.projectArea3 = '';
        this.form.projectArea = item.id;
        this.formList.projectAreaList2 = this.getProjectArea(item.id, 2);
        this.allPath = item.allPath;
        if (item.level) {
          this.form.level = item.level;
        } else {
          this.form.level = '';
          this.form.level1 = '';
          this.form.level2 = '';
        }
      } else if (flag == 2) {
        this.form.projectArea3 = '';
        this.form.projectArea2 = item.id;
        this.formList.projectAreaList3 = this.getProjectArea(item.id, 3);
        this.allPath = item.allPath;
        if (item.level) {
          this.form.level1 = item.level;
        } else {
          this.form.level1 = '';
          this.form.level2 = '';
        }
      } else {
        this.form.projectArea3 = item.id;
        this.allPath = item.allPath;
        if (item.level) {
          this.form.level2 = item.level;
        } else {
          this.form.level2 = '';
        }
      }
    },

    // 交易品种切换
    changeTrade(item, index, flag) {
      console.log(item);
      this.form.proTypeParentId = item.proTypeParentId;
      if (flag == 1) {
        this.form.tradeCategory2 = '';
        this.form.tradeCategory = item.proTypeKey;
        // 获取子集 发请求
        if (item.proTypeKey)
          this.formList.tradeCategoryList2 = [
            { proTypeKey: '', proTypeName: '全部' },
            ...this.formList.tradeCategoryList[index].children,
          ];
      }
      if (flag == 2) this.form.tradeCategory2 = item.proTypeKey;
    },
    handleActive3(order) {
      this.activeStatus = order;
    },

    handleActive4(index) {
      this.activeSort = index;
      this.sortList[index].order = !this.sortList[index].order;
      this.sortList.forEach((item, idx) => {
        if (index !== idx) {
          item.order = false;
        }
      });
      if (index === 0) {
        this.orderType = '';
      } else {
        if (this.sortList[index].order) {
          this.orderType = '1';
        } else {
          this.orderType = '2';
        }
      }
      this.getData();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getData();
    },
    changeTab(th, status) {
      this.activeIndex = th;
      this.form.projectStatus = status;
      this.getData();
    },
    radioChange(item) {
      console.log(item);
      this.areaId = this.form.projectArea3;
      this.form.level = item.level;
      this.getData();
    },

    toMore() {
      //更多
      this.$router.push('projectInformationView?type=0');
    },
    // 跳转 详情
    goDetail(item) {
      // 挂牌项目 终止公告 详情跳转处理
      if (this.activeNav == 0 || this.activeNav == 4) {
        console.log(item, '详情');
        if (item.dataSource == 2) {
          return window.open(item.projectLink);
        }
      }

      let query = [];
      let type;
      query = [{ name: '公示公告', route: 'publicInformationView' }];
      if (item.xmStatus == 2) type = 1;
      if (item.xmStatus == 4) type = 2;
      // this.$router.push({ name: "details", query: { id: item.tendersId, title: item.proName, route: JSON.stringify(query), type: type } })
      if (window.location.origin == 'http://localhost:8080') {
        window.open(
          window.location.origin +
          `/#/details?id=${item.tendersId}&projectId=${item.projectId}&changeType=${item.type}&route=${JSON.stringify(query)}&type=0&homeType=2&noticeType=${this.activeNav}`,
        );
      } else {
        window.open(
          window.location.origin +
          window.location.pathname +
          `#/details?id=${item.tendersId}&projectId=${item.projectId}&changeType=${item.type}&route=${JSON.stringify(query)}&type=0&homeType=2&noticeType=${this.activeNav}`,
        );
      }
    },

    // 处理路由传递参数
    fromHome() {
      // this.reset();
      this.clearSearch();
      let type = this.$route.query.type;
      console.log(type, '路由参数');
      if (type) {
        this.activeNav = Number(type);
      }
      if (this.$route.query.projectNameOrCode) {
        this.form.projectNameOrCode = this.$route.query.projectNameOrCode;
      } else {
        this.form.projectNameOrCode = ""
      }
      console.log(this.activeNav, '8888888888888');
      // let tradeCategory = this.$route.query.tradeCategory1;
      // let tradeCategory2 = this.$route.query.tradeCategory2;
      // let projectNameOrCode = this.$route.query.projectNameOrCode;

      // if (isRouter == '/' && (tradeCategory || projectNameOrCode || type)) this.reset();

      // 获取交易品种
      this.getTradeCategoryList();
      // 获取地区
      this.getProjectArea();

      // if (isRouter == '/' || isRouter.includes('/details')) {
      //   console.log('====', typeof type, type);
      //   if (type == '1') this.tabIndex = 1; // tab切换
      //   if (type === '0') this.tabIndex = 0;
      //   // else this.tabIndex = 0;
      // }
      // if (tradeCategory) this.form.tradeCategory = tradeCategory;
      // if (tradeCategory) this.form.proTypeParentId = tradeCategory;
      // if (tradeCategory2) this.form.tradeCategory2 = tradeCategory2;
      // if (projectNameOrCode) this.form.projectNameOrCode = projectNameOrCode;

      this.getData();
    },

    getData() {
      if (this.form.dealAcreage1 && this.form.dealAcreage2) {
        if (this.form.dealAcreage1 * 1 >= this.form.dealAcreage2 * 1) {
          this.$message({
            message: '左侧数值需小于右侧数值',
            type: 'warning',
          });
          return;
        }
      }
      //---初始化数据
      let unitId = '';
      if (
        this.form.projectArea3
          ? this.form.projectArea3
          : this.form.projectArea2
            ? this.form.projectArea2
            : this.form.projectArea
      ) {
        unitId = this.form.projectArea3
          ? this.form.projectArea3
          : this.form.projectArea2
            ? this.form.projectArea2
            : this.form.projectArea;
      }

      let level = ""
      if (this.form.level && this.form.level1 && this.form.level2) {
        level = this.form.level2
      } else if (this.form.level && this.form.level1 && !this.form.level2) {
        level = this.form.level1
      } else if (this.form.level && !this.form.level1 && !this.form.level2) {
        level = this.form.level
      } else {
        level = ""
      }

      console.log(this.form.level, this.form.level1, this.form.level2, "oooooooooooooo");

      console.log(level, "层级");

      if (this.activeNav == 4) {
        this.postParams = {
          unitId, //地区id
          level,
          proTypeId: this.form.tradeCategory ? this.form.tradeCategory2 : '',
          proTypeParentId: this.form.tradeCategory,
        }
        return
      }
      let urlNew = '/notice/noticeInfo'
      if(this.activeNav == 0){
        urlNew = '/notice/noticeInfoV2'
      }
      this.$ajax({
        url: urlNew,
        method: 'post',
        data: {
          unitId, //地区id
          level,
          fromHome: '0',
          proTypeId: this.form.tradeCategory ? this.form.tradeCategory2 : '',
          proTypeParentId: this.form.tradeCategory,
          page: this.currentPage,
          size: this.pageSize,
          noticeType: this.activeNav == 5 ? '0' : this.activeNav.toString(),
          xmStatus: this.activeStatus, //项目状态
          order: this.activeSort.toString(), // 排序名称
          // orderType: this.activeSort !== 0 ? '1' : '', //排序方式  选中时候 1 正序排序
          orderType: this.orderType, //排序方式  选中时候 1 正序排序
          keyWords: this.form.projectNameOrCode, //项目名称
          cjDateStart: this.form.cjDateStart, //成交开始时间
          cjDateEnd: this.form.cjDateEnd, //成交结束时间
          bmEndFirst: this.form.bmEndFirst, //报名开始时间
          bmEndSecond: this.form.bmEndSecond, //报名结束时间
          jymjStart: this.form.dealAcreage1, //最大交易面积
          jymjEnd: this.form.dealAcreage2, //最小交易面积
          recommendedItemFlag: this.activeNav == 5 ? 1 : ''
        },
      }).then((response) => {
        if (response.data.code == 200) {
          this.list = response.data.data.records;
          this.total = response.data.data.total;
        }
      });
    },
    getProType() {
      //---初始化交易品种
      this.$ajax({
        url: '/baseInfo/webHomeProType',
        method: 'get',
      }).then((response) => {
        if (response.data.code == 200) {
          this.options = response.data.data;
        }
      });
    },
    // 节点点击
    handleNodeClick(node) {
      this.nodeName = node[this.defaultProps.label];
      this.areaId = node[this.defaultProps.id];
      this.getData();
      this.treeExpandIdList = [];
      this.$refs.ndSelect.blur();
    },
    handleChange(node) {
      this.jypz = node;
      this.getData();
    },

    navClick(order, index) {
      this.activeNav = order;
      console.log(this.activeNav, '选中项目');
      if (order === 0 || order === 1 || order == 4 || order === 5) {
        // this.getData();
        sessionStorage.setItem('aaaSelectedNav', order)
        this.reset();
      } else {
        sessionStorage.removeItem('aaaSelectedNav')
      }
      if (order !== 2) {
        this.isShowNav = false;
      } else {
        this.isShowNav = true;
      }
    },

    menuClick(item) {
      this.addOption++
      this.activeMenu = item.order;
      this.currentTab = item.order;
    },

    // 获取交易品种的接口
    getTradeCategoryList() {
      new Promise((resolve, reject) => {
        this.$ajax({
          method: 'get',
          url: '/baseInfo/webProInfoProType',
          data: {},
        })
          .then((res) => {
            if (res.data.code == 200) {
              // console.log('交易品种的接口', res.data.data);
              this.formList.tradeCategoryList = [
                { proTypeKey: '', proTypeName: '全部' },
                ...res.data.data,
              ];
              resolve();
            }
          })
          .then((res) => {
            let tradeCategory = this.$route.query.tradeCategory1;
            let tradeCategory2 = this.$route.query.tradeCategory2;
            if (tradeCategory) {
              this.form.tradeCategory = tradeCategory; // 选中一级分类
              this.form.tradeCategory2 = tradeCategory2; // 选中二级分类
              let findChild = this.formList.tradeCategoryList.find((item, index) => {
                return item.proTypeKey == tradeCategory;
              });
              this.form.proTypeParentId = tradeCategory; // 父级id
              this.formList.tradeCategoryList2 = [
                { proTypeKey: '', proTypeName: '全部' },
                ...findChild.children,
              ];
            }
          });
      });
    },

    // 获取项目地区的接口
    getProjectArea(areaId, flag) {
      console.log(flag);
      return new Promise((resolve, reject) => {
        if (areaId) {
          this.$ajax({
            method: 'get',
            url: '/area/webServiceLazyLoadRegionTree',
            data: {
              areaId: areaId,
            },
          }).then((res) => {
            if (res.data.code == 200) {
              // console.log('地区树的接口1', res.data.data);
              if (flag == 2)
                this.formList.projectAreaList2 = [{ id: '', name: '全部' }, ...res.data.data];
              if (flag == 3)
                this.formList.projectAreaList3 = [{ id: '', name: '全部' }, ...res.data.data];
              console.log(this.formList.projectAreaList3, '999');
              resolve();
            }
          });
        } else {
          this.$ajax({
            method: 'get',
            url: '/area/webServiceLazyLoadRegionTree',
          }).then((res) => {
            if (res.data.code == 200) {
              // console.log('地区树的接口2', res.data.data);
              this.formList.projectAreaList = [
                { id: '', name: '全部' },
                ...res.data.data[0].children,
              ];

              // if (flag == 'one') {
              //   // this.form.projectArea = res.data.data[0].id;
              //   this.allPath = res.data.data[0].allPath;
              //   this.formList.projectAreaList2 = this.getProjectArea(res.data.data[0].id, 2);
              //   // 获取数据
              //   this.getData();
              // }
            }
          });
        }
        resolve();
      });
    },

    changeDate1(e) {
      console.log(e, '时间');
      if (e && e.length !== 0) {
        this.form.cjDateStart = e[0];
        this.form.cjDateEnd = e[1];
      } else {
        this.form.cjDateStart = '';
        this.form.cjDateEnd = '';
      }
    },

    changeDate2(e) {
      console.log(e, '时间');
      if (e && e.length !== 0) {
        this.form.bmEndFirst = e[0];
        this.form.bmEndSecond = e[1];
      } else {
        this.form.bmEndFirst = '';
        this.form.bmEndSecond = '';
      }
    },

    // 亩数提示
    priceFormat(value, flag, int = 100) {
      value = value.toString();
      // debugger
      // 先把非数字的都替换掉，除了数字和小数点
      value = value.replace(/[^\d.]/g, '');
      // // 必须保证第一个为数字而不是小数点
      value = value.replace(/^\./g, '');
      // // 保证只有出现一个小数点而没有多个小数点
      value = value.replace(/\.{2,}/g, '.');
      // // 保证小数点只出现一次，而不能出现两次以上
      value = value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      // 保证只能输入2个小数
      value = value.replace(/^(\d+)\.(\d{0,2}).*$/, '$1.$2');
      // 只能8位整数
      let index = value.indexOf('.');
      if (index > -1) {
        value = value.slice(0, index < int ? index : int) + value.slice(index);
      } else {
        value = value.slice(0, int);
      }
      if (flag == 1) this.form.dealAcreage1 = value;
      if (flag == 2) this.form.dealAcreage2 = value;
    },

    // 查询
    handleSearch() {
      this.currentPage = 1;
      this.getData();
      this.$bus.$emit('searchData', this.form.projectNameOrCode);
    },

    //清除触发
    handleClear(val) {
      console.log(val, "清除");
    },

    // 清空查询条件
    clearSearch() {
      this.form.projectArea = '';
      this.form.projectArea2 = '';
      this.form.projectArea3 = '';
      this.form.level = '';
      this.form.level1 = '';
      this.form.level2 = '';
      this.form.tradeCategory = '';
      this.form.tradeCategory2 = '';
      this.currentPage = 1;
      this.pageSize = 16;
      this.activeStatus = '';
      this.activeSort = 0;
      // this.form.projectNameOrCode = '';
      this.form.dealTime = [];
      this.form.cjDateStart = '';
      this.form.cjDateEnd = '';
      this.form.applyTime = [];
      this.form.bmEndFirst = '';
      this.form.bmEndSecond = '';
      this.form.dealAcreage1 = '';
      this.form.dealAcreage2 = '';
    },

    // 重置 事件
    reset() {
      // 清空
      this.clearSearch();
      // 搜索
      this.getData();
    },

    handleLeave(e) {
      this.isShowNav = false;
    },

    handleEnter(e) {
      if (this.activeNav === 2 && e === 2) {
        this.isShowNav = true;
      }
      if (this.activeNav === 6 && e === 6) {
        this.isShowNav = true;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  padding: 0px 0 21px;
  background: #fff;
  display: flex;
  align-items: center;
  flex-direction: column;

  .text-mu {
    font-family: Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    margin-left: 6px;
  }

  .line-gap {
    width: 100%;
    height: 1px;
    background: #e5e5e5;
    position: relative;
    top: 50px;
  }

  .main-top {
    width: 1300px;
    background: #fff;
    // padding: 0px 0px 25px;
    // margin-bottom: 31px;

    .nav-search {
      // margin-top: 20px;
      // height: 370px;

      .nav-area {
        width: 100%;
        height: 50px;
        display: flex;
        align-items: center;
        flex-direction: row;
        // background: #f9f9f9;
        // margin-bottom: 24px;

        .active-item {
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 20px;
          color: #ed911f;
          line-height: 24px;
          border-top: 2px solid #ed911f;
          height: 100%;
        }

        .deactive-item {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 20px;
          color: #333333;
          line-height: 24px;
          height: 100%;
        }

        .item-area:nth-child(-n + 6) {
          margin-right: 50px;
        }

        .item-area {
          // width: 25%;
          display: flex;
          align-items: center;
          justify-content: center;
          // height: 77px;
          position: relative;
          cursor: pointer;

          .arrow-img {
            margin-left: 10px;
          }

          .img-pic {
            height: 28px;
            width: 28px;
            margin-right: 8px;
          }

          &:hover .out-cont {
            display: block;
          }

          .out-cont {
            display: none;
            position: absolute;
            top: 37px;
            left: -15px;
            width: 140px;
            height: 122px;
            z-index: 999;
          }

          .menu-cont {
            width: 100%;
            //height: 111px;
            display: flex;
            flex-direction: column;
            background: #ffffff;
            border-radius: 8px;
            position: relative;
            bottom: -11px;
            border: 1px solid #E5E5E5;
            box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.07);

            .menu-item {
              height: 34px;
              width: 140px;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            .active-menu {
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 16px;
              color: #f1a600;
              line-height: 34px;
              background: rgba(241, 166, 0, 0.17);
            }

            .deactive-menu {
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 16px;
              color: #666666;
              line-height: 34px;
            }

            .deactive-menu:hover {
              background: #f7f7f7;
            }
          }
        }
      }

      .search-area {
        margin-top: 24px;
        margin-bottom: 11px;

        .search-item {
          display: flex;
          flex-direction: row;
          // align-items: center;
          margin-bottom: 6px;

          .search-title {
            width: 89px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            font-size: 14px;
            color: #333333;
            // line-height: 40px;
            margin-right: 11px;
            vertical-align: top;
          }

          .search-val {
            flex: 1;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            // column-gap: 12px;
            margin-right: 12px;

            .val-item1 {
              height: 28px;
              border-radius: 2px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 14px;
              line-height: 40px;
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 0px 10px;
              cursor: pointer;
            }

            .active-val1 {
              color: #ffffff;
              height: 23px;
              background: #ed911f;
              border-radius: 8px;
              min-width: 58px;
              padding: 0 15px;
            }

            .deactive-val1 {
              background: #fff;
              color: #666;
              min-width: 58px;
              padding: 0 15px;
            }
          }
        }
      }

      .query-cont {
        display: flex;
        align-items: center;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;
        // row-gap: 12px;
        // margin-bottom: 30px;
        width: 100%;

        ::v-deep .el-range-editor.el-input__inner {
          height: 28px;
          padding: 0 10px;
        }

        ::v-deep .el-date-editor .el-range__icon {
          line-height: 28px;
        }

        ::v-deep .el-date-editor .el-range-separator {
          line-height: 28px;
          padding: 0;
        }

        ::v-deep .el-input__inner {
          height: 24px;
          line-height: 24px;
        }

        ::v-deep .el-input__clear {
          display: flex;
          align-items: center;
        }

        ::v-deep .el-range__close-icon {
          display: flex;
          align-items: center;
        }

        // .query-item:nth-child(-n + 2) {
        //   margin-bottom: 12px;
        // }
        .query-item {
          width: 50%;
          height: 34px;
          display: flex;
          align-items: center;

          .query-title {
            width: 98px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            font-size: 14px;
            color: #333333;
          }

          .query-body {
            height: 24px;
            flex: 1;
            display: flex;
            align-items: center;
            flex-direction: row;
          }
        }
      }

      .btn-cont {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        float: right;
        flex: 1;

        ::v-deep .el-button {
          padding: 0;
        }
      }
    }
  }

  .main-box {
    width: 1300px;
    background: #ffffff;
    // padding: 20px 23px 7px 20px;
    // margin-top: 31px;

    .top-box {
      display: flex;
      align-items: center;
      margin-bottom: 26px;

      .sort-box {
        display: flex;
        align-items: center;
        flex-direction: row;

        .by-item {
          margin-right: 30px;
          display: flex;
          align-items: center;
          cursor: pointer;
        }

        .active-by {
          font-family: Microsoft YaHei;
          font-weight: bold;
          font-size: 14px;
          color: #ed911f;
          line-height: 40px;
        }

        .deactive-by {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          line-height: 40px;
        }
      }
    }

    .bot-box {
      display: flex;
      align-items: center;
      // justify-content: space-between;
      flex-wrap: wrap;
      // row-gap: 13px;
      // column-gap: 13px;

      .list-box:not(:nth-child(4n)) {
        margin-right: 20px;
      }

      .list-box {
        // height: 335px;
        width: 310px;
        // background: #f9f9f9;
        border-radius: 4px;
        cursor: pointer;
        margin-bottom: 30px;

        .list-top:hover {
          background-size: 120%;
          /* 放大1.2倍 */
        }

        .list-top {
          background-size: 100%;
          height: 226px;
          width: 100%;
          position: relative;
          margin-bottom: 14px;
          display: flex;
          align-items: flex-end;
          border-radius: 8px;

          .project-code {
            width: 100%;
            height: 40px;
            background: rgba(61, 61, 61, 0.8);
            border-radius: 0px 0px 8px 8px;
            display: flex;
            align-items: center;
            padding-left: 10px;

            .project-text {
              width: 99%;
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 16px;
              color: #ffffff;
              line-height: 36px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .list-tag {
            height: 34px;
            color: #ffffff;
            display: flex;
            align-items: center;
            position: absolute;
            right: 0px;
            top: 0px;
            border-radius: 0px 8px 0px 8px;
            padding: 0px 14px;

            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            line-height: 24px;
          }
        }

        .list-bot {
          width: 100%;
          padding: 0 10px;

          .text1 {
            font-family: Microsoft YaHei;
            font-weight: bold;
            font-size: 18px;
            color: #333333;
            // line-height: 36px;
            margin-bottom: 14px;
            white-space: nowrap;
            // display: -webkit-box;
            // -webkit-line-clamp: 1;
            // -webkit-box-orient: vertical;
            // overflow: hidden;
            // text-overflow: ellipsis;
            span{
              font-size: 15px;
              font-weight: normal;
              padding:0 4px;
              margin-right: 4px;
              color:#ed911f;
              border:1px solid #ed911f;
              border-radius: 4px;
            }
          }

          .text-cont1 {
            height: 15px;
          }

          .text-cont2 {
            height: 13px;
          }

          .text-cont:nth-child(-n + 3) {
            margin-bottom: 13px;
          }

          .text-cont {
            display: flex;
            flex-direction: row;
            align-items: center;

            .gpxm-img {
              margin-right: 9px;
              width: 17px;
              height: 16px;
            }

            .text2 {
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 14px;
              color: #666666;
              // line-height: 24px;
              white-space: nowrap;
            }

            .text3 {
              font-family: Microsoft YaHei;
              font-weight: 400;
              font-size: 14px;
              color: #666666;
              // line-height: 24px;
              // width: 90%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;

              .text-num {
                font-family: Microsoft YaHei;
                font-weight: bold;
                font-size: 18px;
                color: #ed911f;
              }

              .text-unit {
                font-family: Microsoft YaHei;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                line-height: 24px;
              }
            }
          }
        }
      }
    }
  }
}

.pagination-cont {
  :v-deep .el-pagination button:disabled {
    background: none;
  }

  ::v-deep .el-pagination .btn-next,
  .el-pagination .btn-prev {
    background: none;
  }

  ::v-deep .el-pagination .btn-prev:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pagination .btn-next:hover {
    color: #f2ae0f;
  }

  ::v-deep .el-pager li {
    background: none;
  }

  ::v-deep .el-pager li.active {
    color: #fff;
    background: #f2ae0f;
  }

  ::v-deep .el-pager li.active:hover {
    color: #fff;
  }

  ::v-deep .el-pager li:hover {
    color: #f2ae0f;
  }
}

.search-box {
  position: absolute;
  right: 310px;
  top: 21px;

  .custom-placeholder ::placeholder {
    color: #999;
    /* 指定颜色 */
  }

  ::v-deep .el-input__inner {
    height: 38px;
    line-height: 38px;
    // background-color: #60c6d7;
    // border: 1px solid #60c6d7;
    color: #666;
    border-radius: 8px 0 0px 8px;
  }

  ::v-deep .el-input-group {
    border-radius: 8px;
  }

  // ::v-deep .el-input__icon {
  //   line-height: 38px;
  //   color: #fff;
  // }

  ::v-deep .el-icon-search {
    color: #fff;
    // position: relative;
    // left: 50%;
    // transform: translateX(-50%);
  }

  ::v-deep .el-input-group__append,
  .el-input-group__prepend {
    padding: 0 15.25px;
    // width: 47px;
    background: #ed911f;
    border-radius: 0px 8px 8px 0;
    border: 1px solid #ed911f;
    // cursor: pointer;
  }
}

.deactive-item:hover {
  background: #f7f7f7;
  color: #ed911f !important;

}
</style>