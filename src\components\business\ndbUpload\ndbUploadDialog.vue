<template>
  <nd-dialog ref="uploadDialog" :title="title" width="800px" height="450px" append-to-body center :before-close="close">
    <div class="ndb-upload-dialog-box">
      <el-upload ref="upload" drag action="#" multiple :auto-upload="false" :headers="headers" :on-change="fileSelected" :http-request="pcUpload">
        <div class="upload-dialog-text1">根据需要拖动附件至本区域上传或点击下方按钮进行操作</div>
        <div class="upload-dialog-text3">
          {{ dialogText.text3 }}
        </div>
        <div class="upload-dialog-text3">
          {{ dialogText.text4 }}
        </div>
        <div class="upload-dialog-text4">
          <nd-button>本地上传</nd-button>
          <!-- <nd-button v-show="showPhoneUpload" @click.stop="openPhoneUploadDialog()"> 手机上传 </nd-button> -->
        </div>
      </el-upload>
      <div v-loading="loading" class="describe-loading">
        <div>
          <ul class="views-show-ul">
            <li
              v-for="(item, index) in fileList"
              :key="index"
              class="views-show-item"
              :class="!item.active ? 'views-show-item' : 'views-show-item-hover'"
              @mouseenter="mouseEnter(item)"
              @mouseleave="mouseLeave(item)"
            >
              <div class="left">
                <div class="views-show-item-num" v-text="index + 1" />
                <div class="views-show-item-img">
                  <el-image
                    v-if="item.fileSuffix != 'pdf' && item.fileSuffix != 'PDF' && item.fileSuffix != 'doc' && item.fileSuffix != 'docx'"
                    class="item-img-box"
                    style="width: 50px; height: 50px"
                    :src="item.fileUrl"
                    :preview-src-list="imagePreviewList"
                    @click="clickImage(item)"
                  />
                  <!-- 关联图片特有小标志 -->
                  <img v-if="item.isAssociated === '1'" src="./images/buleGL.png" class="item-img-gl" />
                  <div v-if="item.fileSuffix == 'pdf' || item.fileSuffix == 'PDF'" class="pdf-box" @click="openPDf(item.fileUrl)">
                    <img src="./images/pdf.png" class="item-img-box" style="width: 50px; height: 50px" alt="" />
                  </div>
                  <div v-if="item.fileSuffix == 'html'" class="pdf-box" @click="openPDf(item.fileUrl)">
                    <img src="./images/html.png" class="item-img-box" style="width: 50px; height: 50px" alt="" />
                  </div>
                  <div v-if="item.fileSuffix == 'doc' || item.fileSuffix == 'docx'" class="pdf-box" @click="openPDf(item.fileUrl)">
                    <img src="./images/word.png" class="item-img-box" style="width: 50px; height: 50px" alt="" />
                  </div>
                </div>
                <div class="views-show-item-name">
                  <div v-if="!item.changeName">
                    {{ item.curName }}
                  </div>
                  <div v-else class="views-show-item-name-input-box">
                    <nd-input ref="nameInput" v-model="item.curName" type="text" width="100%" class="views-show-item-name-input" />
                    <i class="el-icon-success" @click="nameInputClick(item)" />
                    <i class="el-icon-error" @click="nameInputClickQX(item)" />
                  </div>
                </div>
              </div>
              <div class="right">
                <div v-if="item.remark && item.remark !== ''" class="views-show-item-remark">
                  {{ item.remark }}
                </div>
                <div v-if="!item.active" class="views-show-item-settings-none" />
                <div v-if="item.active" class="views-show-item-settings">
                  <div v-if="isChangeName" class="settings-btn" @click="rename(item, index)">重命名</div>
                  <div v-if="isChangeOrder" class="settings-btn" @click="changeOrder(item, index)">调整顺序</div>
                  <div v-if="isNeedBillRecognition" class="settings-btn" @click="billRecognitionBtn(item.fileId)">识别票据</div>
                  <div class="settings-btn" v-if="isSz" @click="downLoadOneFile(item.fileId)">下载</div>
                  <div class="settings-btn" @click="deleteFile(index, item)">删除</div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <template #footer>
      <nd-button v-if="isNeedtrueClick" type="primary" @click="submitTrue()"> 确定 </nd-button>
      <nd-button v-if="isNeedClearAll" @click="clearAll()"> 全部清空 </nd-button>
      <nd-button @click="downLoadAllFile()" v-if="isSz" :disabled="fileList.length === 0"> 全部下载 </nd-button>
      <nd-button @click="close"> 关闭 </nd-button>
    </template>
    <ndb-phone ref="ndbPhone" @updateFile="updateFile" />
    <ndb-change-order
      ref="ndbChangeOrderRef"
      :is-sz="isSz"
      :isDocumentManagement="isDocumentManagement"
      :function-id="functionId"
      :data-id="dataId"
      :file-type-id="fileTypeId"
      @orderNumfuc="orderNumfuc"
    />
    <!-- 图片压缩（IE浏览器暂不做处理，建议使用chrome浏览器）-->
    <ndb-picture-compression ref="ndbPictureCompression" @getCompressFile="compressFileIE" />
  </nd-dialog>
</template>

<script>
import ndDialog from "@/components/ndDialog.vue";
import ndButton from "@/components/ndButton.vue";
import ndInput from "@/components/ndInput.vue";
import ndbPhone from "@/components/business/ndbUpload/ndbPhone.vue";
import ndbChangeOrder from "@/components/business/ndbUpload/ndbChangeOrder.vue";
import ndbPictureCompression from "@/components/business/ndbUpload/ndbPictureCompression.vue";
export default {
  components: {
    ndDialog,
    ndButton,
    ndInput,
    ndbChangeOrder,
    ndbPhone,
    ndbPictureCompression,
  },
  model: {
    prop: "vmData",
    event: "fileChange",
  },
  props: {
    // v-model数据
    vmData: {
      type: Array,
      default() {
        return [];
      },
    },
    // 是否需要上传pdf
    isNeedPDF: {
      type: Boolean,
      default: true,
    },
    // 所属模块id
    functionId: {
      type: String,
      default: "",
    },
    // 所属模块name
    functionName: {
      type: String,
      default: "",
    },
    // 批次id
    dataId: {
      type: String,
      default: "",
    },
    // 类型id
    fileTypeId: {
      type: String,
      default: "",
    },
    // 账套id
    ztId: {
      type: String,
      default: "",
    },
    // 服务器
    serverName: {
      type: String,
      default: "nd-oneThree",
    },
    // 是否显示手机上传按钮
    showPhoneUpload: {
      type: Boolean,
      default: true,
    },
    // 是否有重命名功能
    isChangeName: {
      type: Boolean,
      default: false,
    },
    // 是否有调整顺序功能
    isChangeOrder: {
      type: Boolean,
      default: false,
    },
    // 标题
    title: {
      type: String,
      default: "附件管理",
    },
    // 是否是三资
    isSz: {
      type: Boolean,
      default: false,
    },
    // 是否需要账套
    isZT: {
      type: String,
      default: "0",
    },
    // 是否需要全部清空
    isNeedClearAll: {
      type: Boolean,
      default: false,
    },
    // 是否是报账单据管理
    // isNeedDescribe: {
    isDocumentManagement: {
      type: Boolean,
      default: false,
    },
    // 是否需要确定按钮
    isNeedtrueClick: {
      type: Boolean,
      default: false,
    },
    // 三资前缀
    szUrl: {
      type: String,
      default: "/sz_product_new/",
    },
    // 不同的类型展示不同的提示语
    isPLSC: {
      type: String,
      default: "",
    },
    // 账套ID
    accountSetId: {
      type: String,
      default: "",
    },
    // 是否需要票据识别
    isNeedBillRecognition: {
      type: Boolean,
      default: false,
    },
    // 判断是哪个菜单进入
    menuEntry: {
      type: String,
      default: "",
    },
    // 手机二维码使用的模块类型
    modelType: {
      type: String,
      default: "",
    },
    // 附件是必填项模块才需要传过来的参数
    isFileRequired: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      newFile: [],
      newFile1: [],
      key: "0", //0没有经过放大缩小的旋转，1经过放大缩小的旋转
      fileId: "", //图片id图片旋转使用
      deg: 0, //图片旋转角度图片旋转用
      orderNumber: "", //点击每张图片的编号
      headers: {
        // 默认头部
        "Content-Type": "multipart/form-data",
      },
      fileList: [], // 文件列表
      uploadFileList: [], // 待上传的文件列表
      setTimeoutObject: null,
      describe: "", // 事项描述
      dialogText: {
        text3: "仅支持上传.jpg、.jpeg、.png、.pdf等图片格式的附件",
        text4: "",
      },
      loading: false, //加载动画
      moduleIdentifier: "", //是否是票据管理 不是0 是的话1
      fileSourceID: "",
      fileDoid: "",
      rotateImgtimer: null,
      rotateImgover: true,
    };
  },
  computed: {
    // 图片预览
    imagePreviewList() {
      var lists = [];
      if (Array.isArray(this.fileList)) {
        this.fileList.map((item, index) => {
          lists.push(item.fileUrl);
        });
      }
      return lists;
    },
  },
  watch: {
    vmData: {
      immediate: true,
      handler(newVal) {
        this.fileList = newVal;
      },
    },
    // 批次id
    dataId: {
      // immediate: true,
      handler() {
        this.getFiles();
      },
    },
  },
  mounted() {},
  methods: {
    // //获取服务器
    getServerPath() {
      if (typeof getContextPath === "function") {
        // console.log("获得服务器路径:" + getContextPath());
        return getContextPath();
      } else {
        return "/sz_product_new";
      }
    },
    //下载单个附件
    downLoadOneFile(id) {
      window.open(this.getServerPath() + "/apiFile/picDownloadUrl.do?id=" + id);
    },
    //下载全部附件
    downLoadAllFile() {
      if (this.fileList.length === 0) {
        this.$message.warning("暂无可下载附件");
        return;
      }
      this.getAllFileID().then((ids) => {
        // window.open(this.getServerPath() + '/apiFile/downLoadAllFiles.do?ids=' + ids)
        this.$ajax({
          url: "/apiFile/downLoadAllFiles.do",
          method: "post",
          data: { ids },
          responseType: "blob",
        }).then((res) => {
          if (!res) return;
          const blob = new Blob([res.data], {
            type: "application/zip",
          });
          if (window.navigator.msSaveOrOpenBlob) {
            //兼容IE10
            navigator.msSaveBlob(blob, "附件.zip");
          } else {
            const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
            const a = document.createElement("a"); //创建a标签
            a.style.display = "none";
            a.href = href; // 指定下载链接
            a.setAttribute("download", "附件.zip");
            a.click(); //触发下载
            URL.revokeObjectURL(a.href); //释放URL对象
          }
        });
      });
    },
    //获取所有附件的id
    getAllFileID() {
      if (this.fileList.length === 1) {
        this.downLoadOneFile(this.fileList[0].fileId);
        return;
      }
      return new Promise((reslove) => {
        let ids = [];
        this.fileList.forEach((element) => {
          ids.push(element.fileId);
        });
        reslove(ids);
      });
    },
    // 实现图片旋转保存
    clickImage(item) {
      this.orderNumber = Number(item.orderNumber);
      this.fileId = item.fileId;
      this.$nextTick(() => {
        let prever = document.getElementsByClassName("el-image-viewer__prev"); //获取前一张图片的dom实例对象
        let nexter = document.getElementsByClassName("el-image-viewer__next"); //获取后一张图片的dom实例对象
        let wrapper = document.getElementsByClassName("el-image-viewer__actions__inner"); //拿到下面一排按钮的dom实例对象
        let wrapperLeft = document.getElementsByClassName("el-icon-refresh-left"); //左旋按钮dom实例对象
        let wrapperRight = document.getElementsByClassName("el-icon-refresh-right"); //右旋按钮dom实例对象
        let wrapperClose = document.querySelector(".el-image-viewer__close"); //关闭按钮dom实例对象
        wrapperClose.addEventListener("click", this.hideCusBtnClose); //给关闭按钮添加点击事件，此时要刷新附件
        let wrapperMask = document.querySelector(".el-image-viewer__mask"); //遮罩层dom实例对象
        wrapperMask.addEventListener("click", this.hideCusBtnMask); //遮罩层添加点击事件，此时要刷新附件
        // let downImg = document.createElement('i');
        // downImg.setAttribute('class', 'el-icon-check');
        // wrapper[0].appendChild(downImg);//添加最后一排√按钮
        // console.log(nexter, 'nexternexternexternexter');
        // console.log(wrapper, 'wrapperwrapperwrapperwrapperwrapper');
        // console.log(wrapperLeft, 'wrapperLeftwrapperLeft');
        // console.log(wrapperRight, 'wrapperRightwrapperRight');
        // console.log(wrapperClose, 'wrapperClosewrapperClosewrapperClose');
        if (wrapper.length > 0) {
          // console.log('0000000000');
          this.wrapperElem = wrapper[0];
          // this.cusClickHandler();
        }
        // 上一张图片按钮
        if (prever.length > 0) {
          // console.log('1111111111');
          this.preverElem = prever[0];
          this.cusClickHandlerLeft();
        }
        // 下一张图片按钮
        if (nexter.length > 0) {
          // console.log('222222222222222');
          this.nexterElem = nexter[0];
          this.cusClickHandlerRight();
        }
        // 左旋按钮
        if (wrapperLeft.length > 0) {
          // console.log('33333');
          clearInterval(this.rotateImgtimer);
          this.rotateImgtimer = null;
          this.wrapperLeftElem = wrapperLeft[0];
          this.wrapperLeftElem.addEventListener("click", this.hideCusBtnWrapperLeft);
        }
        // 右旋按钮
        if (wrapperRight.length > 0) {
          // console.log('4444444444');
          clearInterval(this.rotateImgtimer);
          this.rotateImgtimer = null;
          this.wrapperRightElem = wrapperRight[0];
          this.wrapperRightElem.addEventListener("click", this.hideCusBtnWrapperRight);
        }
      });
    },
    //上一张图片按钮添加点击事件
    cusClickHandlerLeft() {
      this.preverElem.addEventListener("click", this.hideCusBtnLeft);
    },
    // 下一张图片按钮添加点击事件
    cusClickHandlerRight() {
      this.nexterElem.addEventListener("click", this.hideCusBtnRight);
    },
    // 逆时针旋转（向左旋转）-90度传给后端
    hideCusBtnWrapperLeft(e) {
      this.deg = Number(this.deg) + Number(-90);
      console.log(this.deg, "this.degthis.degthis.degthis.deg");
      // if (this.rotateImgover) {
      //   this.rotateImgover = false
      //   // this.rotateImg(this.deg)
      //   this.rotateImg()
      // }
    },
    // 顺时针旋转（向右旋转）90度传给后端
    hideCusBtnWrapperRight(e) {
      this.deg = Number(this.deg) + Number(90);
      console.log(this.deg, "this.degthis.degthis.degthis.deg");
      // if (this.rotateImgover) {
      //   this.rotateImgover = false
      //   // this.rotateImg(this.deg)
      //   this.rotateImg()
      // }
    },
    // 点击×按钮关闭遮罩层刷新附件
    hideCusBtnClose(e) {
      // this.rotateImgover = true
      if (this.deg % 360 === 0) {
        this.deg = Number(0);
        // setTimeout(function () {
        //   this.getFiles();
        // }, 2000);
      } else {
        this.rotateImg();
        this.deg = Number(0);
      }
    },
    // 点击其他区域关闭遮罩层刷新附件
    hideCusBtnMask(e) {
      // this.rotateImgover = true
      // console.log(1111);
      if (this.deg % 360 === 0) {
        this.deg = Number(0);
      } else {
        this.rotateImg();
        this.deg = Number(0);
      }
    },
    // 上一张图片按钮点击事件，拿到当前图片的fileId，并且要刷新附件
    hideCusBtnLeft(e) {
      // this.rotateImgover = true
      if (this.deg % 360 === 0) {
        this.deg = Number(0);
        if (this.orderNumber > 1) {
          this.orderNumber = this.orderNumber - 1;
        } else {
          this.orderNumber = this.fileList.length;
        }
        this.fileId = this.fileList[this.orderNumber - 1].fileId;
        // this.getFiles();
      } else {
        this.rotateImg();
        this.deg = Number(0);
        if (this.orderNumber > 1) {
          this.orderNumber = this.orderNumber - 1;
        } else {
          this.orderNumber = this.fileList.length;
        }
        this.fileId = this.fileList[this.orderNumber - 1].fileId;
        // this.getFiles();
      }
    },
    // 下一张图片按钮点击事件，拿到当前图片的fileId，并且要刷新附件
    hideCusBtnRight(e) {
      // this.rotateImgover = true
      if (this.deg % 360 === 0) {
        this.deg = Number(0);
        if (this.orderNumber > 1) {
          this.orderNumber = this.orderNumber - 1;
        } else {
          this.orderNumber = this.fileList.length;
        }
        this.fileId = this.fileList[this.orderNumber - 1].fileId;
        // this.getFiles();
      } else {
        this.rotateImg();
        this.deg = Number(0);
        if (this.orderNumber > 1) {
          this.orderNumber = this.orderNumber - 1;
        } else {
          this.orderNumber = this.fileList.length;
        }
        this.fileId = this.fileList[this.orderNumber - 1].fileId;
        // this.getFiles();
      }
    },
    // 旋转图片的方法
    rotateImg() {
      // this.rotateImgtimer = setTimeout(() => {

      // }, 2000);
      if (this.isDocumentManagement) {
        this.moduleIdentifier = 1;
      } else {
        this.moduleIdentifier = 0;
      }
      let params = {
        fileId: this.fileId,
        rotationAngle: this.deg,
        moduleIdentifier: this.moduleIdentifier,
      };
      // 请求后台旋转图片
      this.$ajax({
        url: "/szFileNew/rotatingImages.do",
        method: "POST",
        data: params,
      }).then((res) => {
        if (res.data.code === 0) {
          // this.rotateImgover = true;
          console.log("图片旋转保存成功");
          this.getFiles("notEmit");
          // this.$message({
          //   showClose: true,
          //   message: '图片旋转保存成功',
          //   type: 'success',
          // });
          // this.getFiles();
          // document.querySelector(".el-image-viewer__close").click()
          // this.key = '0';
          // clearInterval(this.rotateImgtimer);
          // this.rotateImgtimer = null;
        } else {
          // this.rotateImgover = true;
          // clearInterval(this.rotateImgtimer);
          // this.rotateImgtimer = null;
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: "warning",
          });
        }
      });
    },

    // 获得附件
    getFiles(val) {
      if (this.isSz) {
        if (this.isDocumentManagement) {
          this.$ajax({
            url: "/voucher_bill.do?method=getFiles",
            method: "post",
            params: {
              doId: this.dataId,
            },
          }).then((res) => {
            if (res.data.code === 0) {
              res.data.data.map((item) => {
                this.$set(item, "curName", item.FILE_NAME);
                this.$set(item, "fileId", item.ID);
                this.$set(item, "fileSuffix", item.FILE_fileSuffix_TYPE);
                this.$set(item, "filePath", item.fileAllPath);
                this.$set(item, "ORDERNUMBER", item.order_number);
              });
              this.fileList = res.data.data;
            }
          });
          return;
        }
        let params = {
          dataId: this.dataId,
          functionId: this.functionId,
          fileTypeId: this.fileTypeId,
        };
        this.$ajax({
          url: "/apiFile/getFileList.do",
          method: "post",
          data: params,
        }).then((res) => {
          if (res.data.code == 0) {
            this.fileList = res.data.data;
            this.fileList.map((item) => {
              item.changeName = false;
              this.$set(item, "filePath", item.filePath);
              this.$set(item, "curName", item.fileName);
              this.$set(item, "fileSuffix", item.filefileSuffix);
              this.$set(item, "ORDERNUMBER", item.orderNumber);
            });
            //jsp内附件回显
            if (typeof getFileList === "function") {
              getFileList(this.functionId, this.dataId, this.fileTypeId);
            }
            if (!val) {
              this.$emit("fileChange", this.fileList);
            }
            // console.log(this.fileList, 'this.fileList');
          }
        });
      } else {
        // let params = {
        //   doid: this.dataId,
        //   fzgs: this.fileTypeId,
        // };
        // this.$ajax({
        //   url: "/resources/findAll",
        //   method: "get",
        //   data: params,
        //   serverName: this.serverName,
        // }).then((res) => {
        //   this.fileList = res.data.data;
        // });
      }
    },
    // 打开手机上传附件对话框
    openPhoneUploadDialog() {
      this.$refs.ndbPhone.open(this.dataId, this.functionId, this.functionName, this.fileTypeId, this.isNeedPDF, this.isZT, this.szUrl, this.modelType);
    },
    //IE浏览器，图片不做压缩处理，直接返回上传
    compressFileIE(file) {
      clearTimeout(this.setTimeoutObject);
      // 构建数组
      this.uploadFileList.push(file);
      // console.log(this.uploadFileList, "==========上传图片的最终压缩后列表IE");
      this.setTimeoutObject = setTimeout(() => {
        this.pcUpload();
      }, 1000);
    },
    // 文件选择后
    fileSelected(file) {
      this.uploadFileList = [];
      // console.log(file, '====file');
      this.newFile.push(file);
      //打开加载动画
      this.loading = true;
      // 获取上传文件大小
      let imgSize = Number(file.size / 1024 / 1024);
      //获取文件格式
      let fileTypeLen = file.raw.type.split("/");
      let fileType = fileTypeLen[fileTypeLen.length - 1].toLowerCase(); //转化成小写
      if (this.isNeedPDF) {
        //有PDF上传的权限
        //判断所传的图片格式是上面这几种
        if (fileType === "jpg" || fileType === "jpeg" || fileType === "png" || fileType === "bmp" || fileType === "pdf") {
          if (fileType === "pdf") {
            //判断所传文件是否为pdf格式
            if (imgSize > 20) {
              this.$message.warning("PDF文件大小不能超过20MB，请重新上传");
              this.uploadFileList = [];
              clearTimeout(this.setTimeoutObject);
              //关闭加载动画
              this.loading = false;
              return;
            }
          } else {
            //图片格式
            if (imgSize > 50) {
              this.$message.warning("单张图片不能超过50MB，请重新上传");
              this.uploadFileList = [];
              clearTimeout(this.setTimeoutObject);
              //关闭加载动画
              this.loading = false;
              return;
            }
          }
        } else {
          //其他格式，给出提示
          this.$message.warning("仅支持上传*.jpg,*.png,*.bmp,*.jpeg或者*.pdf格式的附件");
          this.uploadFileList = [];
          clearTimeout(this.setTimeoutObject);
          //关闭加载动画
          this.loading = false;
          return;
        }
      } else if (this.serverName == "nd-village-ph") {
        if (fileType == "doc" || fileType == "docx" || fileType == "msword" || fileType == "vnd.openxmlformats-officedocument.wordprocessingml.document") {
          this.uploadFileList.push(file.raw);
          // console.log(this.uploadFileList, "==========上传图片的最终压缩后列表IE");
          this.setTimeoutObject = setTimeout(() => {
            this.pcUpload();
          }, 1000);
          // if(this.serverName == 'nd-village-ph'){
          // this.$ajax({
          //           url: "/contractSigning/uploadWord",
          //           method: "POST",
          //           data: file,
          //           serverName: "nd-village-ph",
          //         }).then((res) => {
          //           if (res.data.code == 200) {
          //             let obj = {}
          //             obj.sourcePath = res.data.data.sourcePath;
          //             obj.filePath = res.data.data.filePath;
          //             this.$emit("getPath", obj);
          //           } else {

          //           }
          //         }).catch(() => {
          //           this.$message.error("网络连接失败")
          //         })
          //     }
        } else {
          this.$message.warning("仅支持上传doc，docx类型的附件");
          this.uploadFileList = [];
          clearTimeout(this.setTimeoutObject);
          //关闭加载动画
          this.loading = false;
          return;
        }
      } else {
        //没有PDF上传的权限
        if (fileType === "pdf") {
          //判断所传文件是否为pdf格式
          //在没有PDF权限时，仍然上传了PDF格式，给出提示
          this.$message.warning("仅支持上传*.jpg,*.png,*.bmp,*.jpeg图片格式的附件");
          this.uploadFileList = [];
          clearTimeout(this.setTimeoutObject);
          //关闭加载动画
          this.loading = false;
          return;
        } else {
          //判断上传额图片格式是否正确
          if (fileType === "jpg" || fileType === "jpeg" || fileType === "png" || fileType === "bmp") {
            if (imgSize > 50) {
              this.$message.warning("单张图片不能超过50MB，请重新上传");
              this.uploadFileList = [];
              clearTimeout(this.setTimeoutObject);
              //关闭加载动画
              this.loading = false;
              return;
            }
          } else {
            this.$message.warning("仅支持上传*.jpg,*.png,*.bmp,*.jpeg图片格式的附件");
            this.uploadFileList = [];
            clearTimeout(this.setTimeoutObject);
            //关闭加载动画
            this.loading = false;
            return;
          }
        }
        this.uploadFileList = [];
      }

      //调用压缩图片组件
      this.$refs.ndbPictureCompression.beforcompressImg(file.raw).then(
        (fileNew) => {
          clearTimeout(this.setTimeoutObject);
          // 构建数组
          this.uploadFileList.push(fileNew);
          // console.log(this.uploadFileList, "==========上传图片的最终压缩后列表");
          this.setTimeoutObject = setTimeout(() => {
            this.pcUpload();
          }, 1000);
        },
        (reason) => {
          this.$message.warning("您所选择的文件未压缩完毕，请重新上传");
          this.uploadFileList = [];
          clearTimeout(this.setTimeoutObject);
          //关闭加载动画
          this.loading = false;
          return;
        }
      );
    },
    sort(prop) {
      return function (obj1, obj2) {
        var val1 = obj1[prop];
        var val2 = obj2[prop];
        if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
          val1 = Number(val1);
          val2 = Number(val2);
        }
        if (val1 < val2) {
          return -1;
        } else if (val1 > val2) {
          return 1;
        } else {
          return 0;
        }
      };
    },
    // 电脑上传
    pcUpload() {
      console.log(this.fileList,"this.fileList");
      this.uploadFileList.forEach((item) => {
        this.newFile.forEach((el, index) => {
          if (item.uid === el.uid) {
            let sortId = index;
            item.sortId = sortId;
          }
        });
      });
      this.uploadFileList = this.uploadFileList.sort(this.sort("sortId"));
      console.log(this.uploadFileList,"this.uploadFileList");
      
      if(this.fileList.length+this.uploadFileList.length>10){
        this.$message.warning('最多上传10个附件')
        //关闭加载动画
        this.loading = false;
        return
      }
      var params = new FormData();
      this.uploadFileList.map((item) => {
        params.append("files", item);
      });
      this.$ajax({
        // url: "/file/uploadFile?busId=" + this.dataId + "&configFileId=" + this.fileTypeId,
        url: "/web/uploadFile?busId=" + this.dataId,
        // url: "/file/uploadFile?busId=1789946172833124354&configFileId=4ea29a471e99477fad9dfb5397338691",
        method: "post",
        data: params,
        // 后端不要加token
        // headers: {
        //   token: "3255def0-402c-4af6-98b1-37b9ed696fce",
        // },
      }).then((res) => {
        if (res.data.code == 200) {
          this.$message.success("保存成功");
          this.fileList = res.data.data;
          this.$emit("fileChange", this.fileList);
        } else {
          this.$message.warning(res.data.message);
        }
        //关闭加载动画
        this.loading = false;
      });
      this.uploadFileList = [];
    },
    //保存村级报账单据后
    isChangeVillageReimbursement() {
      this.$emit("fileChange", this.fileList);
      this.submitTrueSave().then((res) => {
        if (typeof getFileListNew === "function") {
          getFileListNew(this.fileList);
        } else {
          this.$emit("beforeClose", this.fileList);
        }
      });
    },
    // 高拍仪上传
    metronomeUpload(object) {
      if (this.isSz) {
        let par = {
          dataId: this.dataId,
          functionId: this.functionId,
          fileTypeId: this.fileTypeId,
          fileName: object.fileName,
          img: object.file,
        };
        if (this.isDocumentManagement) {
          this.$set(par, "proofDocumentsOrOther", "1");
        }
        this.$ajax({
          url: "/szFileNew/scanningAttachment.do",
          method: "POST",
          data: par,
        }).then((res) => {
          if (res.data.code === 0) {
            this.$message.success("上传成功！");
            //组件内附件回显
            this.getFiles();
            //jsp内附件回显
            if (typeof getFileList === "function") {
              getFileList(this.functionId, this.dataId, this.fileTypeId);
            }
            this.$emit("fileChange", this.fileList);
            this.submitTrueSave().then((res) => {
              if (typeof getFileListNew === "function") {
                getFileListNew(this.fileList);
              } else {
                this.$emit("beforeClose", this.fileList);
              }
            });
          }
        });
      } else {
        var params = new FormData();
        params.append("fileName", object.fileName);
        params.append("base64", object.file);
        params.append("doid", this.dataId);
        params.append("fzgs", this.fileTypeId);
        params.append("orders", 0);
        this.$ajax({
          url: "/resources/base64/upload?dataId=" + this.dataId + "&fileTypeId=" + this.fileTypeId,
          method: "post",
          data: params,
          serverName: this.serverName,
        }).then((res) => {
          this.fileList = res.data.data;
          this.$emit("fileChange", this.fileList);
          this.submitTrueSave().then((res) => {
            if (typeof getFileListNew === "function") {
              getFileListNew(this.fileList);
            } else {
              this.$emit("beforeClose", this.fileList);
            }
          });
        });
      }
    },
    // 删除附件
    deleteFile(index, item) {
      if (this.isFileRequired) {
        if (this.fileList.length === 1) {
          this.$message.warning("至少保留一个附件！");
          return;
        }
      }

      let params = new FormData();
      params.append("id", item.id);
      this.$ajax({
        url: "/file/deleteFileById",
        method: "post",
        data: params,
        // 后端不要加token
        // headers: {
        //   token: "3255def0-402c-4af6-98b1-37b9ed696fce",
        // },
      }).then((res) => {
        this.$message.success("删除成功");
        this.fileList.splice(index, 1)
        // this.fileList = res.data.data;
        this.$emit("fileChange", this.fileList);
      });
    },
    // 预览pdf
    openPDf(url) {
      window.open(url);
    },
    // 鼠标移入
    mouseEnter(item) {
      this.$nextTick(() => {
        this.fileList.forEach((item) => {
          this.$set(item, "active", false);
        });
        this.$set(item, "active", true);
      });
    },
    // 鼠标移出
    mouseLeave(item) {
      this.$set(item, "active", false);
    },
    // 打开
    open(config) {
      this.newFile = [];
      this.newFile1 = [];
      // console.log(config, '==================>config');
      if (config) {
        if (config.isSz !== undefined) {
          this.isSz = config.isSz;
        }
        if (config.functionId !== undefined) {
          this.functionId = config.functionId;
        }
        if (config.functionName !== undefined) {
          this.functionName = config.functionName;
        }
        if (config.dataId !== undefined) {
          this.dataId = config.dataId;
        }
        if (config.fileTypeId !== undefined) {
          this.fileTypeId = config.fileTypeId;
        }
        if (config.flag !== undefined) {
          this.isZT = config.flag;
        }
        if (config.showPhoneUpload !== undefined) {
          this.showPhoneUpload = config.showPhoneUpload;
        }
        if (config.showMetronomeUpload !== undefined) {
          this.showMetronomeUpload = config.showMetronomeUpload;
        }
        if (config.showScannerUpload !== undefined) {
          this.showScannerUpload = config.showScannerUpload;
        }
        if (config.showVillageReimbursement !== undefined) {
          this.showVillageReimbursement = config.showVillageReimbursement;
        }
        if (config.isChangeOrder !== undefined) {
          this.isChangeOrder = config.isChangeOrder;
        }
        if (config.isChangeName !== undefined) {
          this.isChangeName = config.isChangeName;
        }
        if (config.isNeedClearAll !== undefined) {
          this.isNeedClearAll = config.isNeedClearAll;
        }
        if (config.isDocumentManagement !== undefined) {
          this.isDocumentManagement = config.isDocumentManagement;
        }
        if (config.isNeedtrueClick !== undefined) {
          this.isNeedtrueClick = config.isNeedtrueClick;
        }
        if (config.szUrl !== undefined) {
          this.szUrl = config.szUrl;
        }
        if (config.accountSetId !== undefined) {
          this.accountSetId = config.accountSetId;
        }
        if (config.modelType !== undefined) {
          this.modelType = config.modelType;
        }
        if (config.isFileRequired !== undefined) {
          this.isFileRequired = config.isFileRequired;
        }
      }
      if (this.isNeedPDF && !this.isSz) {
        this.dialogText.text3 = "仅支持上传.jpg、.jpeg、.png、.pdf等图片格式的附件";
      } else if (!this.isNeedPDF && !this.isSz) {
        this.dialogText.text3 = "仅支持上传.jpg、.jpeg、.png等图片格式的附件";
      } else if (this.isSz && this.isNeedPDF) {
        this.dialogText.text3 = "支持批量上传jpg、png、bmp、jpeg等图片及pdf文件，单张图片最大不得超过50M，支持图片无损压缩，单个PDF文件最大不得超过20M";
      } else if (this.isSz && !this.isNeedPDF) {
        this.dialogText.text3 = "支持批量上传jpg、png、bmp、jpeg等图片，单张图片最大不得超过50M，支持图片无损压缩";
      } else {
        this.dialogText.text3 = "仅支持上传.jpg、.jpeg、.png等图片格式的附件";
      }
      if (this.isPLSC === "土地经营流转") {
        this.dialogText.text4 = "为保证上传文件能够和对应导入记录对应,批量上传的文件名需以流转合同编号+#命名,多文件在#后加数字,不可重复";
      } else if (this.isPLSC === "权证登记") {
        this.dialogText.text4 = "为保证上传文件能够和对应导入记录对应,批量上传的文件名需以承包合同编号+#命名,多文件在#后加数字,不可重复";
      } else {
        this.dialogText.text4 = "";
      }
      if (this.serverName == "nd-village-ph") {
        this.dialogText.text3 = "仅支持上传doc，docx类型的附件";
      }

      this.$nextTick(() => {
        if (config && config.isSz) {
          this.getFiles();
        }
      });
      this.$refs.uploadDialog.open();
    },
    // 关闭
    close() {
      this.newFile = [];
      this.newFile1 = [];
      this.deg = Number(0);
      this.$emit("beforeClose", this.fileList);
      this.$refs.uploadDialog.close();
    },
    // 重命名
    rename(item, index) {
      if (this.isSz) {
        item.oldName = item.curName;
        // this.$set(item, 'oldName', item.curName);
        this.fileList.forEach((itemall) => {
          itemall.changeName = false;
          // this.$set(itemall, 'changeName', false);
          // console.log(
          //   itemall.changeName === false && itemall.oldName != undefined
          // );
          if (itemall.changeName === false && itemall.oldName != undefined) {
            itemall.curName = itemall.oldName;
          }
        });
        // this.$set(item, 'changeName', true);
        item.changeName = true;

        let endindex;
        let endStr = item.curName;
        let fileSuffix = "." + item.fileSuffix;
        for (let i = item.curName.length - fileSuffix.length; i > 0; i--) {
          if (item.curName.substr(i, fileSuffix.length) === fileSuffix) {
            endindex = i;
            endStr = endStr.slice(0, endindex);
            break;
          }
        }
        item.curName = endStr;
        this.$nextTick(() => {
          this.$refs.nameInput[0].setCursorPos(0, endindex);
        });
      }
    },
    nameInputClick(item) {
      if (
        item.curName.indexOf(".png") === -1 &&
        item.curName.indexOf(".jpg") === -1 &&
        item.curName.indexOf(".PNG") === -1 &&
        item.curName.indexOf(".JPG") === -1 &&
        item.curName.indexOf(".pdf") === -1 &&
        item.curName.indexOf(".PDF") === -1 &&
        item.curName.indexOf(".jepg") === -1 &&
        item.curName.indexOf(".JEPG") === -1
      ) {
        item.curName = item.curName + "." + item.fileSuffix;
      }
      if (this.isSz) {
        if (this.isDocumentManagement) {
          let paramDescribe = {
            doId: this.dataId,
            id: item.fileId,
            fileName: item.curName,
          };
          this.$ajax({
            url: "/voucher_bill.do?method=updateFileName",
            method: "post",
            params: paramDescribe,
          }).then((res) => {
            if (res.data.code === 0) {
              this.$message.success("重命名成功");
              res.data.data.files.map((item) => {
                this.$set(item, "curName", item.FILE_NAME);
                this.$set(item, "fileId", item.ID);
                this.$set(item, "fileSuffix", item.FILE_fileSuffix_TYPE);
                this.$set(item, "filePath", item.fileAllPath);
              });
              this.fileList = res.data.data.files;
            } else {
              this.$message.error("重命名失败，请重试");
            }
          });
          return true;
        }
        let params = {
          id: item.fileId,
          fileName: item.curName,
          fileTypeId: this.fileTypeId,
        };
        this.$ajax({
          url: "/szFileNew.do?method=updateFileName",
          method: "post",
          params: params,
        }).then((res) => {
          if (res.data.code === 0) {
            this.$message.success("重命名成功");
            this.$set(item, "changeName", false);
            if (typeof getFileList === "function") {
              getFileList(this.functionId, this.dataId, this.fileTypeId);
            }
            res.data.data.files.map((item) => {
              this.$set(item, "curName", item.FILENAME + item.fileTrueType);
              this.$set(item, "fileId", item.ID);
              this.$set(item, "fileSuffix", item.FILEfileSuffix);
              this.$set(item, "filePath", item.filePath);
            });
            this.fileList = res.data.data.files;
            this.$emit("fileChange", this.fileList);
          } else {
            this.$message.error("重命名失败，请重试");
            item.curName = item.oldName;
          }
        });
      }
    },
    nameInputClickQX(item) {
      item.curName = item.oldName;
      this.$set(item, "changeName", false);
    },
    // 调整顺序
    changeOrder(item, index) {
      this.$refs.ndbChangeOrderRef.open(item, index);
    },
    //调准顺序后排序
    orderNumfuc(data) {
      if (data.length !== 0) {
        this.fileList = data;
        if (typeof getFileList === "function") {
          getFileList(this.functionId, this.dataId, this.fileTypeId);
        }
        this.$emit("fileChange", this.fileList);
      }
    },
    //清空全部
    clearAll() {
      this.$confirm("确定将附件全部清空吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if (this.fileList.length === 0) {
            this.$message.warning("没有需清空的附件");
            return false;
          }
          if (this.isSz) {
            if (this.isDocumentManagement) {
              let paramDescribe = {
                // doId: this.accountSetId,
                doId: this.dataId,
                type: 3,
              };
              this.$ajax({
                url: "/apiPz/deleteFileUrlAll.do",
                method: "post",
                data: paramDescribe,
              }).then((res) => {
                if (res.data.code === 0) {
                  this.$message.success("删除成功");
                  this.fileList = [];
                } else {
                  this.$message.error("删除失败，请重试");
                }
              });
              return true;
            }
            let cleanAllFilesUrl = "/apiOcr/cleanAllFiles.do";
            if (this.isFileRequired) {
              cleanAllFilesUrl = "/apiOcr/cleanAllFilesSet.do";
            }
            // console.log(this.isFileRequired,"cleanAllFilesUrlcleanAllFilesUrlcleanAllFilesUrl");
            let params = {
              doId: this.dataId,
              ztQy: "0",
            };
            this.$ajax({
              url: cleanAllFilesUrl,
              method: "POST",
              data: params,
            }).then(async (res) => {
              if (res.data.code === 0) {
                // 组件内附件清空
                if (this.isFileRequired) {
                  await this.getFiles();
                } else {
                  this.fileList = [];
                }
                await this.$emit("fileChange", this.fileList);
                // jsp内附件回显
                if (typeof getFileList === "function") {
                  await getFileList(this.functionId, this.dataId, this.fileTypeId);
                }
              }
            });
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    //保存btn
    submitTrueSave() {
      return new Promise((reslove) => {
        if (this.menuEntry === "pzxg" || this.menuEntry == "凭证修改") {
          let params = {
            pzId: this.dataId,
          };
          this.$ajax({
            url: "/pingzgl/updatePzFjNumNew.do",
            method: "post",
            params: params,
          }).then((res) => {
            if (res.data.code === 0) {
              // this.$message.success("保存成功")
              // this.close()
              reslove(true);
            } else {
              this.$message.error(res.data.msg);
              reslove(true);
            }
          });
        } else {
          reslove(true);
        }
      });
    },
    //确定btn事件
    submitTrue() {
      //事项描述功能提交
      if (this.isDocumentManagement) {
        // if (this.describe === "") {
        //   this.$message.warning("请填写事项描述！");
        //   return false;
        // }
        if (this.fileList.length === 0) {
          this.$message.warning("请先上传附件！");
          return false;
        }
        if (this.isSz) {
          // let paramDescribe = {
          //   remark: this.describe
          // }
          var paramDescribe = new FormData();
          paramDescribe.append("remark", this.describe);
          this.$ajax({
            url: "/voucher_bill.do?method=saveOrUpRemark",
            method: "post",
            data: paramDescribe,
          }).then((res) => {
            if (res.data.code === 0) {
              this.$message.success("上传成功");
              this.close();
              searchqq();
            } else {
              this.$message.error("上传失败，请重试");
            }
          });
          return true;
        }
      }
    },
    // 手机上传回显
    updateFile() {
      this.getFiles();
      // this.submitTrueSave();
    },
    //票据识别
    billRecognitionBtn(id) {
      this.$refs.ndbBillRecognitionRef.open(this.dataId, id, this.menuEntry);
    },
  },
};
</script>

<style lang="scss" scoped>
.ndb-upload-dialog-box {
  width: auto;
  height: auto;

  .upload-dialog-text1 {
    font-size: 18px;
    color: #555555;
    margin-bottom: 8px;
  }

  .upload-dialog-text2 {
    font-size: 16px;
    color: #777777;
    margin-bottom: 8px;
  }

  .upload-dialog-text3 {
    font-size: 12px;
    color: #777777;
    margin-bottom: 8px;
  }

  .upload-dialog-text4 {
    width: 100%;
    height: auto;
  }

  .describe-loading {
    width: auto;
    height: auto;
  }

  .describe-box {
    display: flex;
    padding: 0 8px;
    align-items: center;
    margin-bottom: 8px;

    .describe-box-name {
      min-width: 75px;

      span {
        color: red;
      }
    }
  }
}

.views-show-item-num {
  font-size: 15px;
  padding-left: 6px;
  margin-right: 6px;
}

#views-show {
  padding-top: 5px;
  height: 270px;
  overflow-y: auto;
}

#views-show-describe {
  padding-top: 5px;
  height: 225px;
  overflow-y: auto;
}

.views-show-ul {
  margin: 0 8px;

  .views-show-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;

    .left {
      // background-color: salmon;
      display: flex;
      align-items: center;
      width: 67%;

      .views-show-item-num {
        min-width: 24px;
      }

      .views-show-item-img {
        width: 70px;
        position: relative;

        .item-img-box {
          width: 50px;
          height: 50px;
          border: 1px solid #b6c9e7;
          cursor: pointer;
        }

        .item-img-gl {
          position: absolute;
          width: 15px;
          height: 15px;
          top: -3px;
          right: 5px;
        }
      }

      .views-show-item-name {
        width: 100%;
        min-width: 400px;
        cursor: pointer;

        .views-show-item-name-input {
          width: auto;
          outline: none;
        }
      }

      .views-show-item-name-input-box {
        display: flex;
        align-items: center;
      }
    }

    .right {
      // background-color: greenyellow;
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
      width: 33%;
      padding-right: 8px;

      .views-show-item-remark {
        width: auto;
        margin-right: 20px;
      }

      .views-show-item-settings-none {
        width: 50px;
      }

      .views-show-item-settings {
        min-width: 50px;
        display: flex;
        justify-content: space-between;

        .settings-btn {
          margin-left: 10px;
          color: #0098ff;
          cursor: pointer;
        }
      }
    }
  }

  .views-show-item-hover {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    background-color: #f6faff;

    .left {
      // background-color: salmon;
      display: flex;
      align-items: center;
      width: 67%;

      .views-show-item-num {
        min-width: 24px;
      }

      .views-show-item-img {
        width: 70px;
        position: relative;

        .item-img-box {
          width: 50px;
          height: 50px;
          border: 1px solid #b6c9e7;
          cursor: pointer;
        }

        .item-img-gl {
          position: absolute;
          width: 15px;
          height: 15px;
          top: -3px;
          right: 5px;
        }
      }

      .views-show-item-name {
        width: 100%;
        min-width: 400px;
        cursor: pointer;

        .views-show-item-name-input {
          width: auto;
          outline: none;
        }
      }

      .views-show-item-name-input-box {
        display: flex;
        align-items: center;
      }
    }

    .right {
      // background-color: greenyellow;
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
      width: 33%;
      padding-right: 8px;

      .views-show-item-remark {
        width: auto;
        margin-right: 20px;
      }

      .views-show-item-settings-none {
        width: 50px;
      }

      .views-show-item-settings {
        min-width: 50px;
        display: flex;
        justify-content: space-between;

        .settings-btn {
          margin-left: 10px;
          color: #0098ff;
          cursor: pointer;
        }
      }
    }
  }
}

// ::v-deep .el-upload{
//   width: 98%;
// }

::v-deep .el-upload-dragger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-width: 745px;
  margin: 8px;
  height: 160px;
  border: 1px dashed #dce5f3;
  background-color: #f4f9ff;
  border-radius: 0px;
}

::v-deep .el-upload-list {
  display: none;
}

.el-icon-success {
  margin-left: 5px;
  font-size: 20px;
  color: green;
}

.el-icon-error {
  margin-left: 5px;
  font-size: 20px;
  color: red;
}
</style>
