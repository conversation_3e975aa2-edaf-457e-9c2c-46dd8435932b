<template>
  <div class="policies">
    <div class="title">
      <div class="title-text">政策法规</div>
      <div class="box-title-right" @click="toMore">
        <span>MORE</span>
        <img class="address-img" src="@/assets/projectImg/right2.png" alt="" />
      </div>
    </div>
    <tab
      :tabList="['国家政策', '地方政策', '交易规则', '交易指南']"
      v-model="tabActive"
      style="margin-bottom: 27px"
      @tabClick="getDatas"
    />

    <div class="main">
      <div class="item" v-for="(item, index) in newList" :key="index"  @click="toDetail(item.path)">
        <div class="title">
          {{ item.title }}
        </div>
        <div class="text">
          {{ item.zy }}
        </div>
        <div class="time">
          <div>{{ item.time }}</div>
          <div>查看详情 →</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import tab from './tabBar.vue';
export default {
  components: {
    tab,
  },
  watch: {
    tabActive: {
      handler() {
        // console.log(this.tabActive);
      },
    },
  },
  data() {
    return {
      tabActive: 0,
      newList: [],
    };
  },
  mounted() {
    this.getDatas();
  },
  methods: {
    // 获取新闻数据
    getDatas() {
      let url = '';
      switch (this.tabActive) {
        case 0: {
          url = '/zcfg/gjzc/';
          break;
        }

        case 1: {
          url = '/zcfg/dfzc/';
          break;
        }

        case 2: {
          url = '/zcfg/jygz/';
          break;
        }

        case 3: {
          url = '/zcfg/jyzn/';
          break;
        }
      }
      this.$ajax({
        url: url,
        method: 'get',
        serverName: 'nd-ss',
      }).then((r) => {
        let newArr = eval(r.data);
        if (newArr.length > 5) {
          this.newList = newArr.slice(0, 6);
        } else {
          this.newList = newArr;
        }
      });
    },

    // 点击去往详情
    toDetail(path) {
      this.$router.push({ path: 'listDetailView', query: { path: path, bannerType: 4 } });
    },

    // 点击more
    toMore() {
      this.$router.push('regulationsListView');
    },
  },
};
</script>

<style lang="scss" scoped>
.policies {
  width: 1300px;
  margin: auto;
  padding: 40px 0 20px 0;

  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 17px;
    user-select: none;

    .title-text {
      font-size: 36px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #333333;
      line-height: 1;
    }

    .box-title-right {
      width: 78px;
      height: 38px;
      line-height: 38px;
      text-align: center;
      border: 1px solid #cccccc;
      font-size: 14px;
      color: #999999;
      cursor: pointer;
      position: relative;
      margin-right: 20px;

      .address-img {
        width: 32px;
        height: 7px;
        position: absolute;
        top: 13px;
        right: -22px;
        z-index: 99999;
      }
    }
  }

  .main {
    display: flex;
    flex-wrap: wrap;

    .item {
      position: relative;
      width: 640px;
      background: #ffffff;
      margin-bottom: 20px;
      padding: 20px;
      cursor: pointer;
      transition: 0.3s;
      top: 0;

      &:hover {
        top: -10px;
        box-shadow: 0 4px 10px #ccc;
        transition: 0.3s;
      }

      &:nth-of-type(2n + 1) {
        margin-right: 20px;
      }

      .title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 18px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #333333;
        line-height: 1;
        margin-bottom: 15px;
      }

      .text {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666666;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        margin-bottom: 15px;
        height: 35px;
      }

      .time {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #666666;

        > div:nth-of-type(2) {
          cursor: pointer;
          user-select: none;
        }
      }
    }
  }
}
</style>