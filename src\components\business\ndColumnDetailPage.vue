<template>
  <div class="container">
    <div class="breadcrumbs-box">
      <div class="breadcrumbs">
        <div class="icon"><i class="el-icon-location-outline" /></div>
        <div v-for="(item, index) in breadcrumbItems" :key="index" @click="goBack(index)">
          <span :style="index == breadcrumbItems.length - 1 ? 'color:#ED911F;' : 'cursor:pointer'">
            {{ item }}
          </span>
          <span v-if="index != breadcrumbItems.length - 1">&nbsp;>&nbsp;</span>
        </div>
      </div>
    </div>

    <!-- 文章详情 -->
    <div v-if="detailData.title">
      <div class="concent-bk">
        <div class="list_ntitle">{{ detailData.title }}</div>
        <div class="title">发布时间：{{ detailData.writeTime }}</div>
        <div class="hr"></div>
        <div class="ql-snow">
          <div class="main-content ql-editor" v-html="detailData.manuscriptContent"></div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else class="loading">
      <i class="el-icon-loading"></i>
      加载中...
    </div>
  </div>
</template>

<script>
export default {
  name: 'ndColumnDetailPage',
  props: {
    id: {
      type: String,
      required: true
    },
    breadcrumbName: {
      type: String,
      default: '详情'
    },
    fromPage: {
      type: String,
      default: ''
    },
    parentBreadcrumb: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      detailData: {
        title: '',
        subtitle: '',
        writer: '',
        source: '',
        writeTime: '',
        manuscriptContent: ''
      }
    };
  },
  computed: {
    breadcrumbItems() {
      const items = [];

      // 处理面包屑名称 - 支持任意层级深度
      if (this.breadcrumbName && this.breadcrumbName !== '详情') {
        // 如果 breadcrumbName 包含 '>'，说明是多层级路径，直接使用所有层级
        if (this.breadcrumbName.includes(' > ')) {
          const pathItems = this.breadcrumbName.split(' > ').filter(item => item.trim());
          items.push(...pathItems);
        } else {
          // 单层级，需要添加父级
          const parentMap = {
            'indexView': '首页',
            'newsCenter': '新闻中心',
            'businessRule': '业务规则',
            'lawsRegulation': '政策法规',
            'profileDownload': '资料下载',
            'aboutUs': '关于我们',
            'interacteCommunication': '互动交流',
            'index': '首页',
            'home': '首页',
            '': '首页',
            null: '首页',
            undefined: '首页'
          };

          const parentName = parentMap[this.fromPage] || this.parentBreadcrumb;
          if (parentName) {
            items.push(parentName);
          }
          items.push(this.breadcrumbName);
        }
      } else {
        // 如果没有 breadcrumbName，使用默认父级
        const parentMap = {
          'indexView': '首页',
          'newsCenter': '新闻中心',
          'businessRule': '业务规则',
          'lawsRegulation': '政策法规',
          'profileDownload': '资料下载',
          'aboutUs': '关于我们',
          'interacteCommunication': '互动交流',
          'index': '首页',
          'home': '首页',
          '': '首页',
          null: '首页',
          undefined: '首页'
        };

        const parentName = parentMap[this.fromPage] || this.parentBreadcrumb;
        if (parentName) {
          items.push(parentName);
        }
      }

      return items;
    }
  },
  watch: {
    // 监听id prop变化，重新获取数据
    id: {
      handler(newId) {
        if (newId) {
          console.log('详情页ID变化:', newId);
          this.getDetailData();
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 由于添加了watch，这里可以移除，但保留作为备用
    // this.getDetailData();
  },
  methods: {
    // 获取详情数据
    getDetailData() {
      this.$ajax({
        url: '/jcms/site/manuscript/getDetailById',
        method: 'get',
        serverName: 'nd-ss',
        data: {
          id: this.id
        }
      }).then((res) => {
        if (res.data.code === 200) {
          this.detailData = res.data.data;
        }
      }).catch(error => {
        console.error('获取详情数据失败:', error);
      });
    },

    // 返回列表
    goBack(index) {
      if (index === 0) {
        this.$router.go(-1);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import url('@/assets/css/vue-quill.snow.css');

.breadcrumbs-box {
  padding-left: calc((100% - 1200px) / 2);
  background-color: #f8f8f8;
}

.container {
  border-top: 1px solid #cbcbcb;
  background: #fff;

  .main-content {
    width: 100%;
    min-height: 300px;
    background: #fff;
    margin-top: 20px;
    overflow: hidden;
    padding-top: 10px;
  }
}

.breadcrumbs {
  height: 60px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin: 0 auto;

  .icon {
    color: #ED911F;
    margin-right: 8px;
  }
}

.concent-bk {
  width: 1200px;
  margin: 20px auto;
  border: 1px solid #e8e8e8;
  background: #fff;
  padding: 10px;

  .list_ntitle {
    font-size: 24px;
    font-weight: bold;
    color: #151515;
    line-height: 55px;
    text-align: center;
  }

  .title {
    text-align: center;
    line-height: 25px;
    font-size: 16px;
    color: #000;
  }

  .hr {
    width: 1140px;
    margin: 0 auto;
    height: 2px;
    margin-top: 12px;
    background: #dddddd;
  }
}

.loading {
  text-align: center;
  padding: 50px;
  color: #666;

  i {
    font-size: 24px;
    margin-right: 10px;
  }
}
</style>